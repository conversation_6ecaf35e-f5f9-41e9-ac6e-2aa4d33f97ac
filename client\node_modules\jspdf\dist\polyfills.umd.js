/** @license
 *
 * jsPDF - PDF Document creation from JavaScript
 * Version 2.5.2 Built on 2024-09-17T13:29:57.860Z
 *                      CommitID 00000000
 *
 * Copyright (c) 2010-2021 <PERSON> <<EMAIL>>, https://github.com/MrRio/jsPDF
 *               2015-2021 yWorks GmbH, http://www.yworks.com
 *               2015-2021 <PERSON><PERSON> <<EMAIL>>, https://github.com/HackbrettXXX
 *               2016-2018 <PERSON><PERSON> <<EMAIL>>
 *               2010 <PERSON>, https://github.com/acspike
 *               2012 Willow Systems Corporation, https://github.com/willowsystems
 *               2012 <PERSON>, https://github.com/pablohess
 *               2012 <PERSON><PERSON><PERSON>, https://github.com/fjenett
 *               2013 <PERSON>, https://github.com/warrenweckesser
 *               2013 Youssef <PERSON>, https://github.com/lifof
 *               2013 <PERSON>, https://github.com/lsdriscoll
 *               2013 <PERSON>, https://github.com/stefslon
 *               2013 <PERSON>, https://github.com/jmorel
 *               2013 <PERSON>, https://github.com/chris-rock
 *               2014 Juan <PERSON> Gaviria, https://github.com/juanpgaviria
 *               2014 James Makes, https://github.com/dollaruw
 *               2014 Diego Casorran, https://github.com/diegocr
 *               2014 Steven Spungin, https://github.com/Flamenco
 *               2014 Kenneth Glassey, https://github.com/Gavvers
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * Contributor(s):
 *    siefkenj, ahwolf, rickygu, Midnith, saintclair, eaparango,
 *    kim3er, mfo, alnorth, Flamenco
 */

/**
 * Copyright (c) 2014-2023 Denis Pushkarev
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,r){return t(r={exports:{}},r.exports),r.exports}var e,n,o=function(t){return t&&t.Math===Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof t&&t)||function(){return this}()||t||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},u=!a((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),c=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),f=Function.prototype.call,s=c?f.bind(f):function(){return f.apply(f,arguments)},l={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,y={f:p&&!l.call({1:2},1)?function(t){var r=p(this,t);return!!r&&r.enumerable}:l},h=function(t,r){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:r}},d=Function.prototype,v=d.call,g=c&&d.bind.bind(v,v),b=c?g:function(t){return function(){return v.apply(t,arguments)}},w=b({}.toString),m=b("".slice),A=function(t){return m(w(t),8,-1)},T=Object,E=b("".split),O=a((function(){return!T("z").propertyIsEnumerable(0)}))?function(t){return"String"===A(t)?E(t,""):T(t)}:T,S=function(t){return null==t},j=TypeError,R=function(t){if(S(t))throw new j("Can't call method on "+t);return t},M=function(t){return O(R(t))},x="object"==typeof document&&document.all,I={all:x,IS_HTMLDDA:void 0===x&&void 0!==x},P=I.all,L=I.IS_HTMLDDA?function(t){return"function"==typeof t||t===P}:function(t){return"function"==typeof t},C=I.all,_=I.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:L(t)||t===C}:function(t){return"object"==typeof t?null!==t:L(t)},B=function(t){return L(t)?t:void 0},F=function(t,r){return arguments.length<2?B(i[t]):i[t]&&i[t][r]},U=b({}.isPrototypeOf),D="undefined"!=typeof navigator&&String(navigator.userAgent)||"",N=i.process,k=i.Deno,W=N&&N.versions||k&&k.version,V=W&&W.v8;V&&(n=(e=V.split("."))[0]>0&&e[0]<4?1:+(e[0]+e[1])),!n&&D&&(!(e=D.match(/Edge\/(\d+)/))||e[1]>=74)&&(e=D.match(/Chrome\/(\d+)/))&&(n=+e[1]);var Y=n,G=i.String,z=!!Object.getOwnPropertySymbols&&!a((function(){var t=Symbol("symbol detection");return!G(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),q=z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,H=Object,X=q?function(t){return"symbol"==typeof t}:function(t){var r=F("Symbol");return L(r)&&U(r.prototype,H(t))},K=String,J=function(t){try{return K(t)}catch(t){return"Object"}},$=TypeError,Q=function(t){if(L(t))return t;throw new $(J(t)+" is not a function")},Z=function(t,r){var e=t[r];return S(e)?void 0:Q(e)},tt=TypeError,rt=Object.defineProperty,et=function(t,r){try{rt(i,t,{value:r,configurable:!0,writable:!0})}catch(e){i[t]=r}return r},nt=i["__core-js_shared__"]||et("__core-js_shared__",{}),ot=r((function(t){(t.exports=function(t,r){return nt[t]||(nt[t]=void 0!==r?r:{})})("versions",[]).push({version:"3.33.0",mode:"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.0/LICENSE",source:"https://github.com/zloirock/core-js"})})),it=Object,at=function(t){return it(R(t))},ut=b({}.hasOwnProperty),ct=Object.hasOwn||function(t,r){return ut(at(t),r)},ft=0,st=Math.random(),lt=b(1..toString),pt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+lt(++ft+st,36)},yt=i.Symbol,ht=ot("wks"),dt=q?yt.for||yt:yt&&yt.withoutSetter||pt,vt=function(t){return ct(ht,t)||(ht[t]=z&&ct(yt,t)?yt[t]:dt("Symbol."+t)),ht[t]},gt=TypeError,bt=vt("toPrimitive"),wt=function(t,r){if(!_(t)||X(t))return t;var e,n=Z(t,bt);if(n){if(void 0===r&&(r="default"),e=s(n,t,r),!_(e)||X(e))return e;throw new gt("Can't convert object to primitive value")}return void 0===r&&(r="number"),function(t,r){var e,n;if("string"===r&&L(e=t.toString)&&!_(n=s(e,t)))return n;if(L(e=t.valueOf)&&!_(n=s(e,t)))return n;if("string"!==r&&L(e=t.toString)&&!_(n=s(e,t)))return n;throw new tt("Can't convert object to primitive value")}(t,r)},mt=function(t){var r=wt(t,"string");return X(r)?r:r+""},At=i.document,Tt=_(At)&&_(At.createElement),Et=function(t){return Tt?At.createElement(t):{}},Ot=!u&&!a((function(){return 7!==Object.defineProperty(Et("div"),"a",{get:function(){return 7}}).a})),St=Object.getOwnPropertyDescriptor,jt={f:u?St:function(t,r){if(t=M(t),r=mt(r),Ot)try{return St(t,r)}catch(t){}if(ct(t,r))return h(!s(y.f,t,r),t[r])}},Rt=u&&a((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Mt=String,xt=TypeError,It=function(t){if(_(t))return t;throw new xt(Mt(t)+" is not an object")},Pt=TypeError,Lt=Object.defineProperty,Ct=Object.getOwnPropertyDescriptor,_t={f:u?Rt?function(t,r,e){if(It(t),r=mt(r),It(e),"function"==typeof t&&"prototype"===r&&"value"in e&&"writable"in e&&!e.writable){var n=Ct(t,r);n&&n.writable&&(t[r]=e.value,e={configurable:"configurable"in e?e.configurable:n.configurable,enumerable:"enumerable"in e?e.enumerable:n.enumerable,writable:!1})}return Lt(t,r,e)}:Lt:function(t,r,e){if(It(t),r=mt(r),It(e),Ot)try{return Lt(t,r,e)}catch(t){}if("get"in e||"set"in e)throw new Pt("Accessors not supported");return"value"in e&&(t[r]=e.value),t}},Bt=u?function(t,r,e){return _t.f(t,r,h(1,e))}:function(t,r,e){return t[r]=e,t},Ft=Function.prototype,Ut=u&&Object.getOwnPropertyDescriptor,Dt=ct(Ft,"name"),Nt={EXISTS:Dt,PROPER:Dt&&"something"===function(){}.name,CONFIGURABLE:Dt&&(!u||u&&Ut(Ft,"name").configurable)},kt=b(Function.toString);L(nt.inspectSource)||(nt.inspectSource=function(t){return kt(t)});var Wt,Vt,Yt,Gt=nt.inspectSource,zt=i.WeakMap,qt=L(zt)&&/native code/.test(String(zt)),Ht=ot("keys"),Xt=function(t){return Ht[t]||(Ht[t]=pt(t))},Kt={},Jt=i.TypeError,$t=i.WeakMap;if(qt||nt.state){var Qt=nt.state||(nt.state=new $t);Qt.get=Qt.get,Qt.has=Qt.has,Qt.set=Qt.set,Wt=function(t,r){if(Qt.has(t))throw new Jt("Object already initialized");return r.facade=t,Qt.set(t,r),r},Vt=function(t){return Qt.get(t)||{}},Yt=function(t){return Qt.has(t)}}else{var Zt=Xt("state");Kt[Zt]=!0,Wt=function(t,r){if(ct(t,Zt))throw new Jt("Object already initialized");return r.facade=t,Bt(t,Zt,r),r},Vt=function(t){return ct(t,Zt)?t[Zt]:{}},Yt=function(t){return ct(t,Zt)}}var tr,rr={set:Wt,get:Vt,has:Yt,enforce:function(t){return Yt(t)?Vt(t):Wt(t,{})},getterFor:function(t){return function(r){var e;if(!_(r)||(e=Vt(r)).type!==t)throw new Jt("Incompatible receiver, "+t+" required");return e}}},er=r((function(t){var r=Nt.CONFIGURABLE,e=rr.enforce,n=rr.get,o=String,i=Object.defineProperty,c=b("".slice),f=b("".replace),s=b([].join),l=u&&!a((function(){return 8!==i((function(){}),"length",{value:8}).length})),p=String(String).split("String"),y=t.exports=function(t,n,a){"Symbol("===c(o(n),0,7)&&(n="["+f(o(n),/^Symbol\(([^)]*)\)/,"$1")+"]"),a&&a.getter&&(n="get "+n),a&&a.setter&&(n="set "+n),(!ct(t,"name")||r&&t.name!==n)&&(u?i(t,"name",{value:n,configurable:!0}):t.name=n),l&&a&&ct(a,"arity")&&t.length!==a.arity&&i(t,"length",{value:a.arity});try{a&&ct(a,"constructor")&&a.constructor?u&&i(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var y=e(t);return ct(y,"source")||(y.source=s(p,"string"==typeof n?n:"")),t};Function.prototype.toString=y((function(){return L(this)&&n(this).source||Gt(this)}),"toString")})),nr=function(t,r,e,n){n||(n={});var o=n.enumerable,i=void 0!==n.name?n.name:r;if(L(e)&&er(e,i,n),n.global)o?t[r]=e:et(r,e);else{try{n.unsafe?t[r]&&(o=!0):delete t[r]}catch(t){}o?t[r]=e:_t.f(t,r,{value:e,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},or=Math.ceil,ir=Math.floor,ar=Math.trunc||function(t){var r=+t;return(r>0?ir:or)(r)},ur=function(t){var r=+t;return r!=r||0===r?0:ar(r)},cr=Math.max,fr=Math.min,sr=function(t,r){var e=ur(t);return e<0?cr(e+r,0):fr(e,r)},lr=Math.min,pr=function(t){return t>0?lr(ur(t),9007199254740991):0},yr=function(t){return pr(t.length)},hr=function(t){return function(r,e,n){var o,i=M(r),a=yr(i),u=sr(n,a);if(t&&e!=e){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===e)return t||u||0;return!t&&-1}},dr={includes:hr(!0),indexOf:hr(!1)},vr=dr.indexOf,gr=b([].push),br=function(t,r){var e,n=M(t),o=0,i=[];for(e in n)!ct(Kt,e)&&ct(n,e)&&gr(i,e);for(;r.length>o;)ct(n,e=r[o++])&&(~vr(i,e)||gr(i,e));return i},wr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],mr=wr.concat("length","prototype"),Ar={f:Object.getOwnPropertyNames||function(t){return br(t,mr)}},Tr={f:Object.getOwnPropertySymbols},Er=b([].concat),Or=F("Reflect","ownKeys")||function(t){var r=Ar.f(It(t)),e=Tr.f;return e?Er(r,e(t)):r},Sr=function(t,r,e){for(var n=Or(r),o=_t.f,i=jt.f,a=0;a<n.length;a++){var u=n[a];ct(t,u)||e&&ct(e,u)||o(t,u,i(r,u))}},jr=/#|\.prototype\./,Rr=function(t,r){var e=xr[Mr(t)];return e===Pr||e!==Ir&&(L(r)?a(r):!!r)},Mr=Rr.normalize=function(t){return String(t).replace(jr,".").toLowerCase()},xr=Rr.data={},Ir=Rr.NATIVE="N",Pr=Rr.POLYFILL="P",Lr=Rr,Cr=jt.f,_r=function(t,r){var e,n,o,a,u,c=t.target,f=t.global,s=t.stat;if(e=f?i:s?i[c]||et(c,{}):(i[c]||{}).prototype)for(n in r){if(a=r[n],o=t.dontCallGetSet?(u=Cr(e,n))&&u.value:e[n],!Lr(f?n:c+(s?".":"#")+n,t.forced)&&void 0!==o){if(typeof a==typeof o)continue;Sr(a,o)}(t.sham||o&&o.sham)&&Bt(a,"sham",!0),nr(e,n,a,t)}},Br=!a((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Fr=Xt("IE_PROTO"),Ur=Object,Dr=Ur.prototype,Nr=Br?Ur.getPrototypeOf:function(t){var r=at(t);if(ct(r,Fr))return r[Fr];var e=r.constructor;return L(e)&&r instanceof e?e.prototype:r instanceof Ur?Dr:null},kr=String,Wr=TypeError,Vr=Object.setPrototypeOf||("__proto__"in{}?function(){var t,r=!1,e={};try{(t=function(t,r,e){try{return b(Q(Object.getOwnPropertyDescriptor(t,r)[e]))}catch(t){}}(Object.prototype,"__proto__","set"))(e,[]),r=e instanceof Array}catch(t){}return function(e,n){return It(e),function(t){if("object"==typeof t||L(t))return t;throw new Wr("Can't set "+kr(t)+" as a prototype")}(n),r?t(e,n):e.__proto__=n,e}}():void 0),Yr=Object.keys||function(t){return br(t,wr)},Gr={f:u&&!Rt?Object.defineProperties:function(t,r){It(t);for(var e,n=M(r),o=Yr(r),i=o.length,a=0;i>a;)_t.f(t,e=o[a++],n[e]);return t}},zr=F("document","documentElement"),qr=Xt("IE_PROTO"),Hr=function(){},Xr=function(t){return"<script>"+t+"<\/script>"},Kr=function(t){t.write(Xr("")),t.close();var r=t.parentWindow.Object;return t=null,r},Jr=function(){try{tr=new ActiveXObject("htmlfile")}catch(t){}var t,r;Jr="undefined"!=typeof document?document.domain&&tr?Kr(tr):((r=Et("iframe")).style.display="none",zr.appendChild(r),r.src=String("javascript:"),(t=r.contentWindow.document).open(),t.write(Xr("document.F=Object")),t.close(),t.F):Kr(tr);for(var e=wr.length;e--;)delete Jr.prototype[wr[e]];return Jr()};Kt[qr]=!0;var $r=Object.create||function(t,r){var e;return null!==t?(Hr.prototype=It(t),e=new Hr,Hr.prototype=null,e[qr]=t):e=Jr(),void 0===r?e:Gr.f(e,r)},Qr=function(t,r){_(r)&&"cause"in r&&Bt(t,"cause",r.cause)},Zr=Error,te=b("".replace),re=String(new Zr("zxcasd").stack),ee=/\n\s*at [^:]*:[^\n]*/,ne=ee.test(re),oe=!a((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",h(1,7)),7!==t.stack)})),ie=Error.captureStackTrace,ae=function(t,r,e,n){oe&&(ie?ie(t,r):Bt(t,"stack",function(t,r){if(ne&&"string"==typeof t&&!Zr.prepareStackTrace)for(;r--;)t=te(t,ee,"");return t}(e,n)))},ue=function(t){if("Function"===A(t))return b(t)},ce=ue(ue.bind),fe=function(t,r){return Q(t),void 0===r?t:c?ce(t,r):function(){return t.apply(r,arguments)}},se={},le=vt("iterator"),pe=Array.prototype,ye=function(t){return void 0!==t&&(se.Array===t||pe[le]===t)},he={};he[vt("toStringTag")]="z";var de="[object z]"===String(he),ve=vt("toStringTag"),ge=Object,be="Arguments"===A(function(){return arguments}()),we=de?A:function(t){var r,e,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,r){try{return t[r]}catch(t){}}(r=ge(t),ve))?e:be?A(r):"Object"===(n=A(r))&&L(r.callee)?"Arguments":n},me=vt("iterator"),Ae=function(t){if(!S(t))return Z(t,me)||Z(t,"@@iterator")||se[we(t)]},Te=TypeError,Ee=function(t,r){var e=arguments.length<2?Ae(t):r;if(Q(e))return It(s(e,t));throw new Te(J(t)+" is not iterable")},Oe=function(t,r,e){var n,o;It(t);try{if(!(n=Z(t,"return"))){if("throw"===r)throw e;return e}n=s(n,t)}catch(t){o=!0,n=t}if("throw"===r)throw e;if(o)throw n;return It(n),e},Se=TypeError,je=function(t,r){this.stopped=t,this.result=r},Re=je.prototype,Me=function(t,r,e){var n,o,i,a,u,c,f,l=e&&e.that,p=!(!e||!e.AS_ENTRIES),y=!(!e||!e.IS_RECORD),h=!(!e||!e.IS_ITERATOR),d=!(!e||!e.INTERRUPTED),v=fe(r,l),g=function(t){return n&&Oe(n,"normal",t),new je(!0,t)},b=function(t){return p?(It(t),d?v(t[0],t[1],g):v(t[0],t[1])):d?v(t,g):v(t)};if(y)n=t.iterator;else if(h)n=t;else{if(!(o=Ae(t)))throw new Se(J(t)+" is not iterable");if(ye(o)){for(i=0,a=yr(t);a>i;i++)if((u=b(t[i]))&&U(Re,u))return u;return new je(!1)}n=Ee(t,o)}for(c=y?t.next:n.next;!(f=s(c,n)).done;){try{u=b(f.value)}catch(t){Oe(n,"throw",t)}if("object"==typeof u&&u&&U(Re,u))return u}return new je(!1)},xe=String,Ie=function(t){if("Symbol"===we(t))throw new TypeError("Cannot convert a Symbol value to a string");return xe(t)},Pe=function(t,r){return void 0===t?arguments.length<2?"":r:Ie(t)},Le=vt("toStringTag"),Ce=Error,_e=[].push,Be=function(t,r){var e,n=U(Fe,this);Vr?e=Vr(new Ce,n?Nr(this):Fe):(e=n?this:$r(Fe),Bt(e,Le,"Error")),void 0!==r&&Bt(e,"message",Pe(r)),ae(e,Be,e.stack,1),arguments.length>2&&Qr(e,arguments[2]);var o=[];return Me(t,_e,{that:o}),Bt(e,"errors",o),e};Vr?Vr(Be,Ce):Sr(Be,Ce,{name:!0});var Fe=Be.prototype=$r(Ce.prototype,{constructor:h(1,Be),message:h(1,""),name:h(1,"AggregateError")});_r({global:!0,constructor:!0,arity:2},{AggregateError:Be});var Ue=_t.f,De=vt("unscopables"),Ne=Array.prototype;void 0===Ne[De]&&Ue(Ne,De,{configurable:!0,value:$r(null)});var ke,We,Ve,Ye=function(t){Ne[De][t]=!0},Ge=vt("iterator"),ze=!1;[].keys&&("next"in(Ve=[].keys())?(We=Nr(Nr(Ve)))!==Object.prototype&&(ke=We):ze=!0),(!_(ke)||a((function(){var t={};return ke[Ge].call(t)!==t})))&&(ke={}),L(ke[Ge])||nr(ke,Ge,(function(){return this}));var qe={IteratorPrototype:ke,BUGGY_SAFARI_ITERATORS:ze},He=_t.f,Xe=vt("toStringTag"),Ke=function(t,r,e){t&&!e&&(t=t.prototype),t&&!ct(t,Xe)&&He(t,Xe,{configurable:!0,value:r})},Je=qe.IteratorPrototype,$e=function(){return this},Qe=Nt.PROPER,Ze=Nt.CONFIGURABLE,tn=qe.IteratorPrototype,rn=qe.BUGGY_SAFARI_ITERATORS,en=vt("iterator"),nn=function(){return this},on=function(t,r,e,n,o,i,a){!function(t,r,e,n){var o=r+" Iterator";t.prototype=$r(Je,{next:h(+!n,e)}),Ke(t,o,!1),se[o]=$e}(e,r,n);var u,c,f,l=function(t){if(t===o&&g)return g;if(!rn&&t&&t in d)return d[t];switch(t){case"keys":case"values":case"entries":return function(){return new e(this,t)}}return function(){return new e(this)}},p=r+" Iterator",y=!1,d=t.prototype,v=d[en]||d["@@iterator"]||o&&d[o],g=!rn&&v||l(o),b="Array"===r&&d.entries||v;if(b&&(u=Nr(b.call(new t)))!==Object.prototype&&u.next&&(Nr(u)!==tn&&(Vr?Vr(u,tn):L(u[en])||nr(u,en,nn)),Ke(u,p,!0)),Qe&&"values"===o&&v&&"values"!==v.name&&(Ze?Bt(d,"name","values"):(y=!0,g=function(){return s(v,this)})),o)if(c={values:l("values"),keys:i?g:l("keys"),entries:l("entries")},a)for(f in c)(rn||y||!(f in d))&&nr(d,f,c[f]);else _r({target:r,proto:!0,forced:rn||y},c);return d[en]!==g&&nr(d,en,g,{name:o}),se[r]=g,c},an=function(t,r){return{value:t,done:r}},un=_t.f,cn=rr.set,fn=rr.getterFor("Array Iterator"),sn=on(Array,"Array",(function(t,r){cn(this,{type:"Array Iterator",target:M(t),index:0,kind:r})}),(function(){var t=fn(this),r=t.target,e=t.kind,n=t.index++;if(!r||n>=r.length)return t.target=void 0,an(void 0,!0);switch(e){case"keys":return an(n,!1);case"values":return an(r[n],!1)}return an([n,r[n]],!1)}),"values"),ln=se.Arguments=se.Array;if(Ye("keys"),Ye("values"),Ye("entries"),u&&"values"!==ln.name)try{un(ln,"name",{value:"values"})}catch(t){}var pn=de?{}.toString:function(){return"[object "+we(this)+"]"};de||nr(Object.prototype,"toString",pn,{unsafe:!0});var yn="process"===A(i.process),hn=function(t,r,e){return e.get&&er(e.get,r,{getter:!0}),e.set&&er(e.set,r,{setter:!0}),_t.f(t,r,e)},dn=vt("species"),vn=function(t){var r=F(t);u&&r&&!r[dn]&&hn(r,dn,{configurable:!0,get:function(){return this}})},gn=TypeError,bn=function(t,r){if(U(r,t))return t;throw new gn("Incorrect invocation")},wn=function(){},mn=[],An=F("Reflect","construct"),Tn=/^\s*(?:class|function)\b/,En=b(Tn.exec),On=!Tn.test(wn),Sn=function(t){if(!L(t))return!1;try{return An(wn,mn,t),!0}catch(t){return!1}},jn=function(t){if(!L(t))return!1;switch(we(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return On||!!En(Tn,Gt(t))}catch(t){return!0}};jn.sham=!0;var Rn,Mn,xn,In,Pn=!An||a((function(){var t;return Sn(Sn.call)||!Sn(Object)||!Sn((function(){t=!0}))||t}))?jn:Sn,Ln=TypeError,Cn=function(t){if(Pn(t))return t;throw new Ln(J(t)+" is not a constructor")},_n=vt("species"),Bn=function(t,r){var e,n=It(t).constructor;return void 0===n||S(e=It(n)[_n])?r:Cn(e)},Fn=Function.prototype,Un=Fn.apply,Dn=Fn.call,Nn="object"==typeof Reflect&&Reflect.apply||(c?Dn.bind(Un):function(){return Dn.apply(Un,arguments)}),kn=b([].slice),Wn=TypeError,Vn=function(t,r){if(t<r)throw new Wn("Not enough arguments");return t},Yn=/(?:ipad|iphone|ipod).*applewebkit/i.test(D),Gn=i.setImmediate,zn=i.clearImmediate,qn=i.process,Hn=i.Dispatch,Xn=i.Function,Kn=i.MessageChannel,Jn=i.String,$n=0,Qn={};a((function(){Rn=i.location}));var Zn=function(t){if(ct(Qn,t)){var r=Qn[t];delete Qn[t],r()}},to=function(t){return function(){Zn(t)}},ro=function(t){Zn(t.data)},eo=function(t){i.postMessage(Jn(t),Rn.protocol+"//"+Rn.host)};Gn&&zn||(Gn=function(t){Vn(arguments.length,1);var r=L(t)?t:Xn(t),e=kn(arguments,1);return Qn[++$n]=function(){Nn(r,void 0,e)},Mn($n),$n},zn=function(t){delete Qn[t]},yn?Mn=function(t){qn.nextTick(to(t))}:Hn&&Hn.now?Mn=function(t){Hn.now(to(t))}:Kn&&!Yn?(In=(xn=new Kn).port2,xn.port1.onmessage=ro,Mn=fe(In.postMessage,In)):i.addEventListener&&L(i.postMessage)&&!i.importScripts&&Rn&&"file:"!==Rn.protocol&&!a(eo)?(Mn=eo,i.addEventListener("message",ro,!1)):Mn="onreadystatechange"in Et("script")?function(t){zr.appendChild(Et("script")).onreadystatechange=function(){zr.removeChild(this),Zn(t)}}:function(t){setTimeout(to(t),0)});var no={set:Gn,clear:zn},oo=function(){this.head=null,this.tail=null};oo.prototype={add:function(t){var r={item:t,next:null},e=this.tail;e?e.next=r:this.head=r,this.tail=r},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}};var io,ao,uo,co,fo,so=oo,lo=/ipad|iphone|ipod/i.test(D)&&"undefined"!=typeof Pebble,po=/web0s(?!.*chrome)/i.test(D),yo=jt.f,ho=no.set,vo=i.MutationObserver||i.WebKitMutationObserver,go=i.document,bo=i.process,wo=i.Promise,mo=yo(i,"queueMicrotask"),Ao=mo&&mo.value;if(!Ao){var To=new so,Eo=function(){var t,r;for(yn&&(t=bo.domain)&&t.exit();r=To.get();)try{r()}catch(t){throw To.head&&io(),t}t&&t.enter()};Yn||yn||po||!vo||!go?!lo&&wo&&wo.resolve?((co=wo.resolve(void 0)).constructor=wo,fo=fe(co.then,co),io=function(){fo(Eo)}):yn?io=function(){bo.nextTick(Eo)}:(ho=fe(ho,i),io=function(){ho(Eo)}):(ao=!0,uo=go.createTextNode(""),new vo(Eo).observe(uo,{characterData:!0}),io=function(){uo.data=ao=!ao}),Ao=function(t){To.head||io(),To.add(t)}}var Oo,So,jo,Ro=Ao,Mo=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},xo=i.Promise,Io="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Po=!Io&&!yn&&"object"==typeof window&&"object"==typeof document,Lo=(xo&&xo.prototype,vt("species")),Co=!1,_o=L(i.PromiseRejectionEvent),Bo={CONSTRUCTOR:Lr("Promise",(function(){var t=Gt(xo),r=t!==String(xo);if(!r&&66===Y)return!0;if(!Y||Y<51||!/native code/.test(t)){var e=new xo((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((e.constructor={})[Lo]=n,!(Co=e.then((function(){}))instanceof n))return!0}return!r&&(Po||Io)&&!_o})),REJECTION_EVENT:_o,SUBCLASSING:Co},Fo=TypeError,Uo=function(t){var r,e;this.promise=new t((function(t,n){if(void 0!==r||void 0!==e)throw new Fo("Bad Promise constructor");r=t,e=n})),this.resolve=Q(r),this.reject=Q(e)},Do={f:function(t){return new Uo(t)}},No=no.set,ko=Bo.CONSTRUCTOR,Wo=Bo.REJECTION_EVENT,Vo=Bo.SUBCLASSING,Yo=rr.getterFor("Promise"),Go=rr.set,zo=xo&&xo.prototype,qo=xo,Ho=zo,Xo=i.TypeError,Ko=i.document,Jo=i.process,$o=Do.f,Qo=$o,Zo=!!(Ko&&Ko.createEvent&&i.dispatchEvent),ti=function(t){var r;return!(!_(t)||!L(r=t.then))&&r},ri=function(t,r){var e,n,o,i=r.value,a=1===r.state,u=a?t.ok:t.fail,c=t.resolve,f=t.reject,l=t.domain;try{u?(a||(2===r.rejection&&ai(r),r.rejection=1),!0===u?e=i:(l&&l.enter(),e=u(i),l&&(l.exit(),o=!0)),e===t.promise?f(new Xo("Promise-chain cycle")):(n=ti(e))?s(n,e,c,f):c(e)):f(i)}catch(t){l&&!o&&l.exit(),f(t)}},ei=function(t,r){t.notified||(t.notified=!0,Ro((function(){for(var e,n=t.reactions;e=n.get();)ri(e,t);t.notified=!1,r&&!t.rejection&&oi(t)})))},ni=function(t,r,e){var n,o;Zo?((n=Ko.createEvent("Event")).promise=r,n.reason=e,n.initEvent(t,!1,!0),i.dispatchEvent(n)):n={promise:r,reason:e},!Wo&&(o=i["on"+t])?o(n):"unhandledrejection"===t&&function(t,r){try{1===arguments.length?console.error(t):console.error(t,r)}catch(t){}}("Unhandled promise rejection",e)},oi=function(t){s(No,i,(function(){var r,e=t.facade,n=t.value;if(ii(t)&&(r=Mo((function(){yn?Jo.emit("unhandledRejection",n,e):ni("unhandledrejection",e,n)})),t.rejection=yn||ii(t)?2:1,r.error))throw r.value}))},ii=function(t){return 1!==t.rejection&&!t.parent},ai=function(t){s(No,i,(function(){var r=t.facade;yn?Jo.emit("rejectionHandled",r):ni("rejectionhandled",r,t.value)}))},ui=function(t,r,e){return function(n){t(r,n,e)}},ci=function(t,r,e){t.done||(t.done=!0,e&&(t=e),t.value=r,t.state=2,ei(t,!0))},fi=function(t,r,e){if(!t.done){t.done=!0,e&&(t=e);try{if(t.facade===r)throw new Xo("Promise can't be resolved itself");var n=ti(r);n?Ro((function(){var e={done:!1};try{s(n,r,ui(fi,e,t),ui(ci,e,t))}catch(r){ci(e,r,t)}})):(t.value=r,t.state=1,ei(t,!1))}catch(r){ci({done:!1},r,t)}}};if(ko&&(Ho=(qo=function(t){bn(this,Ho),Q(t),s(Oo,this);var r=Yo(this);try{t(ui(fi,r),ui(ci,r))}catch(t){ci(r,t)}}).prototype,(Oo=function(t){Go(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new so,rejection:!1,state:0,value:void 0})}).prototype=nr(Ho,"then",(function(t,r){var e=Yo(this),n=$o(Bn(this,qo));return e.parent=!0,n.ok=!L(t)||t,n.fail=L(r)&&r,n.domain=yn?Jo.domain:void 0,0===e.state?e.reactions.add(n):Ro((function(){ri(n,e)})),n.promise})),So=function(){var t=new Oo,r=Yo(t);this.promise=t,this.resolve=ui(fi,r),this.reject=ui(ci,r)},Do.f=$o=function(t){return t===qo||void 0===t?new So(t):Qo(t)},L(xo)&&zo!==Object.prototype)){jo=zo.then,Vo||nr(zo,"then",(function(t,r){var e=this;return new qo((function(t,r){s(jo,e,t,r)})).then(t,r)}),{unsafe:!0});try{delete zo.constructor}catch(t){}Vr&&Vr(zo,Ho)}_r({global:!0,constructor:!0,wrap:!0,forced:ko},{Promise:qo}),Ke(qo,"Promise",!1),vn("Promise");var si=vt("iterator"),li=!1;try{var pi=0,yi={next:function(){return{done:!!pi++}},return:function(){li=!0}};yi[si]=function(){return this},Array.from(yi,(function(){throw 2}))}catch(t){}var hi=function(t,r){try{if(!r&&!li)return!1}catch(t){return!1}var e=!1;try{var n={};n[si]=function(){return{next:function(){return{done:e=!0}}}},t(n)}catch(t){}return e},di=Bo.CONSTRUCTOR||!hi((function(t){xo.all(t).then(void 0,(function(){}))}));_r({target:"Promise",stat:!0,forced:di},{all:function(t){var r=this,e=Do.f(r),n=e.resolve,o=e.reject,i=Mo((function(){var e=Q(r.resolve),i=[],a=0,u=1;Me(t,(function(t){var c=a++,f=!1;u++,s(e,r,t).then((function(t){f||(f=!0,i[c]=t,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),e.promise}});var vi=Bo.CONSTRUCTOR,gi=xo&&xo.prototype;if(_r({target:"Promise",proto:!0,forced:vi,real:!0},{catch:function(t){return this.then(void 0,t)}}),L(xo)){var bi=F("Promise").prototype.catch;gi.catch!==bi&&nr(gi,"catch",bi,{unsafe:!0})}_r({target:"Promise",stat:!0,forced:di},{race:function(t){var r=this,e=Do.f(r),n=e.reject,o=Mo((function(){var o=Q(r.resolve);Me(t,(function(t){s(o,r,t).then(e.resolve,n)}))}));return o.error&&n(o.value),e.promise}}),_r({target:"Promise",stat:!0,forced:Bo.CONSTRUCTOR},{reject:function(t){var r=Do.f(this);return s(r.reject,void 0,t),r.promise}});var wi=function(t,r){if(It(t),_(r)&&r.constructor===t)return r;var e=Do.f(t);return(0,e.resolve)(r),e.promise},mi=Bo.CONSTRUCTOR;F("Promise");_r({target:"Promise",stat:!0,forced:mi},{resolve:function(t){return wi(this,t)}}),_r({target:"Promise",stat:!0,forced:di},{allSettled:function(t){var r=this,e=Do.f(r),n=e.resolve,o=e.reject,i=Mo((function(){var e=Q(r.resolve),o=[],i=0,a=1;Me(t,(function(t){var u=i++,c=!1;a++,s(e,r,t).then((function(t){c||(c=!0,o[u]={status:"fulfilled",value:t},--a||n(o))}),(function(t){c||(c=!0,o[u]={status:"rejected",reason:t},--a||n(o))}))})),--a||n(o)}));return i.error&&o(i.value),e.promise}});_r({target:"Promise",stat:!0,forced:di},{any:function(t){var r=this,e=F("AggregateError"),n=Do.f(r),o=n.resolve,i=n.reject,a=Mo((function(){var n=Q(r.resolve),a=[],u=0,c=1,f=!1;Me(t,(function(t){var l=u++,p=!1;c++,s(n,r,t).then((function(t){p||f||(f=!0,o(t))}),(function(t){p||f||(p=!0,a[l]=t,--c||i(new e(a,"No one promise resolved")))}))})),--c||i(new e(a,"No one promise resolved"))}));return a.error&&i(a.value),n.promise}});var Ai=xo&&xo.prototype,Ti=!!xo&&a((function(){Ai.finally.call({then:function(){}},(function(){}))}));if(_r({target:"Promise",proto:!0,real:!0,forced:Ti},{finally:function(t){var r=Bn(this,F("Promise")),e=L(t);return this.then(e?function(e){return wi(r,t()).then((function(){return e}))}:t,e?function(e){return wi(r,t()).then((function(){throw e}))}:t)}}),L(xo)){var Ei=F("Promise").prototype.finally;Ai.finally!==Ei&&nr(Ai,"finally",Ei,{unsafe:!0})}var Oi=b("".charAt),Si=b("".charCodeAt),ji=b("".slice),Ri=function(t){return function(r,e){var n,o,i=Ie(R(r)),a=ur(e),u=i.length;return a<0||a>=u?t?"":void 0:(n=Si(i,a))<55296||n>56319||a+1===u||(o=Si(i,a+1))<56320||o>57343?t?Oi(i,a):n:t?ji(i,a,a+2):o-56320+(n-55296<<10)+65536}},Mi={codeAt:Ri(!1),charAt:Ri(!0)}.charAt,xi=rr.set,Ii=rr.getterFor("String Iterator");on(String,"String",(function(t){xi(this,{type:"String Iterator",string:Ie(t),index:0})}),(function(){var t,r=Ii(this),e=r.string,n=r.index;return n>=e.length?an(void 0,!0):(t=Mi(e,n),r.index+=t.length,an(t,!1))}));var Pi,Li=i,Ci=(Li.Promise,Array.isArray||function(t){return"Array"===A(t)}),_i=vt("species"),Bi=Array,Fi=function(t,r){return new(function(t){var r;return Ci(t)&&(r=t.constructor,(Pn(r)&&(r===Bi||Ci(r.prototype))||_(r)&&null===(r=r[_i]))&&(r=void 0)),void 0===r?Bi:r}(t))(0===r?0:r)},Ui=b([].push),Di=function(t){var r=1===t,e=2===t,n=3===t,o=4===t,i=6===t,a=7===t,u=5===t||i;return function(c,f,s,l){for(var p,y,h=at(c),d=O(h),v=fe(f,s),g=yr(d),b=0,w=l||Fi,m=r?w(c,g):e||a?w(c,0):void 0;g>b;b++)if((u||b in d)&&(y=v(p=d[b],b,h),t))if(r)m[b]=y;else if(y)switch(t){case 3:return!0;case 5:return p;case 6:return b;case 2:Ui(m,p)}else switch(t){case 4:return!1;case 7:Ui(m,p)}return i?-1:n||o?o:m}},Ni={forEach:Di(0),map:Di(1),filter:Di(2),some:Di(3),every:Di(4),find:Di(5),findIndex:Di(6),filterReject:Di(7)},ki=vt("species"),Wi=Ni.map,Vi=(Pi="map",Y>=51||!a((function(){var t=[];return(t.constructor={})[ki]=function(){return{foo:1}},1!==t[Pi](Boolean).foo})));_r({target:"Array",proto:!0,forced:!Vi},{map:function(t){return Wi(this,t,arguments.length>1?arguments[1]:void 0)}});var Yi=function(t,r){return b(i[t].prototype[r])};Yi("Array","map");_r({target:"Array",stat:!0},{isArray:Ci});Li.Array.isArray;var Gi=TypeError,zi=function(t){return function(r,e,n,o){Q(e);var i=at(r),a=O(i),u=yr(i),c=t?u-1:0,f=t?-1:1;if(n<2)for(;;){if(c in a){o=a[c],c+=f;break}if(c+=f,t?c<0:u<=c)throw new Gi("Reduce of empty array with no initial value")}for(;t?c>=0:u>c;c+=f)c in a&&(o=e(o,a[c],c,i));return o}},qi={left:zi(!1),right:zi(!0)},Hi=function(t,r){var e=[][t];return!!e&&a((function(){e.call(null,r||function(){return 1},1)}))},Xi=qi.left,Ki=!yn&&Y>79&&Y<83||!Hi("reduce");_r({target:"Array",proto:!0,forced:Ki},{reduce:function(t){var r=arguments.length;return Xi(this,t,r,r>1?arguments[1]:void 0)}});Yi("Array","reduce");var Ji=Ni.forEach,$i=Hi("forEach")?[].forEach:function(t){return Ji(this,t,arguments.length>1?arguments[1]:void 0)};_r({target:"Array",proto:!0,forced:[].forEach!==$i},{forEach:$i});Yi("Array","forEach");var Qi=Ni.find,Zi=!0;"find"in[]&&Array(1).find((function(){Zi=!1})),_r({target:"Array",proto:!0,forced:Zi},{find:function(t){return Qi(this,t,arguments.length>1?arguments[1]:void 0)}}),Ye("find");Yi("Array","find");_r({target:"Object",stat:!0,sham:!u},{create:$r});Li.Object;var ta=a((function(){Yr(1)}));_r({target:"Object",stat:!0,forced:ta},{keys:function(t){return Yr(at(t))}});Li.Object.keys;var ra=b(y.f),ea=b([].push),na=u&&a((function(){var t=Object.create(null);return t[2]=2,!ra(t,2)})),oa=function(t){return function(r){for(var e,n=M(r),o=Yr(n),i=na&&null===Nr(n),a=o.length,c=0,f=[];a>c;)e=o[c++],u&&!(i?e in n:ra(n,e))||ea(f,t?[e,n[e]]:n[e]);return f}},ia={entries:oa(!0),values:oa(!1)}.values;_r({target:"Object",stat:!0},{values:function(t){return ia(t)}});Li.Object.values;var aa=Object.assign,ua=Object.defineProperty,ca=b([].concat),fa=!aa||a((function(){if(u&&1!==aa({b:1},aa(ua({},"a",{enumerable:!0,get:function(){ua(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},r={},e=Symbol("assign detection");return t[e]=7,"abcdefghijklmnopqrst".split("").forEach((function(t){r[t]=t})),7!==aa({},t)[e]||"abcdefghijklmnopqrst"!==Yr(aa({},r)).join("")}))?function(t,r){for(var e=at(t),n=arguments.length,o=1,i=Tr.f,a=y.f;n>o;)for(var c,f=O(arguments[o++]),l=i?ca(Yr(f),i(f)):Yr(f),p=l.length,h=0;p>h;)c=l[h++],u&&!s(a,f,c)||(e[c]=f[c]);return e}:aa;_r({target:"Object",stat:!0,arity:2,forced:Object.assign!==fa},{assign:fa});Li.Object.assign;var sa="\t\n\v\f\r                　\u2028\u2029\ufeff",la=b("".replace),pa=RegExp("^["+sa+"]+"),ya=RegExp("(^|[^"+sa+"])["+sa+"]+$"),ha=function(t){return function(r){var e=Ie(R(r));return 1&t&&(e=la(e,pa,"")),2&t&&(e=la(e,ya,"$1")),e}},da={start:ha(1),end:ha(2),trim:ha(3)},va=Nt.PROPER,ga=function(t){return a((function(){return!!sa[t]()||"​᠎"!=="​᠎"[t]()||va&&sa[t].name!==t}))},ba=da.trim;_r({target:"String",proto:!0,forced:ga("trim")},{trim:function(){return ba(this)}});Yi("String","trim");var wa=da.start,ma=ga("trimStart")?function(){return wa(this)}:"".trimStart;_r({target:"String",proto:!0,name:"trimStart",forced:"".trimLeft!==ma},{trimLeft:ma}),_r({target:"String",proto:!0,name:"trimStart",forced:"".trimStart!==ma},{trimStart:ma});Yi("String","trimLeft");var Aa=da.end,Ta=ga("trimEnd")?function(){return Aa(this)}:"".trimEnd;_r({target:"String",proto:!0,name:"trimEnd",forced:"".trimRight!==Ta},{trimRight:Ta}),_r({target:"String",proto:!0,name:"trimEnd",forced:"".trimEnd!==Ta},{trimEnd:Ta});Yi("String","trimRight");var Ea=Math.floor,Oa=Number.isInteger||function(t){return!_(t)&&isFinite(t)&&Ea(t)===t};_r({target:"Number",stat:!0},{isInteger:Oa});Li.Number.isInteger;var Sa="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,ja=function(t,r,e){for(var n in r)nr(t,n,r[n],e);return t},Ra=RangeError,Ma=function(t){if(void 0===t)return 0;var r=ur(t),e=pr(r);if(r!==e)throw new Ra("Wrong length or index");return e},xa=Math.sign||function(t){var r=+t;return 0===r||r!=r?r:r<0?-1:1},Ia=Math.abs,Pa=Math.fround||function(t){return function(t,r,e,n){var o=+t,i=Ia(o),a=xa(o);if(i<n)return a*function(t){return t+4503599627370496-4503599627370496}(i/n/r)*n*r;var u=(1+r/2220446049250313e-31)*i,c=u-(u-i);return c>e||c!=c?a*(1/0):a*c}(t,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)},La=Array,Ca=Math.abs,_a=Math.pow,Ba=Math.floor,Fa=Math.log,Ua=Math.LN2,Da=function(t,r,e){var n,o,i,a=La(e),u=8*e-r-1,c=(1<<u)-1,f=c>>1,s=23===r?_a(2,-24)-_a(2,-77):0,l=t<0||0===t&&1/t<0?1:0,p=0;for((t=Ca(t))!=t||t===1/0?(o=t!=t?1:0,n=c):(n=Ba(Fa(t)/Ua),t*(i=_a(2,-n))<1&&(n--,i*=2),(t+=n+f>=1?s/i:s*_a(2,1-f))*i>=2&&(n++,i/=2),n+f>=c?(o=0,n=c):n+f>=1?(o=(t*i-1)*_a(2,r),n+=f):(o=t*_a(2,f-1)*_a(2,r),n=0));r>=8;)a[p++]=255&o,o/=256,r-=8;for(n=n<<r|o,u+=r;u>0;)a[p++]=255&n,n/=256,u-=8;return a[--p]|=128*l,a},Na=function(t,r){var e,n=t.length,o=8*n-r-1,i=(1<<o)-1,a=i>>1,u=o-7,c=n-1,f=t[c--],s=127&f;for(f>>=7;u>0;)s=256*s+t[c--],u-=8;for(e=s&(1<<-u)-1,s>>=-u,u+=r;u>0;)e=256*e+t[c--],u-=8;if(0===s)s=1-a;else{if(s===i)return e?NaN:f?-1/0:1/0;e+=_a(2,r),s-=a}return(f?-1:1)*e*_a(2,s-r)},ka=function(t){for(var r=at(this),e=yr(r),n=arguments.length,o=sr(n>1?arguments[1]:void 0,e),i=n>2?arguments[2]:void 0,a=void 0===i?e:sr(i,e);a>o;)r[o++]=t;return r},Wa=function(t,r,e){var n=mt(r);n in t?_t.f(t,n,h(0,e)):t[n]=e},Va=Array,Ya=Math.max,Ga=function(t,r,e){for(var n=yr(t),o=sr(r,n),i=sr(void 0===e?n:e,n),a=Va(Ya(i-o,0)),u=0;o<i;o++,u++)Wa(a,u,t[o]);return a.length=u,a},za=Ar.f,qa=Nt.PROPER,Ha=Nt.CONFIGURABLE,Xa=rr.getterFor("ArrayBuffer"),Ka=rr.getterFor("DataView"),Ja=rr.set,$a=i.ArrayBuffer,Qa=$a,Za=Qa&&Qa.prototype,tu=i.DataView,ru=tu&&tu.prototype,eu=Object.prototype,nu=i.Array,ou=i.RangeError,iu=b(ka),au=b([].reverse),uu=Da,cu=Na,fu=function(t){return[255&t]},su=function(t){return[255&t,t>>8&255]},lu=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},pu=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},yu=function(t){return uu(Pa(t),23,4)},hu=function(t){return uu(t,52,8)},du=function(t,r,e){hn(t.prototype,r,{configurable:!0,get:function(){return e(this)[r]}})},vu=function(t,r,e,n){var o=Ka(t),i=Ma(e),a=!!n;if(i+r>o.byteLength)throw new ou("Wrong index");var u=o.bytes,c=i+o.byteOffset,f=Ga(u,c,c+r);return a?f:au(f)},gu=function(t,r,e,n,o,i){var a=Ka(t),u=Ma(e),c=n(+o),f=!!i;if(u+r>a.byteLength)throw new ou("Wrong index");for(var s=a.bytes,l=u+a.byteOffset,p=0;p<r;p++)s[l+p]=c[f?p:r-p-1]};if(Sa){var bu=qa&&"ArrayBuffer"!==$a.name;if(a((function(){$a(1)}))&&a((function(){new $a(-1)}))&&!a((function(){return new $a,new $a(1.5),new $a(NaN),1!==$a.length||bu&&!Ha})))bu&&Ha&&Bt($a,"name","ArrayBuffer");else{(Qa=function(t){return bn(this,Za),new $a(Ma(t))}).prototype=Za;for(var wu,mu=za($a),Au=0;mu.length>Au;)(wu=mu[Au++])in Qa||Bt(Qa,wu,$a[wu]);Za.constructor=Qa}Vr&&Nr(ru)!==eu&&Vr(ru,eu);var Tu=new tu(new Qa(2)),Eu=b(ru.setInt8);Tu.setInt8(0,2147483648),Tu.setInt8(1,2147483649),!Tu.getInt8(0)&&Tu.getInt8(1)||ja(ru,{setInt8:function(t,r){Eu(this,t,r<<24>>24)},setUint8:function(t,r){Eu(this,t,r<<24>>24)}},{unsafe:!0})}else Za=(Qa=function(t){bn(this,Za);var r=Ma(t);Ja(this,{type:"ArrayBuffer",bytes:iu(nu(r),0),byteLength:r}),u||(this.byteLength=r,this.detached=!1)}).prototype,ru=(tu=function(t,r,e){bn(this,ru),bn(t,Za);var n=Xa(t),o=n.byteLength,i=ur(r);if(i<0||i>o)throw new ou("Wrong offset");if(i+(e=void 0===e?o-i:pr(e))>o)throw new ou("Wrong length");Ja(this,{type:"DataView",buffer:t,byteLength:e,byteOffset:i,bytes:n.bytes}),u||(this.buffer=t,this.byteLength=e,this.byteOffset=i)}).prototype,u&&(du(Qa,"byteLength",Xa),du(tu,"buffer",Ka),du(tu,"byteLength",Ka),du(tu,"byteOffset",Ka)),ja(ru,{getInt8:function(t){return vu(this,1,t)[0]<<24>>24},getUint8:function(t){return vu(this,1,t)[0]},getInt16:function(t){var r=vu(this,2,t,arguments.length>1&&arguments[1]);return(r[1]<<8|r[0])<<16>>16},getUint16:function(t){var r=vu(this,2,t,arguments.length>1&&arguments[1]);return r[1]<<8|r[0]},getInt32:function(t){return pu(vu(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return pu(vu(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return cu(vu(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return cu(vu(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,r){gu(this,1,t,fu,r)},setUint8:function(t,r){gu(this,1,t,fu,r)},setInt16:function(t,r){gu(this,2,t,su,r,arguments.length>2&&arguments[2])},setUint16:function(t,r){gu(this,2,t,su,r,arguments.length>2&&arguments[2])},setInt32:function(t,r){gu(this,4,t,lu,r,arguments.length>2&&arguments[2])},setUint32:function(t,r){gu(this,4,t,lu,r,arguments.length>2&&arguments[2])},setFloat32:function(t,r){gu(this,4,t,yu,r,arguments.length>2&&arguments[2])},setFloat64:function(t,r){gu(this,8,t,hu,r,arguments.length>2&&arguments[2])}});Ke(Qa,"ArrayBuffer"),Ke(tu,"DataView");var Ou={ArrayBuffer:Qa,DataView:tu},Su=Ou.ArrayBuffer,ju=i.ArrayBuffer;_r({global:!0,constructor:!0,forced:ju!==Su},{ArrayBuffer:Su}),vn("ArrayBuffer");var Ru=Ou.ArrayBuffer,Mu=Ou.DataView,xu=Mu.prototype,Iu=ue(Ru.prototype.slice),Pu=ue(xu.getUint8),Lu=ue(xu.setUint8),Cu=a((function(){return!new Ru(2).slice(1,void 0).byteLength}));_r({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:Cu},{slice:function(t,r){if(Iu&&void 0===r)return Iu(It(this),t);for(var e=It(this).byteLength,n=sr(t,e),o=sr(void 0===r?e:r,e),i=new(Bn(this,Ru))(pr(o-n)),a=new Mu(this),u=new Mu(i),c=0;n<o;)Lu(u,c++,Pu(a,n++));return i}});var _u,Bu,Fu,Uu=rr.enforce,Du=rr.get,Nu=i.Int8Array,ku=Nu&&Nu.prototype,Wu=i.Uint8ClampedArray,Vu=Wu&&Wu.prototype,Yu=Nu&&Nr(Nu),Gu=ku&&Nr(ku),zu=Object.prototype,qu=i.TypeError,Hu=vt("toStringTag"),Xu=pt("TYPED_ARRAY_TAG"),Ku=Sa&&!!Vr&&"Opera"!==we(i.opera),Ju=!1,$u={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},Qu={BigInt64Array:8,BigUint64Array:8},Zu=function(t){var r=Nr(t);if(_(r)){var e=Du(r);return e&&ct(e,"TypedArrayConstructor")?e.TypedArrayConstructor:Zu(r)}},tc=function(t){if(!_(t))return!1;var r=we(t);return ct($u,r)||ct(Qu,r)};for(_u in $u)(Fu=(Bu=i[_u])&&Bu.prototype)?Uu(Fu).TypedArrayConstructor=Bu:Ku=!1;for(_u in Qu)(Fu=(Bu=i[_u])&&Bu.prototype)&&(Uu(Fu).TypedArrayConstructor=Bu);if((!Ku||!L(Yu)||Yu===Function.prototype)&&(Yu=function(){throw new qu("Incorrect invocation")},Ku))for(_u in $u)i[_u]&&Vr(i[_u],Yu);if((!Ku||!Gu||Gu===zu)&&(Gu=Yu.prototype,Ku))for(_u in $u)i[_u]&&Vr(i[_u].prototype,Gu);if(Ku&&Nr(Vu)!==Gu&&Vr(Vu,Gu),u&&!ct(Gu,Hu))for(_u in Ju=!0,hn(Gu,Hu,{configurable:!0,get:function(){return _(this)?this[Xu]:void 0}}),$u)i[_u]&&Bt(i[_u],Xu,_u);var rc={NATIVE_ARRAY_BUFFER_VIEWS:Ku,TYPED_ARRAY_TAG:Ju&&Xu,aTypedArray:function(t){if(tc(t))return t;throw new qu("Target is not a typed array")},aTypedArrayConstructor:function(t){if(L(t)&&(!Vr||U(Yu,t)))return t;throw new qu(J(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,r,e,n){if(u){if(e)for(var o in $u){var a=i[o];if(a&&ct(a.prototype,t))try{delete a.prototype[t]}catch(e){try{a.prototype[t]=r}catch(t){}}}Gu[t]&&!e||nr(Gu,t,e?r:Ku&&ku[t]||r,n)}},exportTypedArrayStaticMethod:function(t,r,e){var n,o;if(u){if(Vr){if(e)for(n in $u)if((o=i[n])&&ct(o,t))try{delete o[t]}catch(t){}if(Yu[t]&&!e)return;try{return nr(Yu,t,e?r:Ku&&Yu[t]||r)}catch(t){}}for(n in $u)!(o=i[n])||o[t]&&!e||nr(o,t,r)}},getTypedArrayConstructor:Zu,isView:function(t){if(!_(t))return!1;var r=we(t);return"DataView"===r||ct($u,r)||ct(Qu,r)},isTypedArray:tc,TypedArray:Yu,TypedArrayPrototype:Gu},ec=rc.NATIVE_ARRAY_BUFFER_VIEWS,nc=i.ArrayBuffer,oc=i.Int8Array,ic=!ec||!a((function(){oc(1)}))||!a((function(){new oc(-1)}))||!hi((function(t){new oc,new oc(null),new oc(1.5),new oc(t)}),!0)||a((function(){return 1!==new oc(new nc(2),1,void 0).length})),ac=RangeError,uc=RangeError,cc=function(t,r){var e=function(t){var r=ur(t);if(r<0)throw new ac("The argument can't be less than 0");return r}(t);if(e%r)throw new uc("Wrong offset");return e},fc=Math.round,sc=function(t){var r=we(t);return"BigInt64Array"===r||"BigUint64Array"===r},lc=TypeError,pc=function(t){var r=wt(t,"number");if("number"==typeof r)throw new lc("Can't convert number to bigint");return BigInt(r)},yc=rc.aTypedArrayConstructor,hc=function(t){var r,e,n,o,i,a,u,c,f=Cn(this),l=at(t),p=arguments.length,y=p>1?arguments[1]:void 0,h=void 0!==y,d=Ae(l);if(d&&!ye(d))for(c=(u=Ee(l,d)).next,l=[];!(a=s(c,u)).done;)l.push(a.value);for(h&&p>2&&(y=fe(y,arguments[2])),e=yr(l),n=new(yc(f))(e),o=sc(n),r=0;e>r;r++)i=h?y(l[r],r):l[r],n[r]=o?pc(i):+i;return n};r((function(t){var r=Ar.f,e=Ni.forEach,n=rr.get,o=rr.set,a=rr.enforce,c=_t.f,f=jt.f,l=i.RangeError,p=Ou.ArrayBuffer,y=p.prototype,d=Ou.DataView,v=rc.NATIVE_ARRAY_BUFFER_VIEWS,g=rc.TYPED_ARRAY_TAG,b=rc.TypedArray,w=rc.TypedArrayPrototype,m=rc.aTypedArrayConstructor,A=rc.isTypedArray,T=function(t,r){m(t);for(var e=0,n=r.length,o=new t(n);n>e;)o[e]=r[e++];return o},E=function(t,r){hn(t,r,{configurable:!0,get:function(){return n(this)[r]}})},O=function(t){var r;return U(y,t)||"ArrayBuffer"===(r=we(t))||"SharedArrayBuffer"===r},S=function(t,r){return A(t)&&!X(r)&&r in t&&Oa(+r)&&r>=0},j=function(t,r){return r=mt(r),S(t,r)?h(2,t[r]):f(t,r)},R=function(t,r,e){return r=mt(r),!(S(t,r)&&_(e)&&ct(e,"value"))||ct(e,"get")||ct(e,"set")||e.configurable||ct(e,"writable")&&!e.writable||ct(e,"enumerable")&&!e.enumerable?c(t,r,e):(t[r]=e.value,t)};u?(v||(jt.f=j,_t.f=R,E(w,"buffer"),E(w,"byteOffset"),E(w,"byteLength"),E(w,"length")),_r({target:"Object",stat:!0,forced:!v},{getOwnPropertyDescriptor:j,defineProperty:R}),t.exports=function(t,u,f){var y=t.match(/\d+/)[0]/8,h=t+(f?"Clamped":"")+"Array",m="get"+t,E="set"+t,S=i[h],j=S,R=j&&j.prototype,M={},x=function(t,r,e){var o=n(t);o.view[E](r*y+o.byteOffset,f?function(t){var r=fc(t);return r<0?0:r>255?255:255&r}(e):e,!0)},I=function(t,r){c(t,r,{get:function(){return function(t,r){var e=n(t);return e.view[m](r*y+e.byteOffset,!0)}(this,r)},set:function(t){return x(this,r,t)},enumerable:!0})};v?ic&&(j=u((function(t,r,e,n){return bn(t,R),function(t,r,e){var n,o;return Vr&&L(n=r.constructor)&&n!==e&&_(o=n.prototype)&&o!==e.prototype&&Vr(t,o),t}(_(r)?O(r)?void 0!==n?new S(r,cc(e,y),n):void 0!==e?new S(r,cc(e,y)):new S(r):A(r)?T(j,r):s(hc,j,r):new S(Ma(r)),t,j)})),Vr&&Vr(j,b),e(r(S),(function(t){t in j||Bt(j,t,S[t])})),j.prototype=R):(j=u((function(t,r,e,n){bn(t,R);var i,a,u,c=0,f=0;if(_(r)){if(!O(r))return A(r)?T(j,r):s(hc,j,r);i=r,f=cc(e,y);var h=r.byteLength;if(void 0===n){if(h%y)throw new l("Wrong length");if((a=h-f)<0)throw new l("Wrong length")}else if((a=pr(n)*y)+f>h)throw new l("Wrong length");u=a/y}else u=Ma(r),i=new p(a=u*y);for(o(t,{buffer:i,byteOffset:f,byteLength:a,length:u,view:new d(i)});c<u;)I(t,c++)})),Vr&&Vr(j,b),R=j.prototype=$r(w)),R.constructor!==j&&Bt(R,"constructor",j),a(R).TypedArrayConstructor=j,g&&Bt(R,g,h);var P=j!==S;M[h]=j,_r({global:!0,constructor:!0,forced:P,sham:!v},M),"BYTES_PER_ELEMENT"in j||Bt(j,"BYTES_PER_ELEMENT",y),"BYTES_PER_ELEMENT"in R||Bt(R,"BYTES_PER_ELEMENT",y),vn(h)}):t.exports=function(){}}))("Uint8",(function(t){return function(r,e,n){return t(this,r,e,n)}})),(0,rc.exportTypedArrayStaticMethod)("from",hc,ic);var dc=rc.aTypedArrayConstructor;(0,rc.exportTypedArrayStaticMethod)("of",(function(){for(var t=0,r=arguments.length,e=new(dc(this))(r);r>t;)e[t]=arguments[t++];return e}),ic);var vc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("at",(function(t){var r=vc(this),e=yr(r),n=ur(t),o=n>=0?n:e+n;return o<0||o>=e?void 0:r[o]}));var gc=TypeError,bc=function(t,r){if(!delete t[r])throw new gc("Cannot delete property "+J(r)+" of "+J(t))},wc=Math.min,mc=b([].copyWithin||function(t,r){var e=at(this),n=yr(e),o=sr(t,n),i=sr(r,n),a=arguments.length>2?arguments[2]:void 0,u=wc((void 0===a?n:sr(a,n))-i,n-o),c=1;for(i<o&&o<i+u&&(c=-1,i+=u-1,o+=u-1);u-- >0;)i in e?e[o]=e[i]:bc(e,o),o+=c,i+=c;return e}),Ac=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("copyWithin",(function(t,r){return mc(Ac(this),t,r,arguments.length>2?arguments[2]:void 0)}));var Tc=Ni.every,Ec=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("every",(function(t){return Tc(Ec(this),t,arguments.length>1?arguments[1]:void 0)}));var Oc=rc.aTypedArray,Sc=rc.exportTypedArrayMethod,jc=b("".slice);Sc("fill",(function(t){var r=arguments.length;Oc(this);var e="Big"===jc(we(this),0,3)?pc(t):+t;return s(ka,this,e,r>1?arguments[1]:void 0,r>2?arguments[2]:void 0)}),a((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t})));var Rc=function(t,r){for(var e=0,n=yr(r),o=new t(n);n>e;)o[e]=r[e++];return o},Mc=rc.aTypedArrayConstructor,xc=rc.getTypedArrayConstructor,Ic=function(t){return Mc(Bn(t,xc(t)))},Pc=function(t,r){return Rc(Ic(t),r)},Lc=Ni.filter,Cc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("filter",(function(t){var r=Lc(Cc(this),t,arguments.length>1?arguments[1]:void 0);return Pc(this,r)}));var _c=Ni.find,Bc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("find",(function(t){return _c(Bc(this),t,arguments.length>1?arguments[1]:void 0)}));var Fc=Ni.findIndex,Uc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("findIndex",(function(t){return Fc(Uc(this),t,arguments.length>1?arguments[1]:void 0)}));var Dc=function(t){var r=1===t;return function(e,n,o){for(var i,a=at(e),u=O(a),c=fe(n,o),f=yr(u);f-- >0;)if(c(i=u[f],f,a))switch(t){case 0:return i;case 1:return f}return r?-1:void 0}},Nc={findLast:Dc(0),findLastIndex:Dc(1)},kc=Nc.findLast,Wc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("findLast",(function(t){return kc(Wc(this),t,arguments.length>1?arguments[1]:void 0)}));var Vc=Nc.findLastIndex,Yc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("findLastIndex",(function(t){return Vc(Yc(this),t,arguments.length>1?arguments[1]:void 0)}));var Gc=Ni.forEach,zc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("forEach",(function(t){Gc(zc(this),t,arguments.length>1?arguments[1]:void 0)}));var qc=dr.includes,Hc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("includes",(function(t){return qc(Hc(this),t,arguments.length>1?arguments[1]:void 0)}));var Xc=dr.indexOf,Kc=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("indexOf",(function(t){return Xc(Kc(this),t,arguments.length>1?arguments[1]:void 0)}));var Jc=rc.aTypedArray,$c=rc.exportTypedArrayMethod,Qc=b([].join);$c("join",(function(t){return Qc(Jc(this),t)}));var Zc=Math.min,tf=[].lastIndexOf,rf=!!tf&&1/[1].lastIndexOf(1,-0)<0,ef=Hi("lastIndexOf"),nf=rf||!ef?function(t){if(rf)return Nn(tf,this,arguments)||0;var r=M(this),e=yr(r),n=e-1;for(arguments.length>1&&(n=Zc(n,ur(arguments[1]))),n<0&&(n=e+n);n>=0;n--)if(n in r&&r[n]===t)return n||0;return-1}:tf,of=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("lastIndexOf",(function(t){var r=arguments.length;return Nn(nf,of(this),r>1?[t,arguments[1]]:[t])}));var af=Ni.map,uf=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("map",(function(t){return af(uf(this),t,arguments.length>1?arguments[1]:void 0,(function(t,r){return new(Ic(t))(r)}))}));var cf=qi.left,ff=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("reduce",(function(t){var r=arguments.length;return cf(ff(this),t,r,r>1?arguments[1]:void 0)}));var sf=qi.right,lf=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("reduceRight",(function(t){var r=arguments.length;return sf(lf(this),t,r,r>1?arguments[1]:void 0)}));var pf=rc.aTypedArray,yf=rc.exportTypedArrayMethod,hf=Math.floor;yf("reverse",(function(){for(var t,r=pf(this).length,e=hf(r/2),n=0;n<e;)t=this[n],this[n++]=this[--r],this[r]=t;return this}));var df=i.RangeError,vf=i.Int8Array,gf=vf&&vf.prototype,bf=gf&&gf.set,wf=rc.aTypedArray,mf=rc.exportTypedArrayMethod,Af=!a((function(){var t=new Uint8ClampedArray(2);return s(bf,t,{length:1,0:3},1),3!==t[1]})),Tf=Af&&rc.NATIVE_ARRAY_BUFFER_VIEWS&&a((function(){var t=new vf(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));mf("set",(function(t){wf(this);var r=cc(arguments.length>1?arguments[1]:void 0,1),e=at(t);if(Af)return s(bf,this,e,r);var n=this.length,o=yr(e),i=0;if(o+r>n)throw new df("Wrong length");for(;i<o;)this[r+i]=e[i++]}),!Af||Tf);var Ef=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("slice",(function(t,r){for(var e=kn(Ef(this),t,r),n=Ic(this),o=0,i=e.length,a=new n(i);i>o;)a[o]=e[o++];return a}),a((function(){new Int8Array(1).slice()})));var Of=Ni.some,Sf=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("some",(function(t){return Of(Sf(this),t,arguments.length>1?arguments[1]:void 0)}));var jf=Math.floor,Rf=function(t,r){var e=t.length,n=jf(e/2);return e<8?Mf(t,r):xf(t,Rf(Ga(t,0,n),r),Rf(Ga(t,n),r),r)},Mf=function(t,r){for(var e,n,o=t.length,i=1;i<o;){for(n=i,e=t[i];n&&r(t[n-1],e)>0;)t[n]=t[--n];n!==i++&&(t[n]=e)}return t},xf=function(t,r,e,n){for(var o=r.length,i=e.length,a=0,u=0;a<o||u<i;)t[a+u]=a<o&&u<i?n(r[a],e[u])<=0?r[a++]:e[u++]:a<o?r[a++]:e[u++];return t},If=Rf,Pf=D.match(/firefox\/(\d+)/i),Lf=!!Pf&&+Pf[1],Cf=/MSIE|Trident/.test(D),_f=D.match(/AppleWebKit\/(\d+)\./),Bf=!!_f&&+_f[1],Ff=rc.aTypedArray,Uf=rc.exportTypedArrayMethod,Df=i.Uint16Array,Nf=Df&&ue(Df.prototype.sort),kf=!(!Nf||a((function(){Nf(new Df(2),null)}))&&a((function(){Nf(new Df(2),{})}))),Wf=!!Nf&&!a((function(){if(Y)return Y<74;if(Lf)return Lf<67;if(Cf)return!0;if(Bf)return Bf<602;var t,r,e=new Df(516),n=Array(516);for(t=0;t<516;t++)r=t%4,e[t]=515-t,n[t]=t-2*r+3;for(Nf(e,(function(t,r){return(t/4|0)-(r/4|0)})),t=0;t<516;t++)if(e[t]!==n[t])return!0}));Uf("sort",(function(t){return void 0!==t&&Q(t),Wf?Nf(this,t):If(Ff(this),function(t){return function(r,e){return void 0!==t?+t(r,e)||0:e!=e?-1:r!=r?1:0===r&&0===e?1/r>0&&1/e<0?1:-1:r>e}}(t))}),!Wf||kf);var Vf=rc.aTypedArray;(0,rc.exportTypedArrayMethod)("subarray",(function(t,r){var e=Vf(this),n=e.length,o=sr(t,n);return new(Ic(e))(e.buffer,e.byteOffset+o*e.BYTES_PER_ELEMENT,pr((void 0===r?n:sr(r,n))-o))}));var Yf=i.Int8Array,Gf=rc.aTypedArray,zf=rc.exportTypedArrayMethod,qf=[].toLocaleString,Hf=!!Yf&&a((function(){qf.call(new Yf(1))}));zf("toLocaleString",(function(){return Nn(qf,Hf?kn(Gf(this)):Gf(this),kn(arguments))}),a((function(){return[1,2].toLocaleString()!==new Yf([1,2]).toLocaleString()}))||!a((function(){Yf.prototype.toLocaleString.call([1,2])})));var Xf=rc.exportTypedArrayMethod,Kf=i.Uint8Array,Jf=Kf&&Kf.prototype||{},$f=[].toString,Qf=b([].join);a((function(){$f.call({})}))&&($f=function(){return Qf(this)});var Zf=Jf.toString!==$f;Xf("toString",$f,Zf);var ts=rc.aTypedArray,rs=rc.getTypedArrayConstructor;(0,rc.exportTypedArrayMethod)("toReversed",(function(){return function(t,r){for(var e=yr(t),n=new r(e),o=0;o<e;o++)n[o]=t[e-o-1];return n}(ts(this),rs(this))}));var es=rc.aTypedArray,ns=rc.getTypedArrayConstructor,os=rc.exportTypedArrayMethod,is=b(rc.TypedArrayPrototype.sort);os("toSorted",(function(t){void 0!==t&&Q(t);var r=es(this),e=Rc(ns(r),r);return is(e,t)}));var as=RangeError,us=rc.aTypedArray,cs=rc.getTypedArrayConstructor,fs=rc.exportTypedArrayMethod,ss=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();fs("with",{with:function(t,r){var e=us(this),n=ur(t),o=sc(e)?pc(r):+r;return function(t,r,e,n){var o=yr(t),i=ur(e),a=i<0?o+i:i;if(a>=o||a<0)throw new as("Incorrect index");for(var u=new r(o),c=0;c<o;c++)u[c]=c===a?n:t[c];return u}(e,cs(e),n,o)}}.with,!ss);var ls=vt("iterator"),ps=i.Uint8Array,ys=b(sn.values),hs=b(sn.keys),ds=b(sn.entries),vs=rc.aTypedArray,gs=rc.exportTypedArrayMethod,bs=ps&&ps.prototype,ws=!a((function(){bs[ls].call([1])})),ms=!!bs&&bs.values&&bs[ls]===bs.values&&"values"===bs.values.name,As=function(){return ys(vs(this))};gs("entries",(function(){return ds(vs(this))}),ws),gs("keys",(function(){return hs(vs(this))}),ws),gs("values",As,ws||!ms,{name:"values"}),gs(ls,As,ws||!ms,{name:"values"});i.Uint8Array;var Ts=function(){return"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this}(),Es=Ts.BlobBuilder||Ts.WebKitBlobBuilder||Ts.MSBlobBuilder||Ts.MozBlobBuilder;Ts.URL=Ts.URL||Ts.webkitURL||function(t,r){return(r=document.createElement("a")).href=t,r};var Os=Ts.Blob,Ss=URL.createObjectURL,js=URL.revokeObjectURL,Rs=Ts.Symbol&&Ts.Symbol.toStringTag,Ms=!1,xs=!1,Is=!!Ts.ArrayBuffer,Ps=Es&&Es.prototype.append&&Es.prototype.getBlob;try{Ms=2===new Blob(["ä"]).size,xs=2===new Blob([new Uint8Array([1,2])]).size}catch(t){}function Ls(t){return t.map((function(t){if(t.buffer instanceof ArrayBuffer){var r=t.buffer;if(t.byteLength!==r.byteLength){var e=new Uint8Array(t.byteLength);e.set(new Uint8Array(r,t.byteOffset,t.byteLength)),r=e.buffer}return r}return t}))}function Cs(t,r){r=r||{};var e=new Es;return Ls(t).forEach((function(t){e.append(t)})),r.type?e.getBlob(r.type):e.getBlob()}function _s(t,r){return new Os(Ls(t),r||{})}if(Ts.Blob&&(Cs.prototype=Blob.prototype,_s.prototype=Blob.prototype),Rs)try{File.prototype[Rs]="File",Blob.prototype[Rs]="Blob",FileReader.prototype[Rs]="FileReader"}catch(t){}function Bs(){var t=!!Ts.ActiveXObject||"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style,r=Ts.XMLHttpRequest&&Ts.XMLHttpRequest.prototype.send;t&&r&&(XMLHttpRequest.prototype.send=function(t){t instanceof Blob?(this.setRequestHeader("Content-Type",t.type),r.call(this,t)):r.call(this,t)});try{new File([],"")}catch(t){try{var e=new Function('class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name;this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date;this.lastModified = +this.lastModifiedDate;}};return new File([], ""), File')();Ts.File=e}catch(t){e=function(t,r,e){var n=new Blob(t,e),o=e&&void 0!==e.lastModified?new Date(e.lastModified):new Date;return n.name=r,n.lastModifiedDate=o,n.lastModified=+o,n.toString=function(){return"[object File]"},Rs&&(n[Rs]="File"),n};Ts.File=e}}}Ms?(Bs(),Ts.Blob=xs?Ts.Blob:_s):Ps?(Bs(),Ts.Blob=Cs):function(){function t(t){for(var r=[],e=0;e<t.length;e++){var n=t.charCodeAt(e);n<128?r.push(n):n<2048?r.push(192|n>>6,128|63&n):n<55296||n>=57344?r.push(224|n>>12,128|n>>6&63,128|63&n):(e++,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return r}function r(t){var r,e,n,o,i,a;for(r="",n=t.length,e=0;e<n;)switch((o=t[e++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:r+=String.fromCharCode(o);break;case 12:case 13:i=t[e++],r+=String.fromCharCode((31&o)<<6|63&i);break;case 14:i=t[e++],a=t[e++],r+=String.fromCharCode((15&o)<<12|(63&i)<<6|(63&a)<<0)}return r}function e(t){for(var r=new Array(t.byteLength),e=new Uint8Array(t),n=r.length;n--;)r[n]=e[n];return r}function n(t){for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",e=[],n=0;n<t.length;n+=3){var o=t[n],i=n+1<t.length,a=i?t[n+1]:0,u=n+2<t.length,c=u?t[n+2]:0,f=o>>2,s=(3&o)<<4|a>>4,l=(15&a)<<2|c>>6,p=63&c;u||(p=64,i||(l=64)),e.push(r[f],r[s],r[l],r[p])}return e.join("")}var o=Object.create||function(t){function r(){}return r.prototype=t,new r};if(Is)var i=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],a=ArrayBuffer.isView||function(t){return t&&i.indexOf(Object.prototype.toString.call(t))>-1};function u(r,n){for(var o=0,i=(r=r||[]).length;o<i;o++){var c=r[o];c instanceof u?r[o]=c._buffer:"string"==typeof c?r[o]=t(c):Is&&(ArrayBuffer.prototype.isPrototypeOf(c)||a(c))?r[o]=e(c):Is&&((f=c)&&DataView.prototype.isPrototypeOf(f))?r[o]=e(c.buffer):r[o]=t(String(c))}var f;this._buffer=[].concat.apply([],r),this.size=this._buffer.length,this.type=n&&n.type||""}function c(t,r,e){e=e||{};var n=u.call(this,t,e)||this;return n.name=r,n.lastModifiedDate=e.lastModified?new Date(e.lastModified):new Date,n.lastModified=+n.lastModifiedDate,n}if(u.prototype.slice=function(t,r,e){return new u([this._buffer.slice(t||0,r||this._buffer.length)],{type:e})},u.prototype.toString=function(){return"[object Blob]"},c.prototype=o(u.prototype),c.prototype.constructor=c,Object.setPrototypeOf)Object.setPrototypeOf(c,u);else try{c.__proto__=u}catch(t){}function f(){if(!(this instanceof f))throw new TypeError("Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");var t=document.createDocumentFragment();this.addEventListener=t.addEventListener,this.dispatchEvent=function(r){var e=this["on"+r.type];"function"==typeof e&&e(r),t.dispatchEvent(r)},this.removeEventListener=t.removeEventListener}function s(t,r,e){if(!(r instanceof u))throw new TypeError("Failed to execute '"+e+"' on 'FileReader': parameter 1 is not of type 'Blob'.");t.result="",setTimeout((function(){this.readyState=f.LOADING,t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend"))}))}c.prototype.toString=function(){return"[object File]"},f.EMPTY=0,f.LOADING=1,f.DONE=2,f.prototype.error=null,f.prototype.onabort=null,f.prototype.onerror=null,f.prototype.onload=null,f.prototype.onloadend=null,f.prototype.onloadstart=null,f.prototype.onprogress=null,f.prototype.readAsDataURL=function(t){s(this,t,"readAsDataURL"),this.result="data:"+t.type+";base64,"+n(t._buffer)},f.prototype.readAsText=function(t){s(this,t,"readAsText"),this.result=r(t._buffer)},f.prototype.readAsArrayBuffer=function(t){s(this,t,"readAsText"),this.result=t._buffer.slice()},f.prototype.abort=function(){},URL.createObjectURL=function(t){return t instanceof u?"data:"+t.type+";base64,"+n(t._buffer):Ss.call(URL,t)},URL.revokeObjectURL=function(t){js&&js.call(URL,t)};var l=Ts.XMLHttpRequest&&Ts.XMLHttpRequest.prototype.send;l&&(XMLHttpRequest.prototype.send=function(t){t instanceof u?(this.setRequestHeader("Content-Type",t.type),l.call(this,r(t._buffer))):l.call(this,t)}),Ts.FileReader=f,Ts.File=c,Ts.Blob=u}();var Fs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";void 0===Ts.btoa&&(Ts.btoa=function(t){var r,e,n,o,i,a=0,u=0,c="",f=[];if(!t)return t;do{r=(i=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,e=i>>12&63,n=i>>6&63,o=63&i,f[u++]=Fs.charAt(r)+Fs.charAt(e)+Fs.charAt(n)+Fs.charAt(o)}while(a<t.length);c=f.join("");var s=t.length%3;return(s?c.slice(0,s-3):c)+"===".slice(s||3)}),void 0===Ts.atob&&(Ts.atob=function(t){var r,e,n,o,i,a,u=0,c=0,f=[];if(!t)return t;t+="";do{r=(a=Fs.indexOf(t.charAt(u++))<<18|Fs.indexOf(t.charAt(u++))<<12|(o=Fs.indexOf(t.charAt(u++)))<<6|(i=Fs.indexOf(t.charAt(u++))))>>16&255,e=a>>8&255,n=255&a,f[c++]=64==o?String.fromCharCode(r):64==i?String.fromCharCode(r,e):String.fromCharCode(r,e,n)}while(u<t.length);return f.join("")})}));
