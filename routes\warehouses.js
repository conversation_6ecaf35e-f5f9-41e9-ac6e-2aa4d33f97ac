const express = require('express');
const { executeQuery } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('warehouses', 'inventory', 'audit'));

// الحصول على جميع المخازن
router.get('/', async (req, res) => {
  try {
    const { type, active_only = 'true' } = req.query;
    
    let whereClause = '';
    let params = [];

    if (type) {
      whereClause += 'WHERE warehouse_type = ?';
      params.push(type);
    }

    if (active_only === 'true') {
      whereClause += whereClause ? ' AND is_active = TRUE' : 'WHERE is_active = TRUE';
    }

    const warehouses = await executeQuery(`
      SELECT w.*, u.full_name as manager_name
      FROM warehouses w
      LEFT JOIN users u ON w.manager_id = u.id
      ${whereClause}
      ORDER BY w.warehouse_code
    `, params);

    res.json({
      success: true,
      data: { warehouses }
    });

  } catch (error) {
    console.error('خطأ في الحصول على المخازن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على علوم الاستلام
router.get('/receipt-notes', async (req, res) => {
  try {
    const { status, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause += 'WHERE rn.status = ?';
      params.push(status);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND rn.receipt_date >= ?' : 'WHERE rn.receipt_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND rn.receipt_date <= ?' : 'WHERE rn.receipt_date <= ?';
      params.push(end_date);
    }

    const receiptNotes = await executeQuery(`
      SELECT 
        rn.*,
        creator.full_name as created_by_name,
        inspector.full_name as technical_inspector_name,
        gatekeeper.full_name as gate_keeper_name,
        warehouse_keeper.full_name as warehouse_keeper_name
      FROM receipt_notes rn
      LEFT JOIN users creator ON rn.created_by = creator.id
      LEFT JOIN users inspector ON rn.technical_inspector_id = inspector.id
      LEFT JOIN users gatekeeper ON rn.gate_keeper_id = gatekeeper.id
      LEFT JOIN users warehouse_keeper ON rn.warehouse_keeper_id = warehouse_keeper.id
      ${whereClause}
      ORDER BY rn.receipt_date DESC, rn.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // الحصول على تفاصيل كل علم استلام
    for (let note of receiptNotes) {
      const details = await executeQuery(`
        SELECT rnd.*, i.item_name, i.item_code, i.unit_of_measure
        FROM receipt_note_details rnd
        JOIN items i ON rnd.item_id = i.id
        WHERE rnd.receipt_note_id = ?
      `, [note.id]);
      note.details = details;
    }

    // عدد علوم الاستلام الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM receipt_notes rn ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        receiptNotes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على علوم الاستلام:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء علم استلام جديد
router.post('/receipt-notes', auditLog('INSERT', 'receipt_notes'), async (req, res) => {
  try {
    const { 
      supplier_name, 
      receipt_date, 
      items_details, 
      notes 
    } = req.body;

    if (!supplier_name || !receipt_date || !items_details || !items_details.length) {
      return res.status(400).json({
        success: false,
        message: 'اسم المورد وتاريخ الاستلام وتفاصيل الأصناف مطلوبة'
      });
    }

    // إنشاء رقم علم الاستلام
    const currentYear = new Date().getFullYear();
    const receiptCount = await executeQuery(
      'SELECT COUNT(*) as count FROM receipt_notes WHERE YEAR(receipt_date) = ?',
      [currentYear]
    );
    
    const receiptNumber = `RN-${currentYear}-${String(receiptCount[0].count + 1).padStart(4, '0')}`;

    // حساب إجمالي المبلغ
    const totalAmount = items_details.reduce((sum, item) => 
      sum + (item.quantity_received * item.unit_cost), 0
    );

    // إنشاء علم الاستلام
    const receiptResult = await executeQuery(
      `INSERT INTO receipt_notes 
       (receipt_number, receipt_date, supplier_name, total_amount, notes, created_by) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [receiptNumber, receipt_date, supplier_name, totalAmount, notes, req.user.id]
    );

    const receiptNoteId = receiptResult.insertId;

    // إضافة تفاصيل الأصناف
    for (const item of items_details) {
      await executeQuery(
        `INSERT INTO receipt_note_details 
         (receipt_note_id, item_id, quantity_ordered, quantity_received, unit_cost, total_cost, inspection_notes) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          receiptNoteId,
          item.item_id,
          item.quantity_ordered,
          item.quantity_received,
          item.unit_cost,
          item.quantity_received * item.unit_cost,
          item.inspection_notes
        ]
      );
    }

    req.recordId = receiptNoteId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء علم الاستلام بنجاح',
      data: { 
        receiptNoteId,
        receiptNumber 
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء علم الاستلام:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على بواني الصرف
router.get('/issue-notes', async (req, res) => {
  try {
    const { status, department, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause += 'WHERE isn.status = ?';
      params.push(status);
    }

    if (department) {
      whereClause += whereClause ? ' AND isn.department = ?' : 'WHERE isn.department = ?';
      params.push(department);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND isn.issue_date >= ?' : 'WHERE isn.issue_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND isn.issue_date <= ?' : 'WHERE isn.issue_date <= ?';
      params.push(end_date);
    }

    const issueNotes = await executeQuery(`
      SELECT 
        isn.*,
        creator.full_name as created_by_name,
        approver.full_name as approved_by_name,
        issuer.full_name as issued_by_name
      FROM issue_notes isn
      LEFT JOIN users creator ON isn.created_by = creator.id
      LEFT JOIN users approver ON isn.approved_by = approver.id
      LEFT JOIN users issuer ON isn.issued_by = issuer.id
      ${whereClause}
      ORDER BY isn.issue_date DESC, isn.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // الحصول على تفاصيل كل بونة صرف
    for (let note of issueNotes) {
      const details = await executeQuery(`
        SELECT ind.*, i.item_name, i.item_code, i.unit_of_measure, w.warehouse_name
        FROM issue_note_details ind
        JOIN items i ON ind.item_id = i.id
        JOIN warehouses w ON ind.warehouse_id = w.id
        WHERE ind.issue_note_id = ?
      `, [note.id]);
      note.details = details;
    }

    // عدد بواني الصرف الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM issue_notes isn ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        issueNotes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على بواني الصرف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء بونة صرف جديدة
router.post('/issue-notes', auditLog('INSERT', 'issue_notes'), async (req, res) => {
  try {
    const { 
      department, 
      cost_center, 
      issue_date, 
      items_details, 
      notes 
    } = req.body;

    if (!department || !issue_date || !items_details || !items_details.length) {
      return res.status(400).json({
        success: false,
        message: 'القسم وتاريخ الصرف وتفاصيل الأصناف مطلوبة'
      });
    }

    // إنشاء رقم بونة الصرف
    const currentYear = new Date().getFullYear();
    const issueCount = await executeQuery(
      'SELECT COUNT(*) as count FROM issue_notes WHERE YEAR(issue_date) = ?',
      [currentYear]
    );
    
    const issueNumber = `IN-${currentYear}-${String(issueCount[0].count + 1).padStart(4, '0')}`;

    // حساب إجمالي المبلغ
    const totalAmount = items_details.reduce((sum, item) => 
      sum + (item.quantity_requested * item.unit_cost), 0
    );

    // إنشاء بونة الصرف
    const issueResult = await executeQuery(
      `INSERT INTO issue_notes 
       (issue_number, issue_date, department, cost_center, total_amount, notes, created_by) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [issueNumber, issue_date, department, cost_center, totalAmount, notes, req.user.id]
    );

    const issueNoteId = issueResult.insertId;

    // إضافة تفاصيل الأصناف
    for (const item of items_details) {
      await executeQuery(
        `INSERT INTO issue_note_details 
         (issue_note_id, item_id, warehouse_id, quantity_requested, quantity_issued, unit_cost, total_cost) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          issueNoteId,
          item.item_id,
          item.warehouse_id,
          item.quantity_requested,
          item.quantity_issued || item.quantity_requested,
          item.unit_cost,
          (item.quantity_issued || item.quantity_requested) * item.unit_cost
        ]
      );
    }

    req.recordId = issueNoteId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء بونة الصرف بنجاح',
      data: { 
        issueNoteId,
        issueNumber 
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء بونة الصرف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
