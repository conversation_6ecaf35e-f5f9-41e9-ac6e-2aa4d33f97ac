// ملف اختبار بسيط للتأكد من عمل النظام
const express = require('express');
const cors = require('cors');

const app = express();

// إعدادات أساسية
app.use(cors());
app.use(express.json());

// صفحة ترحيب
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مرحباً بك في نظام المحاسبة والمراجعة الداخلية! 🎉',
    version: '1.0.0',
    features: [
      '📊 الحسابات المالية والتكاليف',
      '🏪 إدارة المخازن',
      '🛒 إدارة المشتريات', 
      '💰 إدارة الخزينة',
      '📄 مخازن الورق',
      '📈 التقارير والمراجعة'
    ],
    next_steps: [
      '1. قم بإعداد قاعدة البيانات: npm run setup',
      '2. شغل النظام: npm start',
      '3. سجل دخول باستخدام: admin / admin123'
    ]
  });
});

// معلومات النظام
app.get('/api/info', (req, res) => {
  res.json({
    success: true,
    system: {
      name: 'نظام المحاسبة والمراجعة الداخلية',
      version: '1.0.0',
      author: 'Ashraf',
      description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية'
    },
    modules: {
      financial: 'الحسابات المالية والتكاليف',
      inventory: 'إدارة المخازن',
      purchasing: 'إدارة المشتريات',
      treasury: 'إدارة الخزينة',
      paper_warehouse: 'مخازن الورق',
      reports: 'التقارير والمراجعة'
    }
  });
});

// اختبار الاتصال
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'النظام يعمل بشكل طبيعي'
  });
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log('🚀 خادم الاختبار يعمل على المنفذ', PORT);
  console.log('🌐 افتح المتصفح على: http://localhost:' + PORT);
  console.log('');
  console.log('📋 الخطوات التالية:');
  console.log('1. قم بإعداد قاعدة البيانات: npm run setup');
  console.log('2. شغل النظام الكامل: npm start');
  console.log('3. سجل دخول باستخدام: admin / admin123');
});

module.exports = app;
