const express = require('express');
const { executeQuery, executeTransaction } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('treasury', 'financial', 'audit'));

// الحصول على جميع الخزائن
router.get('/', async (req, res) => {
  try {
    const { type, active_only = 'true' } = req.query;
    
    let whereClause = '';
    let params = [];

    if (type) {
      whereClause += 'WHERE treasury_type = ?';
      params.push(type);
    }

    if (active_only === 'true') {
      whereClause += whereClause ? ' AND is_active = TRUE' : 'WHERE is_active = TRUE';
    }

    const treasuries = await executeQuery(`
      SELECT t.*, u.full_name as keeper_name
      FROM treasuries t
      LEFT JOIN users u ON t.keeper_id = u.id
      ${whereClause}
      ORDER BY t.treasury_code
    `, params);

    res.json({
      success: true,
      data: { treasuries }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الخزائن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على حركات الخزينة
router.get('/transactions', async (req, res) => {
  try {
    const { 
      treasury_id, 
      transaction_type, 
      status, 
      start_date, 
      end_date, 
      page = 1, 
      limit = 50 
    } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (treasury_id) {
      whereClause += 'WHERE tt.treasury_id = ?';
      params.push(treasury_id);
    }

    if (transaction_type) {
      whereClause += whereClause ? ' AND tt.transaction_type = ?' : 'WHERE tt.transaction_type = ?';
      params.push(transaction_type);
    }

    if (status) {
      whereClause += whereClause ? ' AND tt.status = ?' : 'WHERE tt.status = ?';
      params.push(status);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND tt.transaction_date >= ?' : 'WHERE tt.transaction_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND tt.transaction_date <= ?' : 'WHERE tt.transaction_date <= ?';
      params.push(end_date);
    }

    const transactions = await executeQuery(`
      SELECT 
        tt.*,
        t.treasury_name,
        t.treasury_type,
        creator.full_name as created_by_name,
        approver.full_name as approved_by_name,
        fv.voucher_number
      FROM treasury_transactions tt
      JOIN treasuries t ON tt.treasury_id = t.id
      LEFT JOIN users creator ON tt.created_by = creator.id
      LEFT JOIN users approver ON tt.approved_by = approver.id
      LEFT JOIN financial_vouchers fv ON tt.voucher_id = fv.id
      ${whereClause}
      ORDER BY tt.transaction_date DESC, tt.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد الحركات الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM treasury_transactions tt ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على حركات الخزينة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء حركة خزينة جديدة
router.post('/transactions', auditLog('INSERT', 'treasury_transactions'), async (req, res) => {
  try {
    const { 
      treasury_id, 
      transaction_type, 
      amount, 
      description, 
      reference_number, 
      voucher_id, 
      beneficiary_name, 
      payment_method, 
      check_number, 
      bank_name 
    } = req.body;

    if (!treasury_id || !transaction_type || !amount || !description) {
      return res.status(400).json({
        success: false,
        message: 'رقم الخزينة ونوع الحركة والمبلغ والوصف مطلوبة'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'المبلغ يجب أن يكون أكبر من صفر'
      });
    }

    // التحقق من وجود الخزينة
    const treasuries = await executeQuery(
      'SELECT * FROM treasuries WHERE id = ? AND is_active = TRUE',
      [treasury_id]
    );

    if (!treasuries.length) {
      return res.status(404).json({
        success: false,
        message: 'الخزينة غير موجودة أو غير مفعلة'
      });
    }

    const treasury = treasuries[0];

    // التحقق من الرصيد في حالة الصرف
    if (transaction_type === 'payment' && treasury.current_balance < amount) {
      return res.status(400).json({
        success: false,
        message: 'الرصيد المتاح في الخزينة غير كافي'
      });
    }

    // إنشاء رقم الحركة
    const currentYear = new Date().getFullYear();
    const transactionCount = await executeQuery(
      'SELECT COUNT(*) as count FROM treasury_transactions WHERE treasury_id = ? AND YEAR(transaction_date) = ?',
      [treasury_id, currentYear]
    );
    
    const transactionNumber = `${transaction_type.toUpperCase()}-${treasury.treasury_code}-${currentYear}-${String(transactionCount[0].count + 1).padStart(4, '0')}`;

    // بدء المعاملة
    const queries = [
      {
        query: `INSERT INTO treasury_transactions 
                (transaction_number, treasury_id, transaction_date, transaction_type, amount, description, 
                 reference_number, voucher_id, beneficiary_name, payment_method, check_number, bank_name, created_by) 
                VALUES (?, ?, CURDATE(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [
          transactionNumber, treasury_id, transaction_type, amount, description,
          reference_number, voucher_id, beneficiary_name, payment_method,
          check_number, bank_name, req.user.id
        ]
      }
    ];

    // تحديث رصيد الخزينة
    const balanceChange = transaction_type === 'receipt' ? amount : -amount;
    queries.push({
      query: 'UPDATE treasuries SET current_balance = current_balance + ? WHERE id = ?',
      params: [balanceChange, treasury_id]
    });

    const results = await executeTransaction(queries);
    const transactionId = results[0].insertId;

    req.recordId = transactionId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء حركة الخزينة بنجاح',
      data: { 
        transactionId,
        transactionNumber,
        newBalance: treasury.current_balance + balanceChange
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء حركة الخزينة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث حالة حركة الخزينة
router.put('/transactions/:id/status', auditLog('UPDATE', 'treasury_transactions'), async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'حالة الحركة مطلوبة'
      });
    }

    // الحصول على الحركة الحالية
    const transactions = await executeQuery(
      'SELECT * FROM treasury_transactions WHERE id = ?',
      [id]
    );

    if (!transactions.length) {
      return res.status(404).json({
        success: false,
        message: 'حركة الخزينة غير موجودة'
      });
    }

    const transaction = transactions[0];

    // التحقق من إمكانية تغيير الحالة
    if (transaction.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن تعديل حركة مكتملة'
      });
    }

    let updateFields = ['status = ?'];
    let updateValues = [status];

    // إضافة معتمد الحركة عند الاعتماد
    if (status === 'completed') {
      updateFields.push('approved_by = ?');
      updateValues.push(req.user.id);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await executeQuery(
      `UPDATE treasury_transactions SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    req.recordId = id;

    res.json({
      success: true,
      message: 'تم تحديث حالة حركة الخزينة بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث حالة حركة الخزينة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على رصيد الخزينة
router.get('/:id/balance', async (req, res) => {
  try {
    const { id } = req.params;
    const { date } = req.query;

    // الحصول على بيانات الخزينة
    const treasuries = await executeQuery(
      'SELECT * FROM treasuries WHERE id = ?',
      [id]
    );

    if (!treasuries.length) {
      return res.status(404).json({
        success: false,
        message: 'الخزينة غير موجودة'
      });
    }

    const treasury = treasuries[0];

    // حساب الرصيد حتى تاريخ معين إذا تم تحديده
    let balance = treasury.opening_balance;
    let whereClause = 'WHERE treasury_id = ? AND status = "completed"';
    let params = [id];

    if (date) {
      whereClause += ' AND transaction_date <= ?';
      params.push(date);
    }

    const transactions = await executeQuery(`
      SELECT transaction_type, SUM(amount) as total_amount
      FROM treasury_transactions
      ${whereClause}
      GROUP BY transaction_type
    `, params);

    let receipts = 0;
    let payments = 0;

    transactions.forEach(t => {
      if (t.transaction_type === 'receipt') {
        receipts = t.total_amount;
      } else if (t.transaction_type === 'payment') {
        payments = t.total_amount;
      }
    });

    const calculatedBalance = balance + receipts - payments;

    res.json({
      success: true,
      data: {
        treasury: {
          id: treasury.id,
          treasury_name: treasury.treasury_name,
          treasury_code: treasury.treasury_code,
          treasury_type: treasury.treasury_type
        },
        balance: {
          opening_balance: treasury.opening_balance,
          total_receipts: receipts,
          total_payments: payments,
          calculated_balance: calculatedBalance,
          current_balance: treasury.current_balance,
          as_of_date: date || new Date().toISOString().split('T')[0]
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على رصيد الخزينة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
