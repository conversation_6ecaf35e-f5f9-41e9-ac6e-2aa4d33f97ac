<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة</title>
    <style>
        body {
            font-family: Arial;
            background: #667eea;
            color: white;
            padding: 20px;
            direction: rtl;
        }
        .box {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }
        .hidden {
            display: none;
        }
        .demo {
            background: rgba(255,215,0,0.2);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            cursor: pointer;
            border: 1px solid gold;
        }
        .demo:hover {
            background: rgba(255,215,0,0.3);
        }
    </style>
</head>
<body>
    <div class="box">
        <!-- شاشة الدخول -->
        <div id="login">
            <h1>🏭 شركة مصر ادفو للورق</h1>
            <h2>نظام المحاسبة</h2>
            
            <div id="msg"></div>
            
            <input type="text" id="user" placeholder="اسم المستخدم">
            <input type="password" id="pass" placeholder="كلمة المرور">
            
            <button class="btn" onclick="login()">دخول</button>
            
            <div class="demo" onclick="fastLogin()">
                اضغط هنا للدخول السريع: admin / 123
            </div>
            
            <hr>
            <p>شركة ايه جي تكنولوجي سيستميز</p>
        </div>

        <!-- النظام الرئيسي -->
        <div id="main" class="hidden">
            <h1>🏭 نظام المحاسبة</h1>
            <p>مرحباً <span id="username">المستخدم</span></p>
            <button class="btn" onclick="logout()" style="background: red;">خروج</button>
            
            <hr>
            
            <h3>العمليات المتاحة:</h3>
            
            <button class="btn" onclick="invoice()">📄 فاتورة مبيعات</button>
            <button class="btn" onclick="purchase()">📦 فاتورة مشتريات</button>
            <button class="btn" onclick="reports()">📈 التقارير</button>
            <button class="btn" onclick="inventory()">🏪 المخازن</button>
            
            <hr>
            
            <button class="btn" onclick="printInv()" style="background: blue;">🖨️ طباعة فاتورة</button>
            <button class="btn" onclick="printRep()" style="background: blue;">🖨️ طباعة تقرير</button>
            
            <hr>
            <p>شركة ايه جي تكنولوجي سيستميز - AG Technology Systems</p>
        </div>
    </div>

    <script>
        function fastLogin() {
            document.getElementById('user').value = 'admin';
            document.getElementById('pass').value = '123';
            login();
        }

        function login() {
            var u = document.getElementById('user').value;
            var p = document.getElementById('pass').value;
            var msg = document.getElementById('msg');
            
            if (u == 'admin' && p == '123') {
                msg.innerHTML = '<p style="color: green;">تم الدخول بنجاح</p>';
                setTimeout(function() {
                    document.getElementById('login').className = 'hidden';
                    document.getElementById('main').className = '';
                    document.getElementById('username').innerHTML = 'مدير النظام';
                    alert('مرحباً بك في نظام المحاسبة!');
                }, 1000);
            } else {
                msg.innerHTML = '<p style="color: red;">خطأ في البيانات</p>';
            }
        }

        function logout() {
            if (confirm('هل تريد الخروج؟')) {
                document.getElementById('login').className = '';
                document.getElementById('main').className = 'hidden';
                document.getElementById('user').value = '';
                document.getElementById('pass').value = '';
                document.getElementById('msg').innerHTML = '';
            }
        }

        function invoice() {
            alert('فاتورة مبيعات\n\nالعميل: شركة الأهرام\nالمنتج: ورق 80 جرام\nالكمية: 100 طن\nالسعر: 25000 جنيه/طن\nالإجمالي: 2,500,000 جنيه\nالضريبة: 350,000 جنيه\nالمجموع: 2,850,000 جنيه\n\nرقم الفاتورة: ' + Date.now());
        }

        function purchase() {
            alert('فاتورة مشتريات\n\nالمورد: شركة المواد الخام\nالصنف: لب الورق\nالكمية: 50 طن\nالسعر: 15000 جنيه/طن\nالإجمالي: 750,000 جنيه\n\nتم التسجيل بنجاح');
        }

        function reports() {
            alert('التقارير المالية\n\nميزان المراجعة:\nالمدين: 12,750,000 جنيه\nالدائن: 12,750,000 جنيه\n\nقائمة الدخل:\nالمبيعات: 5,500,000 جنيه\nصافي الربح: 658,750 جنيه\n\nالميزانية:\nالأصول: 11,000,000 جنيه\nحقوق الملكية: 8,200,000 جنيه');
        }

        function inventory() {
            alert('أرصدة المخازن\n\nورق كتابة 80 جرام: 500 طن\nورق طباعة 70 جرام: 300 طن\nورق صحف 45 جرام: 200 طن\nلب الورق: 150 طن\n\nإجمالي القيمة: 25,250,000 جنيه');
        }

        function printInv() {
            var w = window.open('', '_blank');
            w.document.write('<html><head><meta charset="UTF-8"><title>فاتورة</title></head><body style="font-family:Arial;direction:rtl;padding:20px;"><h1>شركة مصر ادفو للورق</h1><h2>فاتورة مبيعات</h2><p>رقم الفاتورة: ' + Date.now() + '</p><p>التاريخ: ' + new Date().toLocaleDateString() + '</p><p>العميل: شركة الأهرام للطباعة</p><p>المنتج: ورق كتابة 80 جرام</p><p>الكمية: 100 طن</p><p>السعر: 25,000 جنيه/طن</p><p>الإجمالي: 2,850,000 جنيه</p><hr><p>شركة ايه جي تكنولوجي سيستميز</p><script>window.print();</script></body></html>');
            w.document.close();
        }

        function printRep() {
            var w = window.open('', '_blank');
            w.document.write('<html><head><meta charset="UTF-8"><title>تقرير</title></head><body style="font-family:Arial;direction:rtl;padding:20px;"><h1>شركة مصر ادفو للورق</h1><h2>ميزان المراجعة</h2><p>التاريخ: ' + new Date().toLocaleDateString() + '</p><table border="1" style="width:100%;border-collapse:collapse;"><tr><th>الحساب</th><th>مدين</th><th>دائن</th></tr><tr><td>الأصول الثابتة</td><td>6,000,000</td><td></td></tr><tr><td>الأصول المتداولة</td><td>5,000,000</td><td></td></tr><tr><td>الخصوم</td><td></td><td>1,800,000</td></tr><tr><td>رأس المال</td><td></td><td>4,000,000</td></tr><tr><td>المبيعات</td><td></td><td>5,500,000</td></tr><tr><td>تكلفة المبيعات</td><td>3,850,000</td><td></td></tr><tr><td>المصروفات</td><td>900,000</td><td></td></tr><tr><th>الإجمالي</th><th>15,750,000</th><th>15,750,000</th></tr></table><hr><p>شركة ايه جي تكنولوجي سيستميز</p><script>window.print();</script></body></html>');
            w.document.close();
        }

        // Enter للدخول
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && document.getElementById('login').className !== 'hidden') {
                login();
            }
        });
    </script>
</body>
</html>
