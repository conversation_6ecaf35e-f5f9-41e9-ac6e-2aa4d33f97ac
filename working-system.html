<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة - شركة مصر ادفو</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
        }
        .login-box {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            max-width: 400px;
            margin: 50px auto;
            text-align: center;
        }
        .main-content {
            display: none;
        }
        .tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        .tab:hover, .tab.active {
            background: rgba(255,215,0,0.3);
        }
        .section {
            display: none;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
        }
        .section.active {
            display: block;
        }
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }
        .demo-btn {
            background: rgba(255,215,0,0.2);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            border: 1px solid rgba(255,215,0,0.3);
        }
        .demo-btn:hover {
            background: rgba(255,215,0,0.3);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .table th {
            background: rgba(255,215,0,0.3);
            color: #333;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }
        .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #81C784);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen">
            <div class="header">
                <div class="logo">AG</div>
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            </div>
            
            <div class="login-box">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="message"></div>
                
                <div class="form-group">
                    <label>👤 اسم المستخدم:</label>
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>🔒 كلمة المرور:</label>
                    <input type="password" id="password" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()" style="width: 100%; padding: 15px;">🚀 دخول النظام</button>
                
                <div style="margin-top: 20px;">
                    <h4>📋 بيانات تجريبية:</h4>
                    <div class="demo-btn" onclick="fillLogin('admin', 'admin123')">
                        👨‍💼 مدير النظام: admin / admin123
                    </div>
                    <div class="demo-btn" onclick="fillLogin('accountant', 'acc123')">
                        👨‍💰 محاسب: accountant / acc123
                    </div>
                </div>
                
                <div class="footer">
                    <div class="logo" style="width: 40px; height: 40px; font-size: 14px;">AG</div>
                    <div>
                        <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                        <span style="font-size: 12px;">AG Technology Systems</span><br>
                        <span style="font-size: 10px;">© 2024 جميع الحقوق محفوظة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="main-content">
            <div class="header">
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
                <p>مرحباً <span id="currentUser">المستخدم</span> | <button class="btn btn-secondary" onclick="logout()">🚪 خروج</button></p>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('dashboard')">🏠 الرئيسية</button>
                <button class="tab" onclick="showTab('operations')">⚡ العمليات</button>
                <button class="tab" onclick="showTab('reports')">📈 التقارير</button>
                <button class="tab" onclick="showTab('inventory')">🏪 المخازن</button>
            </div>

            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم</h2>
                
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">4</div>
                        <div>المستخدمين</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">2.5M</div>
                        <div>المبيعات (جنيه)</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">150</div>
                        <div>الأصناف</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">85</div>
                        <div>العملاء</div>
                    </div>
                </div>

                <div class="cards">
                    <div class="card">
                        <h3>📄 فاتورة مبيعات</h3>
                        <p>إنشاء فاتورة مبيعات جديدة</p>
                        <button class="btn" onclick="showTab('operations')">إنشاء فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>📦 فاتورة مشتريات</h3>
                        <p>تسجيل فاتورة مشتريات</p>
                        <button class="btn" onclick="showTab('operations')">إدخال فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>🏪 إدارة المخازن</h3>
                        <p>متابعة المخزون</p>
                        <button class="btn" onclick="showTab('inventory')">إدارة المخازن</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير المالية</h3>
                        <p>عرض القوائم المالية</p>
                        <button class="btn" onclick="showTab('reports')">عرض التقارير</button>
                    </div>
                </div>
            </div>
