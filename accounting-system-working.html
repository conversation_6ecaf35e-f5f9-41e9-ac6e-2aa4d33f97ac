<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة والمراجعة الداخلية - شركة مصر ادفو</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
        }

        .tab:hover, .tab.active {
            background: rgba(255,215,0,0.3);
            transform: translateY(-2px);
        }

        .section {
            display: none;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }

        .section.active {
            display: block;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .form-group {
            margin: 15px 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }

        .form-group input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .data-table th, .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .data-table th {
            background: rgba(255,215,0,0.3);
            color: #333;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 50%, #A5D6A7 100%);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 0 auto 15px;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            margin-top: 10px;
            opacity: 0.9;
        }

        /* Login Screen Styles */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            margin-top: 10px;
        }

        .demo-accounts {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .demo-account {
            background: rgba(255,215,0,0.1);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255,215,0,0.3);
            cursor: pointer;
            transition: all 0.3s;
            text-align: right;
        }

        .demo-account:hover {
            background: rgba(255,215,0,0.2);
            transform: translateY(-2px);
        }

        .demo-account code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            color: #ffd700;
        }

        .error-message {
            background: rgba(255,0,0,0.2);
            border: 2px solid rgba(255,0,0,0.5);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            color: #ff6b6b;
        }

        .success-message {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="company-logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            <p>تسجيل الدخول للنظام</p>
            
            <div style="margin: 30px 0;">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="loginMessage"></div>
                <form id="mainLoginForm">
                    <div class="form-group">
                        <label for="loginUsername">👤 اسم المستخدم:</label>
                        <input type="text" id="loginUsername" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">🔒 كلمة المرور:</label>
                        <input type="password" id="loginPassword" placeholder="أدخل كلمة المرور" required>
                    </div>
                    <button type="submit" class="btn login-btn">🚀 دخول النظام</button>
                </form>
                
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-top: 20px; text-align: right;">
                    <h4>📋 بيانات تجريبية للدخول:</h4>
                    <div class="demo-accounts">
                        <div class="demo-account" onclick="fillLoginData('admin', 'admin123')">
                            <strong>👨‍💼 مدير النظام:</strong><br>
                            اسم المستخدم: <code>admin</code><br>
                            كلمة المرور: <code>admin123</code>
                        </div>
                        <div class="demo-account" onclick="fillLoginData('accountant', 'acc123')">
                            <strong>👨‍💰 محاسب:</strong><br>
                            اسم المستخدم: <code>accountant</code><br>
                            كلمة المرور: <code>acc123</code>
                        </div>
                        <div class="demo-account" onclick="fillLoginData('warehouse', 'wh123')">
                            <strong>👨‍🏭 مدير مخازن:</strong><br>
                            اسم المستخدم: <code>warehouse</code><br>
                            كلمة المرور: <code>wh123</code>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <div class="company-logo" style="width: 60px; height: 60px; font-size: 18px;">AG</div>
                    <div>
                        <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                        <span style="font-size: 14px;">AG Technology Systems</span>
                    </div>
                </div>
                <div style="font-size: 12px; opacity: 0.8; margin-top: 10px;">
                    © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز
                </div>
            </div>
        </div>
    </div>

    <!-- النظام الرئيسي -->
    <div class="container" id="mainSystem" style="display: none;">
        <div class="header">
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            <p>واجهة مستقلة - تعمل بدون خادم</p>
            <div style="margin-top: 20px;">
                <button class="btn btn-secondary" onclick="logout()" style="position: absolute; top: 20px; left: 20px;">🚪 تسجيل الخروج</button>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showSection('dashboard')">🏠 لوحة التحكم</button>
            <button class="tab" onclick="showSection('operations')">⚡ العمليات اليومية</button>
            <button class="tab" onclick="showSection('reports')">📈 التقارير</button>
            <button class="tab" onclick="showSection('financial')">💰 الحسابات المالية</button>
            <button class="tab" onclick="showSection('inventory')">🏪 المخازن</button>
            <button class="tab" onclick="showSection('settings')">⚙️ الإعدادات</button>
        </div>

        <div class="content">
            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم الرئيسية</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,500,000</div>
                        <div class="stat-label">إجمالي المبيعات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">150</div>
                        <div class="stat-label">الأصناف</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">85</div>
                        <div class="stat-label">العملاء</div>
                    </div>
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إصدار فاتورة مبيعات</h3>
                        <p>إنشاء فاتورة مبيعات جديدة مع جميع التفاصيل</p>
                        <button class="btn" onclick="createSalesInvoice()">📄 إصدار فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>📦 إدخال مشتريات</h3>
                        <p>تسجيل فاتورة مشتريات جديدة</p>
                        <button class="btn" onclick="createPurchaseInvoice()">📦 إدخال فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>🏪 حركة المخازن</h3>
                        <p>إضافة أو صرف من المخزون</p>
                        <button class="btn" onclick="showSection('inventory')">🏪 إدارة المخازن</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير المالية</h3>
                        <p>عرض وطباعة جميع التقارير والقوائم المالية</p>
                        <button class="btn" onclick="showSection('reports')">📈 عرض التقارير</button>
                    </div>
                </div>
            </div>

            <!-- العمليات اليومية -->
            <div id="operations" class="section">
                <h2>⚡ العمليات اليومية</h2>

                <div class="alert">
                    <strong>العمليات اليومية:</strong> إدخال وإدارة جميع العمليات اليومية للشركة
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إصدار فاتورة مبيعات</h3>
                        <div class="form-group">
                            <label>العميل:</label>
                            <select id="salesCustomer">
                                <option value="">اختر العميل</option>
                                <option value="customer1">شركة الأهرام للطباعة</option>
                                <option value="customer2">مؤسسة النيل للنشر</option>
                                <option value="customer3">شركة المستقبل للورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المنتج:</label>
                            <select id="salesProduct">
                                <option value="">اختر المنتج</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="paper45">ورق صحف 45 جرام</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية (طن):</label>
                            <input type="number" id="salesQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر (جنيه/طن):</label>
                            <input type="number" id="salesPrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createSalesInvoice()">📄 إصدار الفاتورة</button>
                        <button class="btn btn-secondary" onclick="printLastInvoice()">🖨️ طباعة</button>
                    </div>

                    <div class="card">
                        <h3>📦 إدخال مشتريات</h3>
                        <div class="form-group">
                            <label>المورد:</label>
                            <select id="purchaseSupplier">
                                <option value="">اختر المورد</option>
                                <option value="supplier1">شركة المواد الخام المصرية</option>
                                <option value="supplier2">مؤسسة الكيماويات الصناعية</option>
                                <option value="supplier3">شركة الوقود والطاقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="purchaseItem">
                                <option value="">اختر الصنف</option>
                                <option value="pulp">لب الورق</option>
                                <option value="chemicals">كيماويات</option>
                                <option value="fuel">وقود ديزل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية:</label>
                            <input type="number" id="purchaseQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر:</label>
                            <input type="number" id="purchasePrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createPurchaseInvoice()">📦 إدخال الفاتورة</button>
                    </div>

                    <div class="card">
                        <h3>📥 إضافة للمخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="addInventoryItem">
                                <option value="">اختر الصنف</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="pulp">لب الورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المضافة:</label>
                            <input type="number" id="addInventoryQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>سبب الإضافة:</label>
                            <select id="addInventoryReason">
                                <option value="production">إنتاج</option>
                                <option value="purchase">مشتريات</option>
                                <option value="return">مرتجع</option>
                            </select>
                        </div>
                        <button class="btn" onclick="addToInventory()">📥 إضافة للمخزون</button>
                    </div>

                    <div class="card">
                        <h3>📤 صرف من المخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="issueInventoryItem">
                                <option value="">اختر الصنف</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="pulp">لب الورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المصروفة:</label>
                            <input type="number" id="issueInventoryQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>سبب الصرف:</label>
                            <select id="issueInventoryReason">
                                <option value="sales">مبيعات</option>
                                <option value="production">إنتاج</option>
                                <option value="damage">تالف</option>
                            </select>
                        </div>
                        <button class="btn" onclick="issueFromInventory()">📤 صرف من المخزون</button>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <h2>📈 التقارير والقوائم المالية</h2>

                <h3>📊 القوائم المالية الأساسية</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p>إجمالي المدين: 12,750,000 جنيه</p>
                        <p>إجمالي الدائن: 12,750,000 جنيه</p>
                        <button class="btn" onclick="showTrialBalance()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printTrialBalance()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📋 قائمة الدخل</h3>
                        <p>إيرادات المبيعات: 5,500,000 جنيه</p>
                        <p>صافي الربح: 658,750 جنيه</p>
                        <button class="btn" onclick="showIncomeStatement()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printIncomeStatement()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📊 قائمة المركز المالي</h3>
                        <p>إجمالي الأصول: 11,000,000 جنيه</p>
                        <p>حقوق الملكية: 8,200,000 جنيه</p>
                        <button class="btn" onclick="showBalanceSheet()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printBalanceSheet()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>💰 قائمة التدفقات النقدية</h3>
                        <p>التدفق من العمليات: 958,750 جنيه</p>
                        <p>صافي التدفق النقدي: 458,750 جنيه</p>
                        <button class="btn" onclick="showCashFlow()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printCashFlow()">🖨️ طباعة</button>
                    </div>
                </div>
            </div>

            <!-- الحسابات المالية -->
            <div id="financial" class="section">
                <h2>💰 الحسابات المالية</h2>

                <div class="alert">
                    <strong>الحسابات المالية:</strong> إدارة شجرة الحسابات والقيود المحاسبية
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>🌳 شجرة الحسابات</h3>
                        <p>إدارة وتنظيم جميع الحسابات المالية</p>
                        <button class="btn" onclick="showChartOfAccounts()">عرض الحسابات</button>
                    </div>
                    <div class="card">
                        <h3>📝 القيود المحاسبية</h3>
                        <p>إدخال وإدارة القيود اليومية</p>
                        <button class="btn" onclick="showJournalEntries()">عرض القيود</button>
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <h2>🏪 إدارة المخازن</h2>

                <div class="alert">
                    <strong>إدارة المخازن:</strong> متابعة المخزون وحركة الأصناف
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الكمية الحالية</th>
                            <th>الوحدة</th>
                            <th>القيمة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>P001</td>
                            <td>ورق كتابة 80 جرام</td>
                            <td>500</td>
                            <td>طن</td>
                            <td>12,500,000</td>
                        </tr>
                        <tr>
                            <td>P002</td>
                            <td>ورق طباعة 70 جرام</td>
                            <td>300</td>
                            <td>طن</td>
                            <td>6,900,000</td>
                        </tr>
                        <tr>
                            <td>P003</td>
                            <td>ورق صحف 45 جرام</td>
                            <td>200</td>
                            <td>طن</td>
                            <td>3,600,000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- الإعدادات -->
            <div id="settings" class="section">
                <h2>⚙️ إعدادات النظام</h2>

                <div class="cards-grid">
                    <div class="card">
                        <h3>👤 بيانات المستخدم</h3>
                        <p id="currentUserInfo">المستخدم الحالي: غير محدد</p>
                        <button class="btn btn-secondary" onclick="logout()">🚪 تسجيل الخروج</button>
                    </div>
                    <div class="card">
                        <h3>🏢 بيانات الشركة</h3>
                        <p><strong>الاسم:</strong> شركة مصر ادفو للب وورق الكتابة والطباعة</p>
                        <p><strong>النشاط:</strong> تصنيع الورق ومنتجاته</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- فوتر النظام -->
        <div class="footer">
            <div class="company-logo">AG</div>
            <div>
                <h3 style="color: #ffd700; margin: 10px 0;">🏢 شركة ايه جي تكنولوجي سيستميز</h3>
                <p style="margin: 5px 0;">AG Technology Systems</p>
                <p style="margin: 5px 0; font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">© 2024 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </div>

    <script>
        // بيانات المستخدمين
        const loginUsers = {
            'admin': { password: 'admin123', name: 'مدير النظام', role: 'admin' },
            'accountant': { password: 'acc123', name: 'محاسب رئيسي', role: 'accountant' },
            'warehouse': { password: 'wh123', name: 'مدير المخازن', role: 'warehouse' }
        };

        let currentUser = null;

        // دالة ملء بيانات الدخول
        function fillLoginData(username, password) {
            document.getElementById('loginUsername').value = username;
            document.getElementById('loginPassword').value = password;
        }

        // دالة تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();
            const messageDiv = document.getElementById('loginMessage');

            if (!username || !password) {
                showLoginMessage('❌ يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            if (loginUsers[username] && loginUsers[username].password === password) {
                currentUser = { username: username, ...loginUsers[username] };
                showLoginMessage('✅ تم تسجيل الدخول بنجاح...', 'success');

                setTimeout(() => {
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('mainSystem').style.display = 'block';
                    updateUserInterface();

                    alert(`🎉 مرحباً ${currentUser.name}!\n\nتم تسجيل الدخول بنجاح إلى نظام المحاسبة والمراجعة الداخلية\n\n✅ يمكنك الآن:\n• إصدار فواتير المبيعات\n• إدخال المشتريات\n• إدارة المخازن\n• عرض وطباعة التقارير المالية\n• إدخال القيود المحاسبية\n\n🏢 شركة ايه جي تكنولوجي سيستميز`);
                }, 1500);

            } else {
                showLoginMessage('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        // دالة عرض رسائل تسجيل الدخول
        function showLoginMessage(message, type) {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.innerHTML = message;
            messageDiv.style.display = 'block';

            if (type === 'error') {
                setTimeout(() => messageDiv.style.display = 'none', 3000);
            }
        }

        // دالة تحديث واجهة المستخدم
        function updateUserInterface() {
            const userInfo = document.getElementById('currentUserInfo');
            if (userInfo) {
                userInfo.innerHTML = `المستخدم الحالي: ${currentUser.name}<br>الدور: ${currentUser.role}<br>وقت الدخول: ${new Date().toLocaleString('ar-EG')}`;
            }
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                currentUser = null;
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainSystem').style.display = 'none';
                document.getElementById('loginUsername').value = '';
                document.getElementById('loginPassword').value = '';
                document.getElementById('loginMessage').style.display = 'none';
            }
        }

        // دالة عرض الأقسام
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));

            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إظهار القسم المطلوب
            document.getElementById(sectionId).classList.add('active');

            // تفعيل التبويب المطلوب
            event.target.classList.add('active');
        }

        // دوال العمليات اليومية
        function createSalesInvoice() {
            const customer = document.getElementById('salesCustomer')?.value || 'شركة الأهرام للطباعة';
            const product = document.getElementById('salesProduct')?.value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('salesQuantity')?.value || '100';
            const price = document.getElementById('salesPrice')?.value || '25000';

            const total = quantity * price;
            const tax = total * 0.14;
            const grandTotal = total + tax;

            const invoiceData = {
                invoiceNumber: 'INV-' + Date.now(),
                customer: customer,
                product: product,
                quantity: quantity,
                price: price,
                total: total.toLocaleString(),
                tax: tax.toLocaleString(),
                grandTotal: grandTotal.toLocaleString()
            };

            if (confirm(`تم إنشاء فاتورة مبيعات:\n\nالعميل: ${customer}\nالمنتج: ${product}\nالكمية: ${quantity} طن\nالسعر: ${price} جنيه/طن\nالإجمالي: ${grandTotal.toLocaleString()} جنيه\n\nهل تريد طباعة الفاتورة؟`)) {
                printSalesInvoice(invoiceData);
            }

            // مسح النموذج
            if (document.getElementById('salesCustomer')) document.getElementById('salesCustomer').value = '';
            if (document.getElementById('salesProduct')) document.getElementById('salesProduct').value = '';
            if (document.getElementById('salesQuantity')) document.getElementById('salesQuantity').value = '';
            if (document.getElementById('salesPrice')) document.getElementById('salesPrice').value = '';
        }

        function createPurchaseInvoice() {
            const supplier = document.getElementById('purchaseSupplier')?.value || 'شركة المواد الخام المصرية';
            const item = document.getElementById('purchaseItem')?.value || 'لب الورق';
            const quantity = document.getElementById('purchaseQuantity')?.value || '50';
            const price = document.getElementById('purchasePrice')?.value || '15000';

            const total = quantity * price;

            alert(`تم إدخال فاتورة مشتريات:\n\nالمورد: ${supplier}\nالصنف: ${item}\nالكمية: ${quantity}\nالسعر: ${price}\nالإجمالي: ${total.toLocaleString()} جنيه\n\n✅ تم تسجيل الفاتورة بنجاح`);

            // مسح النموذج
            if (document.getElementById('purchaseSupplier')) document.getElementById('purchaseSupplier').value = '';
            if (document.getElementById('purchaseItem')) document.getElementById('purchaseItem').value = '';
            if (document.getElementById('purchaseQuantity')) document.getElementById('purchaseQuantity').value = '';
            if (document.getElementById('purchasePrice')) document.getElementById('purchasePrice').value = '';
        }

        function addToInventory() {
            const item = document.getElementById('addInventoryItem')?.value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('addInventoryQuantity')?.value || '10';
            const reason = document.getElementById('addInventoryReason')?.value || 'إنتاج';

            alert(`تم إضافة للمخزون:\n\nالصنف: ${item}\nالكمية المضافة: ${quantity}\nسبب الإضافة: ${reason}\n\n✅ تم تحديث المخزون بنجاح`);

            // مسح النموذج
            if (document.getElementById('addInventoryItem')) document.getElementById('addInventoryItem').value = '';
            if (document.getElementById('addInventoryQuantity')) document.getElementById('addInventoryQuantity').value = '';
            if (document.getElementById('addInventoryReason')) document.getElementById('addInventoryReason').value = '';
        }

        function issueFromInventory() {
            const item = document.getElementById('issueInventoryItem')?.value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('issueInventoryQuantity')?.value || '5';
            const reason = document.getElementById('issueInventoryReason')?.value || 'مبيعات';

            alert(`تم صرف من المخزون:\n\nالصنف: ${item}\nالكمية المصروفة: ${quantity}\nسبب الصرف: ${reason}\n\n✅ تم تحديث المخزون بنجاح`);

            // مسح النموذج
            if (document.getElementById('issueInventoryItem')) document.getElementById('issueInventoryItem').value = '';
            if (document.getElementById('issueInventoryQuantity')) document.getElementById('issueInventoryQuantity').value = '';
            if (document.getElementById('issueInventoryReason')) document.getElementById('issueInventoryReason').value = '';
        }

        // دوال التقارير
        function showTrialBalance() {
            const content = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
⚖️ ميزان المراجعة

التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

رقم الحساب | اسم الحساب                    | مدين        | دائن
─────────────────────────────────────────────────────────
1000       | الأصول الثابتة               | 6,000,000   |
1100       | الأصول المتداولة             | 5,000,000   |
1110       | النقدية والبنوك              | 650,000     |
1120       | العملاء                      | 1,450,000   |
1130       | المخزون                      | 2,775,000   |
2000       | الخصوم المتداولة             |             | 1,800,000
2100       | الموردون                     |             | 980,000
2200       | مصروفات مستحقة               |             | 220,000
3000       | رأس المال                    |             | 4,000,000
3100       | الأرباح المحتجزة             |             | 3,541,250
4000       | إيرادات المبيعات             |             | 5,500,000
5000       | تكلفة البضاعة المباعة        | 3,850,000   |
6000       | مصروفات التشغيل              | 900,000     |
7000       | مصروفات إدارية               | 350,000     |

─────────────────────────────────────────────────────────
الإجمالي                              | 16,750,000  | 16,750,000

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(content);
        }

        function showIncomeStatement() {
            const content = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📋 قائمة الدخل

التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

الإيرادات:
• إيرادات المبيعات                    5,500,000 جنيه
• إيرادات أخرى                        100,000 جنيه
─────────────────────────────────────
إجمالي الإيرادات                      5,600,000 جنيه

التكاليف والمصروفات:
• تكلفة البضاعة المباعة               3,850,000 جنيه
─────────────────────────────────────
مجمل الربح                           1,750,000 جنيه

المصروفات التشغيلية:
• مصروفات البيع والتوزيع              450,000 جنيه
• مصروفات إدارية وعمومية             350,000 جنيه
• مصروفات أخرى                       100,000 جنيه
─────────────────────────────────────
إجمالي المصروفات التشغيلية           900,000 جنيه

صافي الربح قبل الضرائب               850,000 جنيه
• ضرائب الدخل (22.5%)                191,250 جنيه
─────────────────────────────────────
صافي الربح بعد الضرائب               658,750 جنيه

═══════════════════════════════════════
هامش الربح الإجمالي: 31.25%
هامش الربح الصافي: 11.76%

شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(content);
        }

        function showBalanceSheet() {
            const content = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📊 قائمة المركز المالي (الميزانية العمومية)

التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

الأصول:

الأصول غير المتداولة:
• الأراضي والمباني                   2,500,000 جنيه
• الآلات والمعدات                    2,800,000 جنيه
• وسائل النقل                        450,000 جنيه
• أصول أخرى                          250,000 جنيه
─────────────────────────────────────
إجمالي الأصول غير المتداولة          6,000,000 جنيه

الأصول المتداولة:
• النقدية والبنوك                    650,000 جنيه
• العملاء                           1,450,000 جنيه
• المخزون                           2,775,000 جنيه
• مصروفات مقدمة                      125,000 جنيه
─────────────────────────────────────
إجمالي الأصول المتداولة             5,000,000 جنيه

إجمالي الأصول                       11,000,000 جنيه

═══════════════════════════════════════

الخصوم وحقوق الملكية:

الخصوم المتداولة:
• الموردون                          980,000 جنيه
• مصروفات مستحقة                    220,000 جنيه
• ضرائب مستحقة                      150,000 جنيه
• قروض قصيرة الأجل                  450,000 جنيه
─────────────────────────────────────
إجمالي الخصوم المتداولة             1,800,000 جنيه

الخصوم طويلة الأجل:
• قروض طويلة الأجل                  1,000,000 جنيه
─────────────────────────────────────
إجمالي الخصوم                       2,800,000 جنيه

حقوق الملكية:
• رأس المال                         4,000,000 جنيه
• الأرباح المحتجزة                  3,541,250 جنيه
• أرباح العام الجاري                658,750 جنيه
─────────────────────────────────────
إجمالي حقوق الملكية                 8,200,000 جنيه

إجمالي الخصوم وحقوق الملكية         11,000,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(content);
        }

        function showCashFlow() {
            const content = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
💰 قائمة التدفقات النقدية

التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

التدفقات النقدية من الأنشطة التشغيلية:
• صافي الربح                        658,750 جنيه
• الاستهلاك                         450,000 جنيه
• التغير في العملاء                 (150,000) جنيه
• التغير في المخزون                 (200,000) جنيه
• التغير في الموردين                120,000 جنيه
• التغير في المصروفات المستحقة       80,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التشغيلية    958,750 جنيه

التدفقات النقدية من الأنشطة الاستثمارية:
• شراء أصول ثابتة                   (350,000) جنيه
• بيع أصول ثابتة                    50,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة الاستثمارية  (300,000) جنيه

التدفقات النقدية من الأنشطة التمويلية:
• قروض جديدة                        200,000 جنيه
• سداد قروض                         (150,000) جنيه
• توزيعات أرباح                     (250,000) جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التمويلية    (200,000) جنيه

صافي التغير في النقدية              458,750 جنيه
رصيد النقدية أول الفترة             191,250 جنيه
─────────────────────────────────────
رصيد النقدية آخر الفترة             650,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(content);
        }

        // دوال الطباعة
        function printDocument(title, content) {
            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG');

            const printContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding-bottom: 20px; margin-bottom: 30px; }
        .company-logo { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #4CAF50, #81C784); display: inline-flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; color: white; margin-bottom: 15px; }
        .company-name { font-size: 24px; font-weight: bold; color: #4CAF50; margin: 10px 0; }
        .document-title { font-size: 20px; font-weight: bold; color: #333; margin: 15px 0; }
        .content { margin: 20px 0; white-space: pre-line; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding: 10px; background: white; }
        @media print { .no-print { display: none !important; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-logo">AG</div>
        <div class="company-name">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</div>
        <div class="document-title">${title}</div>
        <div>التاريخ: ${currentDate} | الوقت: ${currentTime}</div>
    </div>
    <div class="content">${content}</div>
    <div class="footer">
        <strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems<br>
        تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة
    </div>
    <script>
        window.onload = function() {
            window.print();
            setTimeout(function() { window.close(); }, 1000);
        }
    </script>
</body>
</html>`;

            printWindow.document.write(printContent);
            printWindow.document.close();
        }

        function printTrialBalance() {
            const content = showTrialBalance();
            printDocument('⚖️ ميزان المراجعة', content);
        }

        function printIncomeStatement() {
            const content = showIncomeStatement();
            printDocument('📋 قائمة الدخل', content);
        }

        function printBalanceSheet() {
            const content = showBalanceSheet();
            printDocument('📊 قائمة المركز المالي', content);
        }

        function printCashFlow() {
            const content = showCashFlow();
            printDocument('💰 قائمة التدفقات النقدية', content);
        }

        function printSalesInvoice(invoiceData) {
            const content = `
فاتورة مبيعات رقم: ${invoiceData.invoiceNumber}

بيانات العميل:
العميل: ${invoiceData.customer}
العنوان: القاهرة - مصر الجديدة
الرقم الضريبي: *********

تفاصيل الفاتورة:
المنتج: ${invoiceData.product}
الكمية: ${invoiceData.quantity} طن
السعر: ${invoiceData.price} جنيه/طن
الإجمالي قبل الضريبة: ${invoiceData.total} جنيه
ضريبة القيمة المضافة (14%): ${invoiceData.tax} جنيه
الإجمالي شامل الضريبة: ${invoiceData.grandTotal} جنيه

شروط السداد: 30 يوم من تاريخ الفاتورة
طريقة السداد: شيكات مؤجلة
            `;
            printDocument('📄 فاتورة مبيعات', content);
        }

        function printLastInvoice() {
            const invoiceData = {
                invoiceNumber: 'INV-' + Date.now(),
                customer: 'شركة الأهرام للطباعة',
                product: 'ورق كتابة 80 جرام',
                quantity: '100',
                price: '25,000',
                total: '2,500,000',
                tax: '350,000',
                grandTotal: '2,850,000'
            };
            printSalesInvoice(invoiceData);
        }

        // دوال أخرى
        function showChartOfAccounts() {
            alert('🌳 شجرة الحسابات\n\n1000 - الأصول\n  1100 - الأصول المتداولة\n    1110 - النقدية والبنوك\n    1120 - العملاء\n    1130 - المخزون\n  1200 - الأصول الثابتة\n\n2000 - الخصوم\n  2100 - الموردون\n  2200 - مصروفات مستحقة\n\n3000 - حقوق الملكية\n  3100 - رأس المال\n  3200 - الأرباح المحتجزة\n\n4000 - الإيرادات\n  4100 - إيرادات المبيعات\n\n5000 - المصروفات\n  5100 - تكلفة البضاعة المباعة\n  5200 - مصروفات التشغيل');
        }

        function showJournalEntries() {
            alert('📝 القيود المحاسبية\n\nآخر القيود المسجلة:\n\n1. من ح/ العملاء\n   إلى ح/ المبيعات\n   مبلغ: 2,500,000 جنيه\n   البيان: فاتورة مبيعات رقم INV-001\n\n2. من ح/ تكلفة البضاعة المباعة\n   إلى ح/ المخزون\n   مبلغ: 1,750,000 جنيه\n   البيان: تكلفة البضاعة المباعة\n\n3. من ح/ النقدية\n   إلى ح/ العملاء\n   مبلغ: 1,200,000 جنيه\n   البيان: تحصيل من العملاء');
        }

        // ربط الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('mainLoginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
        });

        // رسالة ترحيب
        console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
        console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
    </script>
</body>
</html>
