const express = require('express');
const { executeQuery, executeTransaction } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('financial', 'audit'));

// الحصول على جميع الحسابات المالية
router.get('/accounts', async (req, res) => {
  try {
    const accounts = await executeQuery(`
      SELECT fa.*, parent.account_name as parent_account_name
      FROM financial_accounts fa
      LEFT JOIN financial_accounts parent ON fa.parent_account_id = parent.id
      WHERE fa.is_active = TRUE
      ORDER BY fa.account_code
    `);

    res.json({
      success: true,
      data: { accounts }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الحسابات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء حساب مالي جديد
router.post('/accounts', auditLog('INSERT', 'financial_accounts'), async (req, res) => {
  try {
    const { account_code, account_name, account_type, parent_account_id } = req.body;

    if (!account_code || !account_name || !account_type) {
      return res.status(400).json({
        success: false,
        message: 'رقم الحساب واسم الحساب ونوع الحساب مطلوبة'
      });
    }

    // التحقق من عدم تكرار رقم الحساب
    const existingAccount = await executeQuery(
      'SELECT id FROM financial_accounts WHERE account_code = ?',
      [account_code]
    );

    if (existingAccount.length) {
      return res.status(400).json({
        success: false,
        message: 'رقم الحساب موجود بالفعل'
      });
    }

    const result = await executeQuery(
      `INSERT INTO financial_accounts (account_code, account_name, account_type, parent_account_id) 
       VALUES (?, ?, ?, ?)`,
      [account_code, account_name, account_type, parent_account_id || null]
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الحساب بنجاح',
      data: { accountId: result.insertId }
    });

  } catch (error) {
    console.error('خطأ في إنشاء الحساب:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على جميع أذونات الصرف
router.get('/vouchers', async (req, res) => {
  try {
    const { status, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause = 'WHERE fv.status = ?';
      params.push(status);
    }

    const vouchers = await executeQuery(`
      SELECT fv.*, 
             creator.full_name as created_by_name,
             reviewer.full_name as reviewed_by_name,
             approver.full_name as approved_by_name,
             fm_approver.full_name as financial_manager_name
      FROM financial_vouchers fv
      LEFT JOIN users creator ON fv.created_by = creator.id
      LEFT JOIN users reviewer ON fv.reviewed_by = reviewer.id
      LEFT JOIN users approver ON fv.approved_by = approver.id
      LEFT JOIN users fm_approver ON fv.financial_manager_approved_by = fm_approver.id
      ${whereClause}
      ORDER BY fv.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد الأذونات الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM financial_vouchers fv ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        vouchers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الأذونات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء إذن صرف جديد
router.post('/vouchers', auditLog('INSERT', 'financial_vouchers'), async (req, res) => {
  try {
    const { 
      voucher_type, 
      amount, 
      description, 
      supporting_documents, 
      cost_center 
    } = req.body;

    if (!voucher_type || !amount || !description) {
      return res.status(400).json({
        success: false,
        message: 'نوع الإذن والمبلغ والوصف مطلوبة'
      });
    }

    // إنشاء رقم الإذن
    const currentYear = new Date().getFullYear();
    const voucherCount = await executeQuery(
      'SELECT COUNT(*) as count FROM financial_vouchers WHERE YEAR(created_at) = ?',
      [currentYear]
    );
    
    const voucherNumber = `${voucher_type.toUpperCase()}-${currentYear}-${String(voucherCount[0].count + 1).padStart(4, '0')}`;

    const result = await executeQuery(
      `INSERT INTO financial_vouchers 
       (voucher_number, voucher_type, amount, description, supporting_documents, cost_center, created_by) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [voucherNumber, voucher_type, amount, description, supporting_documents, cost_center, req.user.id]
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الإذن بنجاح',
      data: { 
        voucherId: result.insertId,
        voucherNumber 
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء الإذن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث حالة الإذن (مراجعة/اعتماد)
router.put('/vouchers/:id/status', auditLog('UPDATE', 'financial_vouchers'), async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'حالة الإذن مطلوبة'
      });
    }

    // الحصول على الإذن الحالي
    const vouchers = await executeQuery(
      'SELECT * FROM financial_vouchers WHERE id = ?',
      [id]
    );

    if (!vouchers.length) {
      return res.status(404).json({
        success: false,
        message: 'الإذن غير موجود'
      });
    }

    const voucher = vouchers[0];
    let updateFields = ['status = ?'];
    let updateValues = [status];

    // تحديد الحقول حسب الحالة الجديدة
    switch (status) {
      case 'pending_review':
        if (voucher.status !== 'draft') {
          return res.status(400).json({
            success: false,
            message: 'لا يمكن إرسال الإذن للمراجعة إلا من حالة المسودة'
          });
        }
        break;

      case 'approved':
        if (voucher.status !== 'pending_review') {
          return res.status(400).json({
            success: false,
            message: 'لا يمكن اعتماد الإذن إلا بعد المراجعة'
          });
        }
        updateFields.push('reviewed_by = ?', 'approved_by = ?');
        updateValues.push(req.user.id, req.user.id);
        break;

      case 'rejected':
        updateFields.push('reviewed_by = ?');
        updateValues.push(req.user.id);
        break;
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await executeQuery(
      `UPDATE financial_vouchers SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    req.recordId = id;

    res.json({
      success: true,
      message: 'تم تحديث حالة الإذن بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث حالة الإذن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
