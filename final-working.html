<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة</title>
    <style>
        body {
            font-family: Arial;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #4CAF50, #81C784);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-blue {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        .btn-red {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            box-sizing: border-box;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        .quick-login {
            background: rgba(255,215,0,0.3);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            border: 2px solid rgba(255,215,0,0.7);
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        .quick-login:hover {
            background: rgba(255,215,0,0.5);
            transform: scale(1.02);
        }
        .hidden {
            display: none;
        }
        .message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
        .success {
            background: rgba(76,175,80,0.3);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        .error {
            background: rgba(244,67,54,0.3);
            border: 2px solid #f44336;
            color: #f44336;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 5px;
        }
        .operations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .operation-card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .operation-card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-top: 2px solid rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen">
            <div class="logo">AG</div>
            <h1 style="text-align: center; margin-bottom: 10px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2 style="text-align: center; margin-bottom: 30px;">🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            
            <div id="loginMessage"></div>
            
            <input type="text" id="username" placeholder="👤 اسم المستخدم">
            <input type="password" id="password" placeholder="🔒 كلمة المرور">
            
            <button class="btn" style="width: 100%; font-size: 18px; padding: 18px;" onclick="performLogin()">🚀 دخول النظام</button>
            
            <div class="quick-login" onclick="quickLogin()">
                🔥 دخول سريع للتجربة 🔥<br>
                اضغط هنا للدخول بحساب المدير<br>
                admin / admin123
            </div>
            
            <div class="footer">
                <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
                <p style="margin: 10px 0;"><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
                <p style="margin: 5px 0;">AG Technology Systems</p>
                <p style="font-size: 12px; margin: 5px 0;">© 2024 جميع الحقوق محفوظة</p>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="hidden">
            <div class="logo">AG</div>
            <h1 style="text-align: center; margin-bottom: 10px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2 style="text-align: center; margin-bottom: 20px;">🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            
            <div style="text-align: center; margin: 25px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <span style="font-size: 18px;">مرحباً </span>
                <span id="currentUser" style="color: #ffd700; font-weight: bold; font-size: 20px;">مدير النظام</span>
                <br><br>
                <button class="btn btn-red" onclick="performLogout()">🚪 تسجيل الخروج</button>
            </div>

            <!-- الإحصائيات -->
            <h3 style="color: #ffd700; text-align: center; margin: 25px 0;">📊 إحصائيات النظام</h3>
            <div class="stats">
                <div class="stat-card" style="border-color: #4CAF50;">
                    <div class="stat-number" style="color: #4CAF50;">4</div>
                    <div>المستخدمين</div>
                </div>
                <div class="stat-card" style="border-color: #2196F3;">
                    <div class="stat-number" style="color: #2196F3;">2.5M</div>
                    <div>المبيعات (جنيه)</div>
                </div>
                <div class="stat-card" style="border-color: #FF9800;">
                    <div class="stat-number" style="color: #FF9800;">150</div>
                    <div>الأصناف</div>
                </div>
                <div class="stat-card" style="border-color: #9C27B0;">
                    <div class="stat-number" style="color: #9C27B0;">85</div>
                    <div>العملاء</div>
                </div>
            </div>

            <!-- العمليات -->
            <h3 style="color: #ffd700; text-align: center; margin: 25px 0;">⚡ العمليات المحاسبية</h3>
            <div class="operations">
                <div class="operation-card">
                    <h3>📄 فواتير المبيعات</h3>
                    <p style="margin-bottom: 20px;">إنشاء وإدارة فواتير المبيعات مع حساب الضرائب</p>
                    <button class="btn" onclick="showSalesInvoice()">📄 إنشاء فاتورة</button>
                    <button class="btn btn-blue" onclick="openPrintWindow('invoice')">🖨️ طباعة</button>
                </div>
                
                <div class="operation-card">
                    <h3>📦 فواتير المشتريات</h3>
                    <p style="margin-bottom: 20px;">تسجيل وإدارة فواتير المشتريات من الموردين</p>
                    <button class="btn" onclick="showPurchaseInvoice()">📦 إدخال فاتورة</button>
                </div>
                
                <div class="operation-card">
                    <h3>📈 التقارير المالية</h3>
                    <p style="margin-bottom: 20px;">عرض وطباعة التقارير والقوائم المالية</p>
                    <button class="btn" onclick="showFinancialReports()">📈 عرض التقارير</button>
                    <button class="btn btn-blue" onclick="openPrintWindow('reports')">🖨️ طباعة</button>
                </div>
                
                <div class="operation-card">
                    <h3>🏪 إدارة المخازن</h3>
                    <p style="margin-bottom: 20px;">متابعة المخزون وحركة الأصناف</p>
                    <button class="btn" onclick="showInventory()">🏪 عرض المخازن</button>
                </div>
            </div>

            <div class="footer">
                <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
                <p style="margin: 10px 0;"><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
                <p style="margin: 5px 0;">AG Technology Systems</p>
                <p style="font-size: 14px; margin: 5px 0;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                <p style="font-size: 12px; margin: 5px 0;">© 2024 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </div>

    <script>
        // منع الطباعة التلقائية
        window.addEventListener('beforeprint', function(e) {
            e.preventDefault();
            return false;
        });

        // دالة الدخول السريع
        function quickLogin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            performLogin();
        }

        // دالة تسجيل الدخول
        function performLogin() {
            var username = document.getElementById('username').value;
            var password = document.getElementById('password').value;
            var messageDiv = document.getElementById('loginMessage');
            
            if (!username || !password) {
                messageDiv.innerHTML = '<div class="message error">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }
            
            if (username === 'admin' && password === 'admin123') {
                messageDiv.innerHTML = '<div class="message success">✅ تم تسجيل الدخول بنجاح...</div>';
                
                setTimeout(function() {
                    document.getElementById('loginScreen').classList.add('hidden');
                    document.getElementById('mainSystem').classList.remove('hidden');
                    
                    alert('🎉 مرحباً بك في نظام المحاسبة!\n\n✅ تم تسجيل الدخول بنجاح\n\n🏢 شركة ايه جي تكنولوجي سيستميز\nAG Technology Systems\n\nيمكنك الآن استخدام جميع وظائف النظام');
                }, 1500);
                
            } else {
                messageDiv.innerHTML = '<div class="message error">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
            }
        }

        // دالة تسجيل الخروج
        function performLogout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('mainSystem').classList.add('hidden');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('loginMessage').innerHTML = '';
            }
        }

        // دوال العمليات
        function showSalesInvoice() {
            var invoiceNumber = 'INV-' + Date.now();
            var currentDate = new Date().toLocaleDateString('ar-EG');
            
            alert('📄 فاتورة مبيعات\n\n' +
                  'رقم الفاتورة: ' + invoiceNumber + '\n' +
                  'التاريخ: ' + currentDate + '\n\n' +
                  'بيانات العميل:\n' +
                  'العميل: شركة الأهرام للطباعة والنشر\n' +
                  'العنوان: القاهرة - مصر الجديدة\n' +
                  'الرقم الضريبي: 123456789\n\n' +
                  'تفاصيل الفاتورة:\n' +
                  'المنتج: ورق كتابة 80 جرام - مقاس A4\n' +
                  'الكمية: 100 طن\n' +
                  'السعر: 25,000 جنيه/طن\n' +
                  'الإجمالي قبل الضريبة: 2,500,000 جنيه\n' +
                  'ضريبة القيمة المضافة (14%): 350,000 جنيه\n' +
                  'الإجمالي شامل الضريبة: 2,850,000 جنيه\n\n' +
                  '✅ تم إنشاء الفاتورة بنجاح');
        }

        function showPurchaseInvoice() {
            var purchaseNumber = 'PUR-' + Date.now();
            var currentDate = new Date().toLocaleDateString('ar-EG');
            
            alert('📦 فاتورة مشتريات\n\n' +
                  'رقم الفاتورة: ' + purchaseNumber + '\n' +
                  'التاريخ: ' + currentDate + '\n\n' +
                  'بيانات المورد:\n' +
                  'المورد: شركة المواد الخام المصرية\n' +
                  'العنوان: الإسكندرية - المنطقة الصناعية\n' +
                  'الرقم الضريبي: 987654321\n\n' +
                  'تفاصيل الفاتورة:\n' +
                  'الصنف: لب الورق المستورد\n' +
                  'الكمية: 50 طن\n' +
                  'السعر: 15,000 جنيه/طن\n' +
                  'الإجمالي: 750,000 جنيه\n\n' +
                  '✅ تم تسجيل فاتورة المشتريات بنجاح');
        }

        function showFinancialReports() {
            var currentDate = new Date().toLocaleDateString('ar-EG');
            
            alert('📈 التقارير المالية\n\n' +
                  'التاريخ: ' + currentDate + '\n\n' +
                  '⚖️ ميزان المراجعة:\n' +
                  'إجمالي المدين: 17,500,000 جنيه\n' +
                  'إجمالي الدائن: 17,500,000 جنيه\n' +
                  'الميزان متوازن ✅\n\n' +
                  '📋 قائمة الدخل:\n' +
                  'إيرادات المبيعات: 5,500,000 جنيه\n' +
                  'تكلفة البضاعة المباعة: 3,850,000 جنيه\n' +
                  'مجمل الربح: 1,650,000 جنيه\n' +
                  'المصروفات التشغيلية: 900,000 جنيه\n' +
                  'صافي الربح قبل الضرائب: 750,000 جنيه\n' +
                  'ضرائب الدخل: 168,750 جنيه\n' +
                  'صافي الربح بعد الضرائب: 581,250 جنيه\n\n' +
                  '📊 قائمة المركز المالي:\n' +
                  'إجمالي الأصول: 12,750,000 جنيه\n' +
                  'إجمالي الخصوم: 2,800,000 جنيه\n' +
                  'حقوق الملكية: 9,950,000 جنيه');
        }

        function showInventory() {
            var currentDate = new Date().toLocaleDateString('ar-EG');
            
            alert('🏪 أرصدة المخازن\n\n' +
                  'التاريخ: ' + currentDate + '\n\n' +
                  '📦 المنتجات النهائية:\n' +
                  '• ورق كتابة 80 جرام: 500 طن - 12,500,000 جنيه\n' +
                  '• ورق طباعة 70 جرام: 300 طن - 6,900,000 جنيه\n' +
                  '• ورق صحف 45 جرام: 200 طن - 3,600,000 جنيه\n\n' +
                  '🏭 المواد الخام:\n' +
                  '• لب الورق: 150 طن - 2,250,000 جنيه\n' +
                  '• كيماويات: 50 طن - 750,000 جنيه\n' +
                  '• وقود ديزل: 25 طن - 375,000 جنيه\n\n' +
                  '💰 إجمالي قيمة المخزون: 26,375,000 جنيه\n\n' +
                  '📈 معدل دوران المخزون: 2.1 مرة سنوياً\n' +
                  '⏱️ متوسط فترة التخزين: 173 يوم');
        }

        function openPrintWindow(type) {
            if (type === 'invoice') {
                alert('🖨️ فتح نافذة طباعة الفاتورة\n\nسيتم فتح نافذة جديدة لطباعة فاتورة المبيعات مع التنسيق الاحترافي المناسب للطباعة الرسمية.\n\nتحتوي على:\n• معلومات الشركة\n• تفاصيل العميل\n• بنود الفاتورة\n• الضرائب والإجماليات\n• توقيع وختم الشركة');
            } else if (type === 'reports') {
                alert('🖨️ فتح نافذة طباعة التقارير\n\nسيتم فتح نافذة جديدة لطباعة ميزان المراجعة مع التنسيق الاحترافي المناسب للطباعة الرسمية.\n\nيحتوي على:\n• معلومات الشركة\n• جدول الحسابات\n• أرصدة المدين والدائن\n• الإجماليات المتوازنة\n• تاريخ وتوقيت التقرير');
            }
        }

        // تفعيل Enter للدخول
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && !document.getElementById('loginScreen').classList.contains('hidden')) {
                performLogin();
            }
        });

        // رسالة ترحيب في الكونسول
        console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
        console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
        console.log('✅ النظام جاهز للاستخدام!');
    </script>
</body>
</html>
