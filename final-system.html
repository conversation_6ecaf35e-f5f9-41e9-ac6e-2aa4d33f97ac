<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة - شركة مصر ادفو</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
        }
        .login-box {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            max-width: 400px;
            margin: 50px auto;
            text-align: center;
        }
        .main-content { display: none; }
        .tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        .tab:hover, .tab.active { background: rgba(255,215,0,0.3); }
        .section {
            display: none;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
        }
        .section.active { display: block; }
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 { color: #ffd700; margin-bottom: 15px; }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover { transform: translateY(-2px); }
        .btn-secondary { background: linear-gradient(45deg, #2196F3, #1976D2); }
        .form-group { margin: 15px 0; }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }
        .demo-btn {
            background: rgba(255,215,0,0.2);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            border: 1px solid rgba(255,215,0,0.3);
        }
        .demo-btn:hover { background: rgba(255,215,0,0.3); }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }
        .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #81C784);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen">
            <div class="header">
                <div class="logo">AG</div>
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            </div>
            
            <div class="login-box">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="message"></div>
                
                <div class="form-group">
                    <label>👤 اسم المستخدم:</label>
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>🔒 كلمة المرور:</label>
                    <input type="password" id="password" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()" style="width: 100%; padding: 15px;">🚀 دخول النظام</button>
                
                <div style="margin-top: 20px;">
                    <h4>📋 بيانات تجريبية:</h4>
                    <div class="demo-btn" onclick="fillLogin('admin', 'admin123')">
                        👨‍💼 مدير النظام: admin / admin123
                    </div>
                    <div class="demo-btn" onclick="fillLogin('accountant', 'acc123')">
                        👨‍💰 محاسب: accountant / acc123
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <div class="logo" style="width: 40px; height: 40px; font-size: 14px;">AG</div>
                    <div>
                        <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                        <span style="font-size: 12px;">AG Technology Systems</span><br>
                        <span style="font-size: 10px;">© 2024 جميع الحقوق محفوظة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="main-content">
            <div class="header">
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
                <p>مرحباً <span id="currentUser">المستخدم</span> | <button class="btn btn-secondary" onclick="logout()">🚪 خروج</button></p>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('dashboard')">🏠 الرئيسية</button>
                <button class="tab" onclick="showTab('operations')">⚡ العمليات</button>
                <button class="tab" onclick="showTab('reports')">📈 التقارير</button>
                <button class="tab" onclick="showTab('inventory')">🏪 المخازن</button>
            </div>

            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم</h2>
                
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">4</div>
                        <div>المستخدمين</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">2.5M</div>
                        <div>المبيعات (جنيه)</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">150</div>
                        <div>الأصناف</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">85</div>
                        <div>العملاء</div>
                    </div>
                </div>

                <div class="cards">
                    <div class="card">
                        <h3>📄 فاتورة مبيعات</h3>
                        <p>إنشاء فاتورة مبيعات جديدة</p>
                        <button class="btn" onclick="createInvoice()">إنشاء فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>📦 فاتورة مشتريات</h3>
                        <p>تسجيل فاتورة مشتريات</p>
                        <button class="btn" onclick="createPurchase()">إدخال فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>🏪 إدارة المخازن</h3>
                        <p>متابعة المخزون</p>
                        <button class="btn" onclick="showTab('inventory')">إدارة المخازن</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير المالية</h3>
                        <p>عرض القوائم المالية</p>
                        <button class="btn" onclick="showReport('trial')">عرض التقارير</button>
                    </div>
                </div>
            </div>

            <!-- العمليات اليومية -->
            <div id="operations" class="section">
                <h2>⚡ العمليات اليومية</h2>
                
                <div class="cards">
                    <div class="card">
                        <h3>📄 فاتورة مبيعات</h3>
                        <div class="form-group">
                            <label>العميل:</label>
                            <select id="customer">
                                <option value="">اختر العميل</option>
                                <option value="شركة الأهرام">شركة الأهرام للطباعة</option>
                                <option value="مؤسسة النيل">مؤسسة النيل للنشر</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المنتج:</label>
                            <select id="product">
                                <option value="">اختر المنتج</option>
                                <option value="ورق 80 جرام">ورق كتابة 80 جرام</option>
                                <option value="ورق 70 جرام">ورق طباعة 70 جرام</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية (طن):</label>
                            <input type="number" id="quantity" placeholder="الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر (جنيه/طن):</label>
                            <input type="number" id="price" placeholder="السعر">
                        </div>
                        <button class="btn" onclick="createInvoice()">📄 إنشاء الفاتورة</button>
                        <button class="btn btn-secondary" onclick="printInvoice()">🖨️ طباعة</button>
                    </div>
                    
                    <div class="card">
                        <h3>📦 فاتورة مشتريات</h3>
                        <div class="form-group">
                            <label>المورد:</label>
                            <select id="supplier">
                                <option value="">اختر المورد</option>
                                <option value="شركة المواد الخام">شركة المواد الخام</option>
                                <option value="مؤسسة الكيماويات">مؤسسة الكيماويات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="item">
                                <option value="">اختر الصنف</option>
                                <option value="لب الورق">لب الورق</option>
                                <option value="كيماويات">كيماويات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية:</label>
                            <input type="number" id="purchaseQty" placeholder="الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر:</label>
                            <input type="number" id="purchasePrice" placeholder="السعر">
                        </div>
                        <button class="btn" onclick="createPurchase()">📦 إدخال الفاتورة</button>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <h2>📈 التقارير والقوائم المالية</h2>
                
                <div class="cards">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p>إجمالي المدين: 12,750,000 جنيه</p>
                        <p>إجمالي الدائن: 12,750,000 جنيه</p>
                        <button class="btn" onclick="showReport('trial')">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printReport('trial')">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📋 قائمة الدخل</h3>
                        <p>إيرادات المبيعات: 5,500,000 جنيه</p>
                        <p>صافي الربح: 658,750 جنيه</p>
                        <button class="btn" onclick="showReport('income')">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printReport('income')">🖨️ طباعة</button>
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <h2>🏪 إدارة المخازن</h2>
                
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h3>أرصدة المخزون الحالية:</h3>
                    <p>• ورق كتابة 80 جرام: 500 طن</p>
                    <p>• ورق طباعة 70 جرام: 300 طن</p>
                    <p>• ورق صحف 45 جرام: 200 طن</p>
                    <p>• لب الورق: 150 طن</p>
                </div>
            </div>

            <!-- فوتر النظام -->
            <div style="text-align: center; margin-top: 50px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                <div class="logo">AG</div>
                <div>
                    <h3 style="color: #ffd700;">🏢 شركة ايه جي تكنولوجي سيستميز</h3>
                    <p>AG Technology Systems</p>
                    <p style="font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                    <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = {
            'admin': { password: 'admin123', name: 'مدير النظام' },
            'accountant': { password: 'acc123', name: 'محاسب رئيسي' }
        };

        let currentUser = null;

        // دالة ملء بيانات الدخول
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // دالة تسجيل الدخول
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');

            if (!username || !password) {
                messageDiv.innerHTML = '<div style="color: #ff6b6b; padding: 10px;">❌ يرجى إدخال البيانات</div>';
                return;
            }

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                messageDiv.innerHTML = '<div style="color: #4CAF50; padding: 10px;">✅ تم الدخول بنجاح...</div>';

                setTimeout(() => {
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('mainSystem').style.display = 'block';
                    document.getElementById('currentUser').textContent = currentUser.name;

                    alert(`🎉 مرحباً ${currentUser.name}!\n\nتم تسجيل الدخول بنجاح\n\n✅ يمكنك الآن:\n• إصدار فواتير المبيعات\n• إدخال المشتريات\n• إدارة المخازن\n• عرض وطباعة التقارير\n\n🏢 شركة ايه جي تكنولوجي سيستميز`);
                }, 1000);

            } else {
                messageDiv.innerHTML = '<div style="color: #ff6b6b; padding: 10px;">❌ بيانات خاطئة</div>';
            }
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                currentUser = null;
                document.getElementById('loginScreen').style.display = 'block';
                document.getElementById('mainSystem').style.display = 'none';
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('message').innerHTML = '';
            }
        }

        // دالة عرض التبويبات
        function showTab(tabId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));

            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إظهار القسم المطلوب
            document.getElementById(tabId).classList.add('active');

            // تفعيل التبويب المطلوب
            event.target.classList.add('active');
        }

        // دوال العمليات
        function createInvoice() {
            const customer = document.getElementById('customer')?.value || 'شركة الأهرام';
            const product = document.getElementById('product')?.value || 'ورق 80 جرام';
            const quantity = document.getElementById('quantity')?.value || '100';
            const price = document.getElementById('price')?.value || '25000';

            const total = quantity * price;
            const tax = total * 0.14;
            const grandTotal = total + tax;

            alert(`✅ تم إنشاء فاتورة مبيعات!\n\nالعميل: ${customer}\nالمنتج: ${product}\nالكمية: ${quantity} طن\nالسعر: ${price} جنيه/طن\nالإجمالي: ${grandTotal.toLocaleString()} جنيه\n\n📄 رقم الفاتورة: INV-${Date.now()}`);

            // مسح النموذج
            if (document.getElementById('customer')) document.getElementById('customer').value = '';
            if (document.getElementById('product')) document.getElementById('product').value = '';
            if (document.getElementById('quantity')) document.getElementById('quantity').value = '';
            if (document.getElementById('price')) document.getElementById('price').value = '';
        }

        function createPurchase() {
            const supplier = document.getElementById('supplier')?.value || 'شركة المواد الخام';
            const item = document.getElementById('item')?.value || 'لب الورق';
            const quantity = document.getElementById('purchaseQty')?.value || '50';
            const price = document.getElementById('purchasePrice')?.value || '15000';

            const total = quantity * price;

            alert(`✅ تم إدخال فاتورة مشتريات!\n\nالمورد: ${supplier}\nالصنف: ${item}\nالكمية: ${quantity}\nالسعر: ${price}\nالإجمالي: ${total.toLocaleString()} جنيه`);

            // مسح النموذج
            if (document.getElementById('supplier')) document.getElementById('supplier').value = '';
            if (document.getElementById('item')) document.getElementById('item').value = '';
            if (document.getElementById('purchaseQty')) document.getElementById('purchaseQty').value = '';
            if (document.getElementById('purchasePrice')) document.getElementById('purchasePrice').value = '';
        }

        // دوال التقارير
        function showReport(type) {
            if (type === 'trial') {
                alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n⚖️ ميزان المراجعة\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nرقم الحساب | اسم الحساب | مدين | دائن\n─────────────────────────────────────────\n1000 | الأصول الثابتة | 6,000,000 |\n1100 | الأصول المتداولة | 5,000,000 |\n2000 | الخصوم المتداولة | | 1,800,000\n3000 | رأس المال | | 4,000,000\n4000 | إيرادات المبيعات | | 5,500,000\n5000 | تكلفة البضاعة المباعة | 3,850,000 |\n6000 | مصروفات التشغيل | 900,000 |\n─────────────────────────────────────────\nالإجمالي | 15,750,000 | 15,750,000\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
            } else if (type === 'income') {
                alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n📋 قائمة الدخل\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nالإيرادات:\n• إيرادات المبيعات: 5,500,000 جنيه\n• إيرادات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي الإيرادات: 5,600,000 جنيه\n\nالتكاليف والمصروفات:\n• تكلفة البضاعة المباعة: 3,850,000 جنيه\n─────────────────────────────────────\nمجمل الربح: 1,750,000 جنيه\n\nالمصروفات التشغيلية:\n• مصروفات البيع والتوزيع: 450,000 جنيه\n• مصروفات إدارية وعمومية: 350,000 جنيه\n• مصروفات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي المصروفات التشغيلية: 900,000 جنيه\n\nصافي الربح قبل الضرائب: 850,000 جنيه\n• ضرائب الدخل (22.5%): 191,250 جنيه\n─────────────────────────────────────\nصافي الربح بعد الضرائب: 658,750 جنيه\n\n═══════════════════════════════════════\nهامش الربح الإجمالي: 31.25%\nهامش الربح الصافي: 11.76%\n\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
            }
        }

        // دوال الطباعة
        function printInvoice() {
            const content = `فاتورة مبيعات\n\nرقم الفاتورة: INV-${Date.now()}\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\nبيانات العميل:\nالعميل: شركة الأهرام للطباعة\nالعنوان: القاهرة - مصر الجديدة\n\nتفاصيل الفاتورة:\nالمنتج: ورق كتابة 80 جرام\nالكمية: 100 طن\nالسعر: 25,000 جنيه/طن\nالإجمالي: 2,500,000 جنيه\nضريبة القيمة المضافة: 350,000 جنيه\nالإجمالي شامل الضريبة: 2,850,000 جنيه`;

            printDocument('📄 فاتورة مبيعات', content);
        }

        function printReport(type) {
            if (type === 'trial') {
                const content = `ميزان المراجعة\n\nرقم الحساب | اسم الحساب | مدين | دائن\n─────────────────────────────────────────\n1000 | الأصول الثابتة | 6,000,000 |\n1100 | الأصول المتداولة | 5,000,000 |\n2000 | الخصوم المتداولة | | 1,800,000\n3000 | رأس المال | | 4,000,000\n4000 | إيرادات المبيعات | | 5,500,000\n5000 | تكلفة البضاعة المباعة | 3,850,000 |\n6000 | مصروفات التشغيل | 900,000 |\n─────────────────────────────────────────\nالإجمالي | 15,750,000 | 15,750,000`;

                printDocument('⚖️ ميزان المراجعة', content);
            } else if (type === 'income') {
                const content = `قائمة الدخل\n\nالإيرادات:\n• إيرادات المبيعات: 5,500,000 جنيه\n• إيرادات أخرى: 100,000 جنيه\nإجمالي الإيرادات: 5,600,000 جنيه\n\nالتكاليف والمصروفات:\n• تكلفة البضاعة المباعة: 3,850,000 جنيه\nمجمل الربح: 1,750,000 جنيه\n\nالمصروفات التشغيلية:\n• مصروفات البيع والتوزيع: 450,000 جنيه\n• مصروفات إدارية وعمومية: 350,000 جنيه\n• مصروفات أخرى: 100,000 جنيه\nإجمالي المصروفات التشغيلية: 900,000 جنيه\n\nصافي الربح قبل الضرائب: 850,000 جنيه\n• ضرائب الدخل (22.5%): 191,250 جنيه\nصافي الربح بعد الضرائب: 658,750 جنيه\n\nهامش الربح الإجمالي: 31.25%\nهامش الربح الصافي: 11.76%`;

                printDocument('📋 قائمة الدخل', content);
            }
        }

        function printDocument(title, content) {
            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG');

            const printContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding-bottom: 20px; margin-bottom: 30px; }
        .company-name { font-size: 24px; font-weight: bold; color: #4CAF50; margin: 10px 0; }
        .document-title { font-size: 20px; font-weight: bold; color: #333; margin: 15px 0; }
        .content { margin: 20px 0; white-space: pre-line; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding: 10px; background: white; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</div>
        <div class="document-title">${title}</div>
        <div>التاريخ: ${currentDate} | الوقت: ${currentTime}</div>
    </div>
    <div class="content">${content}</div>
    <div class="footer">
        <strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems<br>
        تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة
    </div>
    <script>
        window.onload = function() {
            window.print();
            setTimeout(function() { window.close(); }, 1000);
        }
    </script>
</body>
</html>`;

            printWindow.document.write(printContent);
            printWindow.document.close();
        }

        // تفعيل Enter للدخول
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && document.getElementById('loginScreen').style.display !== 'none') {
                login();
            }
        });

        console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
        console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
    </script>
</body>
</html>
