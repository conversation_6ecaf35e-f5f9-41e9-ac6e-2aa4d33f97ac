const express = require('express');
const { executeQuery } = require('../config/database');
const { authenticateToken, authorize } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);

// تقرير ميزان المراجعة
router.get('/trial-balance', authorize('admin', 'manager', 'supervisor'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    let params = [];

    if (start_date && end_date) {
      dateFilter = 'WHERE je.entry_date BETWEEN ? AND ?';
      params = [start_date, end_date];
    }

    const trialBalance = await executeQuery(`
      SELECT 
        fa.account_code,
        fa.account_name,
        fa.account_type,
        COALESCE(SUM(jed.debit_amount), 0) as total_debit,
        COALESCE(SUM(jed.credit_amount), 0) as total_credit,
        CASE 
          WHEN fa.account_type IN ('asset', 'expense') THEN 
            COALESCE(SUM(jed.debit_amount), 0) - COALESCE(SUM(jed.credit_amount), 0)
          ELSE 
            COALESCE(SUM(jed.credit_amount), 0) - COALESCE(SUM(jed.debit_amount), 0)
        END as balance
      FROM financial_accounts fa
      LEFT JOIN journal_entry_details jed ON fa.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id AND je.status = 'posted'
      ${dateFilter}
      WHERE fa.is_active = TRUE
      GROUP BY fa.id, fa.account_code, fa.account_name, fa.account_type
      HAVING total_debit > 0 OR total_credit > 0
      ORDER BY fa.account_code
    `, params);

    // حساب الإجماليات
    const totals = trialBalance.reduce((acc, account) => {
      acc.total_debits += account.total_debit;
      acc.total_credits += account.total_credit;
      return acc;
    }, { total_debits: 0, total_credits: 0 });

    res.json({
      success: true,
      data: {
        trial_balance: trialBalance,
        totals,
        period: {
          start_date: start_date || 'من البداية',
          end_date: end_date || 'حتى اليوم'
        }
      }
    });

  } catch (error) {
    console.error('خطأ في تقرير ميزان المراجعة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تقرير أرصدة المخازن
router.get('/inventory-balances', async (req, res) => {
  try {
    const { warehouse_id, item_category, low_stock_only } = req.query;

    let whereClause = '';
    let params = [];

    if (warehouse_id) {
      whereClause += 'WHERE w.id = ?';
      params.push(warehouse_id);
    }

    if (item_category) {
      whereClause += whereClause ? ' AND i.item_category = ?' : 'WHERE i.item_category = ?';
      params.push(item_category);
    }

    const inventoryBalances = await executeQuery(`
      SELECT 
        i.item_code,
        i.item_name,
        i.item_category,
        i.unit_of_measure,
        w.warehouse_name,
        COALESCE(latest_balance.balance_quantity, 0) as current_quantity,
        COALESCE(latest_balance.balance_value, 0) as current_value,
        i.minimum_stock,
        i.maximum_stock,
        CASE 
          WHEN COALESCE(latest_balance.balance_quantity, 0) <= i.minimum_stock THEN 'نقص في المخزون'
          WHEN COALESCE(latest_balance.balance_quantity, 0) >= i.maximum_stock THEN 'زيادة في المخزون'
          ELSE 'طبيعي'
        END as stock_status,
        CASE 
          WHEN COALESCE(latest_balance.balance_quantity, 0) > 0 THEN 
            COALESCE(latest_balance.balance_value, 0) / COALESCE(latest_balance.balance_quantity, 1)
          ELSE 0
        END as average_cost
      FROM items i
      CROSS JOIN warehouses w
      LEFT JOIN (
        SELECT 
          item_id, 
          warehouse_id, 
          balance_quantity, 
          balance_value,
          ROW_NUMBER() OVER (PARTITION BY item_id, warehouse_id ORDER BY created_at DESC) as rn
        FROM item_cards
      ) latest_balance ON i.id = latest_balance.item_id AND w.id = latest_balance.warehouse_id AND latest_balance.rn = 1
      ${whereClause}
      ${low_stock_only === 'true' ? (whereClause ? ' AND' : 'WHERE') + ' COALESCE(latest_balance.balance_quantity, 0) <= i.minimum_stock' : ''}
      ORDER BY i.item_code, w.warehouse_name
    `, params);

    // حساب الإجماليات
    const summary = inventoryBalances.reduce((acc, item) => {
      acc.total_items++;
      acc.total_value += item.current_value;
      if (item.stock_status === 'نقص في المخزون') acc.low_stock_items++;
      if (item.stock_status === 'زيادة في المخزون') acc.overstock_items++;
      return acc;
    }, { 
      total_items: 0, 
      total_value: 0, 
      low_stock_items: 0, 
      overstock_items: 0 
    });

    res.json({
      success: true,
      data: {
        inventory_balances: inventoryBalances,
        summary
      }
    });

  } catch (error) {
    console.error('خطأ في تقرير أرصدة المخازن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تقرير حركة الخزينة
router.get('/treasury-movement', async (req, res) => {
  try {
    const { treasury_id, start_date, end_date } = req.query;

    let whereClause = '';
    let params = [];

    if (treasury_id) {
      whereClause += 'WHERE tt.treasury_id = ?';
      params.push(treasury_id);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND tt.transaction_date >= ?' : 'WHERE tt.transaction_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND tt.transaction_date <= ?' : 'WHERE tt.transaction_date <= ?';
      params.push(end_date);
    }

    const treasuryMovement = await executeQuery(`
      SELECT 
        t.treasury_name,
        t.treasury_code,
        t.treasury_type,
        tt.transaction_date,
        tt.transaction_number,
        tt.description,
        tt.transaction_type,
        tt.amount,
        tt.payment_method,
        tt.beneficiary_name,
        u.full_name as created_by_name
      FROM treasury_transactions tt
      JOIN treasuries t ON tt.treasury_id = t.id
      JOIN users u ON tt.created_by = u.id
      ${whereClause}
      AND tt.status = 'completed'
      ORDER BY t.treasury_name, tt.transaction_date DESC
    `, params);

    // حساب الإجماليات لكل خزينة
    const treasurySummary = {};
    treasuryMovement.forEach(transaction => {
      const key = transaction.treasury_code;
      if (!treasurySummary[key]) {
        treasurySummary[key] = {
          treasury_name: transaction.treasury_name,
          treasury_type: transaction.treasury_type,
          total_receipts: 0,
          total_payments: 0,
          net_movement: 0
        };
      }

      if (transaction.transaction_type === 'receipt') {
        treasurySummary[key].total_receipts += transaction.amount;
      } else {
        treasurySummary[key].total_payments += transaction.amount;
      }
      
      treasurySummary[key].net_movement = treasurySummary[key].total_receipts - treasurySummary[key].total_payments;
    });

    res.json({
      success: true,
      data: {
        treasury_movement: treasuryMovement,
        treasury_summary: Object.values(treasurySummary),
        period: {
          start_date: start_date || 'من البداية',
          end_date: end_date || 'حتى اليوم'
        }
      }
    });

  } catch (error) {
    console.error('خطأ في تقرير حركة الخزينة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تقرير إنتاج وشحنات الورق
router.get('/paper-production-shipments', async (req, res) => {
  try {
    const { start_date, end_date, paper_type_id } = req.query;

    let dateFilter = '';
    let params = [];

    if (start_date && end_date) {
      dateFilter = 'AND dpp.production_date BETWEEN ? AND ?';
      params.push(start_date, end_date);
    }

    // تقرير الإنتاج
    const production = await executeQuery(`
      SELECT 
        pt.paper_name,
        pt.weight_gsm,
        pw.warehouse_name,
        SUM(dpp.quantity_produced) as total_quantity,
        SUM(dpp.total_weight) as total_weight,
        COUNT(*) as production_days
      FROM daily_paper_production dpp
      JOIN paper_types pt ON dpp.paper_type_id = pt.id
      JOIN paper_warehouses pw ON dpp.warehouse_id = pw.id
      WHERE 1=1 ${dateFilter}
      ${paper_type_id ? 'AND dpp.paper_type_id = ?' : ''}
      GROUP BY pt.id, pw.id
      ORDER BY pt.paper_name, pw.warehouse_name
    `, paper_type_id ? [...params, paper_type_id] : params);

    // تقرير الشحنات
    let shipmentDateFilter = dateFilter.replace('dpp.production_date', 'ps.shipment_date');
    const shipments = await executeQuery(`
      SELECT 
        pt.paper_name,
        pt.weight_gsm,
        pw.warehouse_name,
        COUNT(*) as total_shipments,
        SUM(ps.net_weight) as total_shipped_weight,
        SUM(ps.total_amount) as total_revenue,
        AVG(ps.unit_price) as average_price
      FROM paper_shipments ps
      JOIN paper_types pt ON ps.paper_type_id = pt.id
      JOIN paper_warehouses pw ON ps.warehouse_id = pw.id
      WHERE ps.status = 'delivered' ${shipmentDateFilter}
      ${paper_type_id ? 'AND ps.paper_type_id = ?' : ''}
      GROUP BY pt.id, pw.id
      ORDER BY pt.paper_name, pw.warehouse_name
    `, paper_type_id ? [...params, paper_type_id] : params);

    // دمج البيانات
    const combinedReport = production.map(prod => {
      const shipment = shipments.find(ship => 
        ship.paper_name === prod.paper_name && 
        ship.warehouse_name === prod.warehouse_name
      );

      return {
        paper_name: prod.paper_name,
        weight_gsm: prod.weight_gsm,
        warehouse_name: prod.warehouse_name,
        production: {
          total_quantity: prod.total_quantity,
          total_weight: prod.total_weight,
          production_days: prod.production_days
        },
        shipments: shipment ? {
          total_shipments: shipment.total_shipments,
          total_shipped_weight: shipment.total_shipped_weight,
          total_revenue: shipment.total_revenue,
          average_price: shipment.average_price
        } : {
          total_shipments: 0,
          total_shipped_weight: 0,
          total_revenue: 0,
          average_price: 0
        },
        remaining_stock: prod.total_weight - (shipment ? shipment.total_shipped_weight : 0)
      };
    });

    res.json({
      success: true,
      data: {
        paper_report: combinedReport,
        period: {
          start_date: start_date || 'من البداية',
          end_date: end_date || 'حتى اليوم'
        }
      }
    });

  } catch (error) {
    console.error('خطأ في تقرير إنتاج وشحنات الورق:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تقرير المراجعة الداخلية
router.get('/audit-log', authorize('admin', 'manager'), async (req, res) => {
  try {
    const { user_id, table_name, action, start_date, end_date, page = 1, limit = 100 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (user_id) {
      whereClause += 'WHERE al.user_id = ?';
      params.push(user_id);
    }

    if (table_name) {
      whereClause += whereClause ? ' AND al.table_name = ?' : 'WHERE al.table_name = ?';
      params.push(table_name);
    }

    if (action) {
      whereClause += whereClause ? ' AND al.action = ?' : 'WHERE al.action = ?';
      params.push(action);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND DATE(al.created_at) >= ?' : 'WHERE DATE(al.created_at) >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND DATE(al.created_at) <= ?' : 'WHERE DATE(al.created_at) <= ?';
      params.push(end_date);
    }

    const auditLog = await executeQuery(`
      SELECT 
        al.*,
        u.full_name as user_name,
        u.department
      FROM audit_log al
      JOIN users u ON al.user_id = u.id
      ${whereClause}
      ORDER BY al.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد سجلات المراجعة الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM audit_log al ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        audit_log: auditLog,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في تقرير المراجعة الداخلية:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
