import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AlertOutlinedSvg from "@ant-design/icons-svg/es/asn/AlertOutlined";
import AntdIcon from "../components/AntdIcon";
var AlertOutlined = function AlertOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AlertOutlinedSvg
  }));
};

/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE5MyA3OTZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTc0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU2M2MwLTE3Ni4yLTE0Mi44LTMxOS0zMTktMzE5UzE5MyAzODYuOCAxOTMgNTYzdjIzM3ptNzItMjMzYzAtMTM2LjQgMTEwLjYtMjQ3IDI0Ny0yNDdzMjQ3IDExMC42IDI0NyAyNDd2MTkzSDQwNFY1ODVjMC01LjUtNC41LTEwLTEwLTEwaC00NGMtNS41IDAtMTAgNC41LTEwIDEwdjE3MWgtNzVWNTYzem0tNDguMS0yNTIuNWwzOS42LTM5LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtNjcuOS02Ny45YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMzkuNiAzOS42YTguMDMgOC4wMyAwIDAwMCAxMS4zbDY3LjkgNjcuOWMzLjEgMy4xIDguMSAzLjEgMTEuMyAwem02NjkuNi03OS4ybC0zOS42LTM5LjZhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC02Ny45IDY3LjlhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzkuNiAzOS42YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNjcuOS02Ny45YzMuMS0zLjIgMy4xLTguMiAwLTExLjN6TTgzMiA4OTJIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyNGMwIDQuNCAzLjYgOCA4IDhoNjg4YzQuNCAwIDgtMy42IDgtOHYtMjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTQ4NCAxODBoNTZjNC40IDAgOC0zLjYgOC04Vjc2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY5NmMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(AlertOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlertOutlined';
}
export default RefIcon;