// خادم بسيط يعمل بضمان
const http = require('http');
const fs = require('fs');
const path = require('path');

// قراءة ملف HTML
function getIndexHTML() {
  try {
    return fs.readFileSync(path.join(__dirname, 'index.html'), 'utf8');
  } catch (error) {
    return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة والمراجعة الداخلية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(0,255,0,0.2);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(0,255,0,0.5);
            text-align: center;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .feature h3 {
            color: #ffd700;
            margin-bottom: 10px;
        }
        .commands {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        .commands code {
            background: rgba(255,255,255,0.1);
            padding: 5px 10px;
            border-radius: 5px;
            display: block;
            margin: 5px 0;
        }
        .next-steps {
            background: linear-gradient(45deg, rgba(255,215,0,0.2), rgba(255,165,0,0.2));
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px solid rgba(255,215,0,0.3);
        }
        .api-links {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .api-links a {
            color: #ffd700;
            text-decoration: none;
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            transition: all 0.3s;
        }
        .api-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 نظام المحاسبة والمراجعة الداخلية</h1>
        
        <div class="status">
            <h2>✅ الخادم يعمل بنجاح!</h2>
            <p><strong>المنفذ:</strong> 5000 | <strong>الحالة:</strong> متصل | <strong>الوقت:</strong> ${new Date().toLocaleString('ar-EG')}</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>📊 الحسابات المالية والتكاليف</h3>
                <p>إدارة شجرة الحسابات، أذونات الصرف والتوريد، القيود المحاسبية، ميزان المراجعة</p>
            </div>
            <div class="feature">
                <h3>🏪 إدارة المخازن</h3>
                <p>6 أنواع مخازن، بطاقات الأصناف، حركة المخزون، نظام الجرد المتكامل</p>
            </div>
            <div class="feature">
                <h3>🛒 إدارة المشتريات</h3>
                <p>طلبات الشراء، إدارة الموردين، عروض الأسعار، أوامر التوريد</p>
            </div>
            <div class="feature">
                <h3>💰 إدارة الخزينة</h3>
                <p>خزائن منفصلة، حركات يومية، سجلات الشيكات، الجرد الدوري</p>
            </div>
            <div class="feature">
                <h3>📄 مخازن الورق</h3>
                <p>إنتاج يومي، شحنات العملاء، أوزان السيارات، تصاريح الخروج</p>
            </div>
            <div class="feature">
                <h3>📈 التقارير والمراجعة</h3>
                <p>تقارير شاملة، تصدير PDF، استيراد Excel، سجل مراجعة داخلية</p>
            </div>
        </div>

        <div class="api-links">
            <h3>🔗 روابط النظام:</h3>
            <a href="/api/info">📋 معلومات النظام</a>
            <a href="/api/health">💚 حالة النظام</a>
            <a href="/api/files">📁 ملفات النظام</a>
        </div>

        <div class="next-steps">
            <h3>🚀 الخطوات التالية لتشغيل النظام الكامل:</h3>
            <div class="commands">
                <p><strong>1. تثبيت Node.js:</strong></p>
                <code>حمل من: https://nodejs.org</code>
                
                <p><strong>2. تثبيت الحزم:</strong></p>
                <code>npm install</code>
                
                <p><strong>3. إعداد قاعدة البيانات:</strong></p>
                <code>npm run setup</code>
                
                <p><strong>4. تشغيل النظام:</strong></p>
                <code>npm start</code>
                
                <p><strong>5. تشغيل واجهة المستخدم:</strong></p>
                <code>cd client && npm install && npm start</code>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2);">
            <p><strong>تم التطوير بواسطة:</strong> Ashraf | <strong>الإصدار:</strong> 1.0.0 | <strong>التاريخ:</strong> 2024</p>
        </div>
    </div>
</body>
</html>`;
  }
}

const server = http.createServer((req, res) => {
  // إعداد headers
  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  console.log(`📥 طلب جديد: ${req.method} ${req.url} من ${req.connection.remoteAddress}`);

  // معالجة المسارات
  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200);
    res.end(getIndexHTML());
    
  } else if (req.url === '/api/info') {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      system: {
        name: 'نظام المحاسبة والمراجعة الداخلية',
        version: '1.0.0',
        author: 'Ashraf',
        description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية',
        created: new Date().toISOString(),
        status: 'يعمل بنجاح'
      },
      modules: {
        financial: 'الحسابات المالية والتكاليف',
        inventory: 'إدارة المخازن',
        purchasing: 'إدارة المشتريات',
        treasury: 'إدارة الخزينة',
        paper_warehouse: 'مخازن الورق',
        reports: 'التقارير والمراجعة'
      },
      features: [
        'تصدير PDF',
        'استيراد Excel',
        'واجهة مستخدم React',
        'نظام مصادقة آمن',
        'تقارير شاملة',
        'دورة مستندية كاملة'
      ]
    }, null, 2));
    
  } else if (req.url === '/api/health') {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'النظام يعمل بشكل طبيعي',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      platform: process.platform
    }, null, 2));
    
  } else if (req.url === '/api/files') {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.writeHead(200);
    
    const files = {
      backend: [
        'server.js - الخادم الرئيسي',
        'setup.js - إعداد قاعدة البيانات',
        'package.json - إعدادات المشروع',
        'routes/ - 10 ملفات مسارات API',
        'utils/ - أدوات PDF والاستيراد',
        'database/schema.sql - هيكل قاعدة البيانات'
      ],
      frontend: [
        'client/src/App.js - التطبيق الرئيسي',
        'client/src/components/ - مكونات الواجهة',
        'client/src/services/ - خدمات API',
        'client/package.json - حزم React'
      ],
      documentation: [
        'README.md - دليل شامل',
        'INSTALLATION.md - تعليمات التثبيت',
        'index.html - صفحة ترحيب'
      ]
    };
    
    res.end(JSON.stringify({
      success: true,
      message: 'قائمة ملفات النظام',
      files: files,
      total_files: Object.values(files).flat().length
    }, null, 2));
    
  } else {
    res.writeHead(404);
    res.end(`
      <html dir="rtl">
        <head><meta charset="UTF-8"><title>صفحة غير موجودة</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px; background: #f0f0f0;">
          <h1>❌ الصفحة غير موجودة</h1>
          <p>الصفحة المطلوبة "${req.url}" غير موجودة</p>
          <a href="/" style="color: #1890ff; text-decoration: none;">🏠 العودة للصفحة الرئيسية</a>
        </body>
      </html>
    `);
  }
});

const PORT = 5000;

server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 الخادم يعمل بنجاح!');
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
  console.log(`📅 الوقت: ${new Date().toLocaleString('ar-EG')}`);
  console.log('✅ جاهز لاستقبال الطلبات');
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ المنفذ ${PORT} مستخدم بالفعل`);
    console.log('💡 جرب إيقاف العمليات الأخرى أو استخدم منفذ آخر');
  } else {
    console.error('❌ خطأ في الخادم:', err.message);
  }
});

module.exports = server;
