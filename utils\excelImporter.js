const XLSX = require('xlsx');
const { executeQuery, executeTransaction } = require('../config/database');

class ExcelImporter {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.successCount = 0;
  }

  // قراءة ملف Excel
  readExcelFile(filePath, sheetName = null) {
    try {
      const workbook = XLSX.readFile(filePath);
      const sheet = sheetName ? workbook.Sheets[sheetName] : workbook.Sheets[workbook.SheetNames[0]];
      
      if (!sheet) {
        throw new Error(`الورقة ${sheetName || 'الأولى'} غير موجودة`);
      }

      const data = XLSX.utils.sheet_to_json(sheet, { 
        header: 1,
        defval: '',
        raw: false
      });

      return data;
    } catch (error) {
      throw new Error(`خطأ في قراءة ملف Excel: ${error.message}`);
    }
  }

  // قراءة من Buffer (للرفع عبر الويب)
  readExcelBuffer(buffer, sheetName = null) {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheet = sheetName ? workbook.Sheets[sheetName] : workbook.Sheets[workbook.SheetNames[0]];
      
      if (!sheet) {
        throw new Error(`الورقة ${sheetName || 'الأولى'} غير موجودة`);
      }

      const data = XLSX.utils.sheet_to_json(sheet, { 
        header: 1,
        defval: '',
        raw: false
      });

      return data;
    } catch (error) {
      throw new Error(`خطأ في قراءة ملف Excel: ${error.message}`);
    }
  }

  // التحقق من صحة البيانات
  validateData(data, requiredColumns) {
    const errors = [];
    
    if (!data || data.length === 0) {
      errors.push('الملف فارغ أو لا يحتوي على بيانات');
      return errors;
    }

    const headers = data[0];
    
    // التحقق من وجود الأعمدة المطلوبة
    requiredColumns.forEach(column => {
      if (!headers.includes(column)) {
        errors.push(`العمود المطلوب "${column}" غير موجود`);
      }
    });

    // التحقق من وجود بيانات
    if (data.length < 2) {
      errors.push('لا توجد بيانات للاستيراد (فقط رأس الجدول موجود)');
    }

    return errors;
  }

  // استيراد الحسابات المالية
  async importFinancialAccounts(data, userId) {
    this.errors = [];
    this.warnings = [];
    this.successCount = 0;

    const requiredColumns = ['رقم الحساب', 'اسم الحساب', 'نوع الحساب'];
    const validationErrors = this.validateData(data, requiredColumns);
    
    if (validationErrors.length > 0) {
      this.errors = validationErrors;
      return this.getResult();
    }

    const headers = data[0];
    const accountCodeIndex = headers.indexOf('رقم الحساب');
    const accountNameIndex = headers.indexOf('اسم الحساب');
    const accountTypeIndex = headers.indexOf('نوع الحساب');
    const parentAccountIndex = headers.indexOf('الحساب الأب');

    const accountTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'];

    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      
      try {
        const accountCode = row[accountCodeIndex]?.toString().trim();
        const accountName = row[accountNameIndex]?.toString().trim();
        const accountType = row[accountTypeIndex]?.toString().trim().toLowerCase();
        const parentAccount = row[parentAccountIndex]?.toString().trim();

        // التحقق من البيانات المطلوبة
        if (!accountCode || !accountName || !accountType) {
          this.errors.push(`الصف ${i + 1}: بيانات مطلوبة مفقودة`);
          continue;
        }

        // التحقق من نوع الحساب
        if (!accountTypes.includes(accountType)) {
          this.errors.push(`الصف ${i + 1}: نوع الحساب "${accountType}" غير صحيح`);
          continue;
        }

        // التحقق من عدم تكرار رقم الحساب
        const existingAccount = await executeQuery(
          'SELECT id FROM financial_accounts WHERE account_code = ?',
          [accountCode]
        );

        if (existingAccount.length > 0) {
          this.warnings.push(`الصف ${i + 1}: رقم الحساب "${accountCode}" موجود بالفعل - تم تجاهله`);
          continue;
        }

        // البحث عن الحساب الأب إذا كان موجوداً
        let parentAccountId = null;
        if (parentAccount) {
          const parentResult = await executeQuery(
            'SELECT id FROM financial_accounts WHERE account_code = ?',
            [parentAccount]
          );
          
          if (parentResult.length > 0) {
            parentAccountId = parentResult[0].id;
          } else {
            this.warnings.push(`الصف ${i + 1}: الحساب الأب "${parentAccount}" غير موجود`);
          }
        }

        // إدراج الحساب
        await executeQuery(
          `INSERT INTO financial_accounts (account_code, account_name, account_type, parent_account_id) 
           VALUES (?, ?, ?, ?)`,
          [accountCode, accountName, accountType, parentAccountId]
        );

        this.successCount++;

      } catch (error) {
        this.errors.push(`الصف ${i + 1}: ${error.message}`);
      }
    }

    return this.getResult();
  }

  // استيراد الأصناف
  async importItems(data, userId) {
    this.errors = [];
    this.warnings = [];
    this.successCount = 0;

    const requiredColumns = ['رقم الصنف', 'اسم الصنف', 'فئة الصنف', 'وحدة القياس'];
    const validationErrors = this.validateData(data, requiredColumns);
    
    if (validationErrors.length > 0) {
      this.errors = validationErrors;
      return this.getResult();
    }

    const headers = data[0];
    const itemCodeIndex = headers.indexOf('رقم الصنف');
    const itemNameIndex = headers.indexOf('اسم الصنف');
    const itemCategoryIndex = headers.indexOf('فئة الصنف');
    const unitOfMeasureIndex = headers.indexOf('وحدة القياس');
    const minStockIndex = headers.indexOf('الحد الأدنى');
    const maxStockIndex = headers.indexOf('الحد الأقصى');
    const unitCostIndex = headers.indexOf('تكلفة الوحدة');

    const itemCategories = ['raw_material', 'auxiliary_material', 'fuel', 'spare_part', 'maintenance', 'packaging', 'finished_product'];

    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      
      try {
        const itemCode = row[itemCodeIndex]?.toString().trim();
        const itemName = row[itemNameIndex]?.toString().trim();
        const itemCategory = row[itemCategoryIndex]?.toString().trim().toLowerCase();
        const unitOfMeasure = row[unitOfMeasureIndex]?.toString().trim();
        const minStock = parseFloat(row[minStockIndex]) || 0;
        const maxStock = parseFloat(row[maxStockIndex]) || 0;
        const unitCost = parseFloat(row[unitCostIndex]) || 0;

        // التحقق من البيانات المطلوبة
        if (!itemCode || !itemName || !itemCategory || !unitOfMeasure) {
          this.errors.push(`الصف ${i + 1}: بيانات مطلوبة مفقودة`);
          continue;
        }

        // التحقق من فئة الصنف
        if (!itemCategories.includes(itemCategory)) {
          this.errors.push(`الصف ${i + 1}: فئة الصنف "${itemCategory}" غير صحيحة`);
          continue;
        }

        // التحقق من عدم تكرار رقم الصنف
        const existingItem = await executeQuery(
          'SELECT id FROM items WHERE item_code = ?',
          [itemCode]
        );

        if (existingItem.length > 0) {
          this.warnings.push(`الصف ${i + 1}: رقم الصنف "${itemCode}" موجود بالفعل - تم تجاهله`);
          continue;
        }

        // إدراج الصنف
        await executeQuery(
          `INSERT INTO items (item_code, item_name, item_category, unit_of_measure, minimum_stock, maximum_stock, unit_cost) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [itemCode, itemName, itemCategory, unitOfMeasure, minStock, maxStock, unitCost]
        );

        this.successCount++;

      } catch (error) {
        this.errors.push(`الصف ${i + 1}: ${error.message}`);
      }
    }

    return this.getResult();
  }

  // استيراد الموردين
  async importSuppliers(data, userId) {
    this.errors = [];
    this.warnings = [];
    this.successCount = 0;

    const requiredColumns = ['رقم المورد', 'اسم المورد'];
    const validationErrors = this.validateData(data, requiredColumns);
    
    if (validationErrors.length > 0) {
      this.errors = validationErrors;
      return this.getResult();
    }

    const headers = data[0];
    const supplierCodeIndex = headers.indexOf('رقم المورد');
    const supplierNameIndex = headers.indexOf('اسم المورد');
    const contactPersonIndex = headers.indexOf('الشخص المسؤول');
    const phoneIndex = headers.indexOf('الهاتف');
    const emailIndex = headers.indexOf('البريد الإلكتروني');
    const addressIndex = headers.indexOf('العنوان');
    const categoryIndex = headers.indexOf('فئة المورد');
    const paymentTermsIndex = headers.indexOf('شروط الدفع');

    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      
      try {
        const supplierCode = row[supplierCodeIndex]?.toString().trim();
        const supplierName = row[supplierNameIndex]?.toString().trim();
        const contactPerson = row[contactPersonIndex]?.toString().trim() || null;
        const phone = row[phoneIndex]?.toString().trim() || null;
        const email = row[emailIndex]?.toString().trim() || null;
        const address = row[addressIndex]?.toString().trim() || null;
        const category = row[categoryIndex]?.toString().trim() || null;
        const paymentTerms = row[paymentTermsIndex]?.toString().trim() || null;

        // التحقق من البيانات المطلوبة
        if (!supplierCode || !supplierName) {
          this.errors.push(`الصف ${i + 1}: رقم المورد واسم المورد مطلوبان`);
          continue;
        }

        // التحقق من عدم تكرار رقم المورد
        const existingSupplier = await executeQuery(
          'SELECT id FROM suppliers WHERE supplier_code = ?',
          [supplierCode]
        );

        if (existingSupplier.length > 0) {
          this.warnings.push(`الصف ${i + 1}: رقم المورد "${supplierCode}" موجود بالفعل - تم تجاهله`);
          continue;
        }

        // التحقق من صحة البريد الإلكتروني
        if (email && !this.isValidEmail(email)) {
          this.warnings.push(`الصف ${i + 1}: البريد الإلكتروني "${email}" غير صحيح`);
        }

        // إدراج المورد
        await executeQuery(
          `INSERT INTO suppliers (supplier_code, supplier_name, contact_person, phone, email, address, supplier_category, payment_terms) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [supplierCode, supplierName, contactPerson, phone, email, address, category, paymentTerms]
        );

        this.successCount++;

      } catch (error) {
        this.errors.push(`الصف ${i + 1}: ${error.message}`);
      }
    }

    return this.getResult();
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // الحصول على نتيجة الاستيراد
  getResult() {
    return {
      success: this.errors.length === 0,
      successCount: this.successCount,
      errors: this.errors,
      warnings: this.warnings,
      summary: `تم استيراد ${this.successCount} سجل بنجاح، ${this.errors.length} خطأ، ${this.warnings.length} تحذير`
    };
  }

  // إنشاء قالب Excel للاستيراد
  static createTemplate(type) {
    const templates = {
      financial_accounts: [
        ['رقم الحساب', 'اسم الحساب', 'نوع الحساب', 'الحساب الأب'],
        ['1000', 'الأصول', 'asset', ''],
        ['1100', 'الأصول المتداولة', 'asset', '1000'],
        ['1110', 'النقدية والبنوك', 'asset', '1100']
      ],
      items: [
        ['رقم الصنف', 'اسم الصنف', 'فئة الصنف', 'وحدة القياس', 'الحد الأدنى', 'الحد الأقصى', 'تكلفة الوحدة'],
        ['ITM001', 'مواد خام أساسية', 'raw_material', 'كيلو', '100', '1000', '50.00'],
        ['ITM002', 'وقود ديزل', 'fuel', 'لتر', '500', '5000', '15.50']
      ],
      suppliers: [
        ['رقم المورد', 'اسم المورد', 'الشخص المسؤول', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'فئة المورد', 'شروط الدفع'],
        ['SUP001', 'شركة المواد الخام', 'أحمد محمد', '********890', '<EMAIL>', 'القاهرة', 'مواد خام', 'نقدي'],
        ['SUP002', 'مؤسسة الوقود', 'محمد علي', '01987654321', '<EMAIL>', 'الإسكندرية', 'وقود', 'آجل 30 يوم']
      ]
    };

    if (!templates[type]) {
      throw new Error(`نوع القالب "${type}" غير مدعوم`);
    }

    const ws = XLSX.utils.aoa_to_sheet(templates[type]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

    return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
  }
}

module.exports = ExcelImporter;
