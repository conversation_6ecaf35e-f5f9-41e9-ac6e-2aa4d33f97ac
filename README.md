# نظام المحاسبة والمراجعة الداخلية الشامل

نظام محاسبي متكامل يغطي جميع جوانب المراجعة الداخلية للشركات، مع التركيز على الدورة المستندية والرقابة الداخلية.

## 🌟 المميزات الرئيسية

### 📊 الحسابات المالية والتكاليف
- إدارة الحسابات المالية وشجرة الحسابات
- أذونات الصرف والتوريد مع نظام الموافقات
- القيود المحاسبية والتسويات المالية
- دفتر الأستاذ العام والمساعد
- قوائم التكاليف قبل وبعد التوزيع
- ميزان المراجعة النهائي

### 🏪 إدارة المخازن
- إدارة المخازن المتعددة (خامات رئيسية، مساعدة، وقود، قطع غيار، صيانة، تعبئة)
- بطاقات الأصناف وحركة المخزون
- علوم الاستلام ومحاضر الفحص
- بواني الصرف مع مراكز التكاليف
- نظام الجرد الدوري والمستمر والمفاجئ

### 🛒 إدارة المشتريات
- طلبات الشراء مع نظام الموافقات
- إدارة الموردين وتصنيفهم
- عروض الأسعار والتفريغ
- أوامر التوريد والمتابعة
- لجان البت للمشتريات عالية القيمة

### 💰 إدارة الخزينة
- خزائن منفصلة للمصروفات والإيرادات
- حركات الخزينة اليومية
- سجلات الشيكات والتوقيعات
- الجرد الدوري والمفاجئ للخزائن
- ربط مع البنوك والتمويل

### 📄 مخازن الورق
- إدارة إنتاج الورق اليومي
- تصنيف الورق حسب الجرام والمقاس
- شحنات العملاء مع أوزان السيارات
- علوم التصدير وتصاريح الخروج
- مطابقة الأوزان والكميات

### 📈 التقارير والمراجعة
- ميزان المراجعة التفصيلي
- تقارير أرصدة المخازن
- تقارير حركة الخزينة
- تقارير الإنتاج والشحنات
- سجل المراجعة الداخلية الشامل

## 🛠️ التقنيات المستخدمة

- **Backend**: Node.js + Express.js
- **Database**: MySQL
- **Authentication**: JWT
- **Security**: bcryptjs, helmet, rate limiting
- **API**: RESTful APIs

## 📋 متطلبات النظام

- Node.js (الإصدار 14 أو أحدث)
- MySQL (الإصدار 5.7 أو أحدث)
- npm أو yarn

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd accounting-system
```

### 2. تثبيت الحزم
```bash
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# تأكد من تشغيل MySQL
# قم بتحديث ملف .env بمعلومات قاعدة البيانات

# تشغيل إعداد قاعدة البيانات
node setup.js
```

### 4. تشغيل النظام
```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

## 🔧 إعدادات البيئة

قم بتحديث ملف `.env` بالمعلومات المناسبة:

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=accounting_system

# إعدادات الأمان
JWT_SECRET=your_secret_key

# إعدادات الشركة
COMPANY_NAME=اسم شركتك
COMPANY_ADDRESS=عنوان الشركة
```

## 👤 المستخدم الافتراضي

بعد الإعداد، يمكنك تسجيل الدخول باستخدام:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: قم بتغيير كلمة المرور فور تسجيل الدخول الأول!

## 🔐 الصلاحيات والأدوار

### الأدوار المتاحة:
- **admin**: مدير النظام (صلاحية كاملة)
- **manager**: مدير القسم
- **supervisor**: مشرف
- **user**: مستخدم عادي

### الأقسام:
- **financial**: الحسابات المالية
- **inventory**: المخازن
- **purchasing**: المشتريات
- **treasury**: الخزينة
- **paper_warehouse**: مخازن الورق
- **audit**: المراجعة الداخلية

## 📚 استخدام API

### المصادقة
```javascript
// تسجيل الدخول
POST /api/auth/login
{
  "username": "admin",
  "password": "admin123"
}

// استخدام التوكن في الطلبات
Headers: {
  "Authorization": "Bearer YOUR_TOKEN"
}
```

### أمثلة على الطلبات
```javascript
// الحصول على الحسابات المالية
GET /api/financial/accounts

// إنشاء إذن صرف
POST /api/financial/vouchers
{
  "voucher_type": "payment",
  "amount": 1000,
  "description": "صرف مصروفات إدارية"
}

// الحصول على أرصدة المخازن
GET /api/inventory/balances
```

## 🔄 الدورة المستندية

النظام يطبق الدورة المستندية الكاملة:

1. **إنشاء المستند** → **مراجعة** → **اعتماد** → **تنفيذ**
2. **ختم المراجعة** لمنع التكرار
3. **تسجيل جميع العمليات** في سجل المراجعة
4. **مطابقات دورية** بين الأقسام المختلفة

## 🛡️ الأمان والرقابة

- تشفير كلمات المرور
- نظام صلاحيات متقدم
- تسجيل جميع العمليات
- حماية من الهجمات الشائعة
- معدل محدود للطلبات

## 🔧 التخصيص والتطوير

النظام مصمم ليكون قابل للتخصيص:

- إضافة حقول جديدة
- تعديل الدورة المستندية
- إضافة تقارير مخصصة
- ربط مع أنظمة خارجية

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع الوثائق في مجلد `docs/`
2. تحقق من ملفات السجل في `logs/`
3. استخدم أدوات التطوير في المتصفح

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف `LICENSE` للتفاصيل.

---

**تم تطويره بواسطة**: Ashraf  
**التاريخ**: 2024  
**الإصدار**: 1.0.0
