const express = require('express');
const { executeQuery } = require('../config/database');
const { authenticateToken, authorize } = require('../middleware/auth');
const { generateTrialBalanceReport, generateInventoryReport } = require('../utils/pdfGenerator');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);

// تصدير ميزان المراجعة PDF
router.get('/trial-balance/pdf', async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    let dateFilter = '';
    let params = [];

    if (start_date && end_date) {
      dateFilter = 'WHERE je.entry_date BETWEEN ? AND ?';
      params = [start_date, end_date];
    }

    const trialBalance = await executeQuery(`
      SELECT 
        fa.account_code,
        fa.account_name,
        fa.account_type,
        COALESCE(SUM(jed.debit_amount), 0) as total_debit,
        COALESCE(SUM(jed.credit_amount), 0) as total_credit,
        CASE 
          WHEN fa.account_type IN ('asset', 'expense') THEN 
            COALESCE(SUM(jed.debit_amount), 0) - COALESCE(SUM(jed.credit_amount), 0)
          ELSE 
            COALESCE(SUM(jed.credit_amount), 0) - COALESCE(SUM(jed.debit_amount), 0)
        END as balance
      FROM financial_accounts fa
      LEFT JOIN journal_entry_details jed ON fa.id = jed.account_id
      LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id AND je.status = 'posted'
      ${dateFilter}
      WHERE fa.is_active = TRUE
      GROUP BY fa.id, fa.account_code, fa.account_name, fa.account_type
      HAVING total_debit > 0 OR total_credit > 0
      ORDER BY fa.account_code
    `, params);

    const pdfBuffer = await generateTrialBalanceReport(trialBalance, {
      startDate: start_date,
      endDate: end_date
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="trial-balance-${new Date().toISOString().split('T')[0]}.pdf"`);
    res.send(pdfBuffer);

  } catch (error) {
    console.error('خطأ في تصدير ميزان المراجعة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير التقرير'
    });
  }
});

// تصدير تقرير المخازن PDF
router.get('/inventory/pdf', async (req, res) => {
  try {
    const { warehouse_id, item_category } = req.query;
    
    let whereClause = '';
    let params = [];

    if (warehouse_id) {
      whereClause += 'WHERE w.id = ?';
      params.push(warehouse_id);
    }

    if (item_category) {
      whereClause += whereClause ? ' AND i.item_category = ?' : 'WHERE i.item_category = ?';
      params.push(item_category);
    }

    const inventoryBalances = await executeQuery(`
      SELECT 
        i.item_code,
        i.item_name,
        i.item_category,
        i.unit_of_measure,
        w.warehouse_name,
        COALESCE(latest_balance.balance_quantity, 0) as current_quantity,
        COALESCE(latest_balance.balance_value, 0) as current_value,
        i.minimum_stock,
        i.maximum_stock,
        CASE 
          WHEN COALESCE(latest_balance.balance_quantity, 0) <= i.minimum_stock THEN 'نقص في المخزون'
          WHEN COALESCE(latest_balance.balance_quantity, 0) >= i.maximum_stock THEN 'زيادة في المخزون'
          ELSE 'طبيعي'
        END as stock_status
      FROM items i
      CROSS JOIN warehouses w
      LEFT JOIN (
        SELECT 
          item_id, 
          warehouse_id, 
          balance_quantity, 
          balance_value,
          ROW_NUMBER() OVER (PARTITION BY item_id, warehouse_id ORDER BY created_at DESC) as rn
        FROM item_cards
      ) latest_balance ON i.id = latest_balance.item_id AND w.id = latest_balance.warehouse_id AND latest_balance.rn = 1
      ${whereClause}
      ORDER BY i.item_code, w.warehouse_name
    `, params);

    const warehouseName = warehouse_id ? 
      (await executeQuery('SELECT warehouse_name FROM warehouses WHERE id = ?', [warehouse_id]))[0]?.warehouse_name :
      'جميع المخازن';

    const pdfBuffer = await generateInventoryReport(inventoryBalances, {
      warehouseName
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="inventory-report-${new Date().toISOString().split('T')[0]}.pdf"`);
    res.send(pdfBuffer);

  } catch (error) {
    console.error('خطأ في تصدير تقرير المخازن:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير التقرير'
    });
  }
});

// تصدير قائمة الحسابات المالية Excel
router.get('/financial-accounts/excel', async (req, res) => {
  try {
    const XLSX = require('xlsx');
    
    const accounts = await executeQuery(`
      SELECT 
        fa.account_code as 'رقم الحساب',
        fa.account_name as 'اسم الحساب',
        fa.account_type as 'نوع الحساب',
        parent.account_name as 'الحساب الأب',
        CASE WHEN fa.is_active THEN 'نشط' ELSE 'غير نشط' END as 'الحالة',
        DATE_FORMAT(fa.created_at, '%Y-%m-%d') as 'تاريخ الإنشاء'
      FROM financial_accounts fa
      LEFT JOIN financial_accounts parent ON fa.parent_account_id = parent.id
      ORDER BY fa.account_code
    `);

    const ws = XLSX.utils.json_to_sheet(accounts);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الحسابات المالية');

    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="financial-accounts-${new Date().toISOString().split('T')[0]}.xlsx"`);
    res.send(buffer);

  } catch (error) {
    console.error('خطأ في تصدير الحسابات المالية:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير البيانات'
    });
  }
});

// تصدير قائمة الأصناف Excel
router.get('/items/excel', async (req, res) => {
  try {
    const XLSX = require('xlsx');
    
    const items = await executeQuery(`
      SELECT 
        item_code as 'رقم الصنف',
        item_name as 'اسم الصنف',
        item_category as 'فئة الصنف',
        unit_of_measure as 'وحدة القياس',
        minimum_stock as 'الحد الأدنى',
        maximum_stock as 'الحد الأقصى',
        unit_cost as 'تكلفة الوحدة',
        CASE WHEN is_active THEN 'نشط' ELSE 'غير نشط' END as 'الحالة',
        DATE_FORMAT(created_at, '%Y-%m-%d') as 'تاريخ الإنشاء'
      FROM items
      ORDER BY item_code
    `);

    const ws = XLSX.utils.json_to_sheet(items);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الأصناف');

    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="items-${new Date().toISOString().split('T')[0]}.xlsx"`);
    res.send(buffer);

  } catch (error) {
    console.error('خطأ في تصدير الأصناف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير البيانات'
    });
  }
});

// تصدير قائمة الموردين Excel
router.get('/suppliers/excel', async (req, res) => {
  try {
    const XLSX = require('xlsx');
    
    const suppliers = await executeQuery(`
      SELECT 
        supplier_code as 'رقم المورد',
        supplier_name as 'اسم المورد',
        contact_person as 'الشخص المسؤول',
        phone as 'الهاتف',
        email as 'البريد الإلكتروني',
        address as 'العنوان',
        supplier_category as 'فئة المورد',
        payment_terms as 'شروط الدفع',
        CASE WHEN is_active THEN 'نشط' ELSE 'غير نشط' END as 'الحالة',
        DATE_FORMAT(created_at, '%Y-%m-%d') as 'تاريخ الإنشاء'
      FROM suppliers
      ORDER BY supplier_code
    `);

    const ws = XLSX.utils.json_to_sheet(suppliers);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الموردين');

    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="suppliers-${new Date().toISOString().split('T')[0]}.xlsx"`);
    res.send(buffer);

  } catch (error) {
    console.error('خطأ في تصدير الموردين:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير البيانات'
    });
  }
});

module.exports = router;
