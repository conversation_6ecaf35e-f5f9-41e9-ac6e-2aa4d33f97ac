import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'fr_BE',
  today: "Aujourd'hui",
  now: 'Maintenant',
  backToToday: "Aujourd'hui",
  ok: 'OK',
  clear: 'Rétablir',
  week: '<PERSON><PERSON><PERSON>',
  month: '<PERSON><PERSON>',
  year: 'Ann<PERSON>',
  timeSelect: "Sélectionner l'heure",
  dateSelect: "Sélectionner l'heure",
  monthSelect: 'Choisissez un mois',
  yearSelect: 'Choisissez une année',
  decadeSelect: 'Choisissez une décennie',
  dateFormat: 'D/M/YYYY',
  dateTimeFormat: 'D/M/YYYY HH:mm:ss',
  previousMonth: '<PERSON><PERSON> précédent (PageUp)',
  nextMonth: '<PERSON><PERSON> suivant (PageDown)',
  previousYear: '<PERSON><PERSON> précédente (Ctrl + gauche)',
  nextYear: '<PERSON><PERSON> prochaine (Ctrl + droite)',
  previousDecade: '<PERSON>écennie précédente',
  nextDecade: '<PERSON>écennie suivante',
  previousCentury: 'Siècle précédent',
  nextCentury: 'Siècle suivant'
});
export default locale;