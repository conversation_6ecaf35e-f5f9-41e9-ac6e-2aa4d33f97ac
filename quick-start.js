// خادم سريع للبدء
const express = require('express');
const app = express();

// إعدادات أساسية
app.use(express.json());

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام المحاسبة والمراجعة الداخلية</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                color: white;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                font-size: 2.5em;
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .feature {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                text-align: center;
            }
            .api-links {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                margin-top: 20px;
            }
            .api-links a {
                color: #ffd700;
                text-decoration: none;
                display: block;
                margin: 10px 0;
                padding: 10px;
                background: rgba(255,255,255,0.1);
                border-radius: 5px;
                transition: all 0.3s;
            }
            .api-links a:hover {
                background: rgba(255,255,255,0.2);
                transform: translateX(-5px);
            }
            .status {
                text-align: center;
                background: rgba(0,255,0,0.2);
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                border: 2px solid rgba(0,255,0,0.5);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 نظام المحاسبة والمراجعة الداخلية</h1>
            
            <div class="status">
                <h2>✅ النظام يعمل بنجاح!</h2>
                <p>الإصدار 1.0.0 - تم التطوير بواسطة Ashraf</p>
            </div>

            <div class="features">
                <div class="feature">
                    <h3>📊 الحسابات المالية</h3>
                    <p>إدارة الحسابات وأذونات الصرف والقيود المحاسبية</p>
                </div>
                <div class="feature">
                    <h3>🏪 إدارة المخازن</h3>
                    <p>بطاقات الأصناف وحركة المخزون والجرد</p>
                </div>
                <div class="feature">
                    <h3>🛒 إدارة المشتريات</h3>
                    <p>طلبات الشراء والموردين وأوامر التوريد</p>
                </div>
                <div class="feature">
                    <h3>💰 إدارة الخزينة</h3>
                    <p>حركات الخزينة والشيكات والتوقيعات</p>
                </div>
                <div class="feature">
                    <h3>📄 مخازن الورق</h3>
                    <p>إنتاج الورق وشحنات العملاء</p>
                </div>
                <div class="feature">
                    <h3>📈 التقارير</h3>
                    <p>ميزان المراجعة وتقارير شاملة</p>
                </div>
            </div>

            <div class="api-links">
                <h3>🔗 روابط النظام:</h3>
                <a href="/api/info">📋 معلومات النظام</a>
                <a href="/api/health">💚 حالة النظام</a>
                <a href="/api/test">🧪 اختبار API</a>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <h3>🚀 الخطوات التالية:</h3>
                <p>1. قم بإعداد قاعدة البيانات: <code>npm run setup</code></p>
                <p>2. سجل دخول باستخدام: <strong>admin / admin123</strong></p>
                <p>3. استخدم API endpoints للتفاعل مع النظام</p>
            </div>
        </div>
    </body>
    </html>
  `);
});

// معلومات النظام
app.get('/api/info', (req, res) => {
  res.json({
    success: true,
    system: {
      name: 'نظام المحاسبة والمراجعة الداخلية',
      version: '1.0.0',
      author: 'Ashraf',
      description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية',
      created: new Date().toISOString()
    },
    modules: {
      financial: 'الحسابات المالية والتكاليف',
      inventory: 'إدارة المخازن',
      purchasing: 'إدارة المشتريات',
      treasury: 'إدارة الخزينة',
      paper_warehouse: 'مخازن الورق',
      reports: 'التقارير والمراجعة'
    },
    endpoints: [
      'GET /',
      'GET /api/info',
      'GET /api/health',
      'GET /api/test'
    ]
  });
});

// حالة النظام
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'النظام يعمل بشكل طبيعي',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
});

// اختبار API
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'اختبار API ناجح! 🎉',
    timestamp: new Date().toISOString(),
    test_data: {
      accounts: ['الأصول', 'الخصوم', 'حقوق الملكية'],
      warehouses: ['مخزن الخامات', 'مخزن الوقود', 'مخزن قطع الغيار'],
      users: ['admin', 'manager', 'user']
    }
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 النظام يعمل على المنفذ', PORT);
  console.log('🌐 افتح المتصفح على: http://localhost:' + PORT);
  console.log('✅ جاهز للاستخدام!');
});

module.exports = app;
