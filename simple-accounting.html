<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة - شركة مصر ادفو</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .login-screen {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            max-width: 500px;
            margin: 50px auto;
            text-align: center;
        }

        .main-system {
            display: none;
        }

        .tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
        }

        .tab:hover, .tab.active {
            background: rgba(255,215,0,0.3);
            transform: translateY(-2px);
        }

        .section {
            display: none;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
        }

        .section.active {
            display: block;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .form-group {
            margin: 15px 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }

        .form-group input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .demo-account {
            background: rgba(255,215,0,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
            text-align: right;
        }

        .demo-account:hover {
            background: rgba(255,215,0,0.2);
            transform: translateY(-2px);
        }

        .demo-account code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            color: #ffd700;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .error {
            background: rgba(255,0,0,0.2);
            border: 2px solid rgba(255,0,0,0.5);
            color: #ff6b6b;
        }

        .success {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
            color: #4CAF50;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            margin-top: 10px;
            opacity: 0.9;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .data-table th, .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .data-table th {
            background: rgba(255,215,0,0.3);
            color: #333;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 50%, #A5D6A7 100%);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 0 auto 15px;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen" class="login-screen">
            <div class="company-logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            <p>تسجيل الدخول للنظام</p>
            
            <div style="margin: 30px 0;">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="loginMessage"></div>
                
                <div class="form-group">
                    <label for="username">👤 اسم المستخدم:</label>
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label for="password">🔒 كلمة المرور:</label>
                    <input type="password" id="password" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()" style="width: 100%; padding: 15px; font-size: 18px;">🚀 دخول النظام</button>
                
                <div style="margin-top: 20px;">
                    <h4>📋 بيانات تجريبية للدخول:</h4>
                    <div class="demo-account" onclick="fillLogin('admin', 'admin123')">
                        <strong>👨‍💼 مدير النظام:</strong><br>
                        اسم المستخدم: <code>admin</code> | كلمة المرور: <code>admin123</code>
                    </div>
                    <div class="demo-account" onclick="fillLogin('accountant', 'acc123')">
                        <strong>👨‍💰 محاسب:</strong><br>
                        اسم المستخدم: <code>accountant</code> | كلمة المرور: <code>acc123</code>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <div class="company-logo" style="width: 60px; height: 60px; font-size: 18px;">AG</div>
                <div>
                    <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                    <span style="font-size: 14px;">AG Technology Systems</span><br>
                    <span style="font-size: 12px; opacity: 0.8;">© 2024 جميع الحقوق محفوظة</span>
                </div>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="main-system">
            <div class="header">
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
                <p>مرحباً <span id="currentUser">المستخدم</span> | <button class="btn btn-secondary" onclick="logout()">🚪 تسجيل الخروج</button></p>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('dashboard')">🏠 لوحة التحكم</button>
                <button class="tab" onclick="showTab('operations')">⚡ العمليات اليومية</button>
                <button class="tab" onclick="showTab('reports')">📈 التقارير</button>
                <button class="tab" onclick="showTab('inventory')">🏪 المخازن</button>
            </div>

            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم الرئيسية</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,500,000</div>
                        <div class="stat-label">إجمالي المبيعات (جنيه)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">150</div>
                        <div class="stat-label">الأصناف</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">85</div>
                        <div class="stat-label">العملاء</div>
                    </div>
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إصدار فاتورة مبيعات</h3>
                        <p>إنشاء فاتورة مبيعات جديدة مع جميع التفاصيل</p>
                        <button class="btn" onclick="showTab('operations')">📄 إصدار فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>📦 إدخال مشتريات</h3>
                        <p>تسجيل فاتورة مشتريات جديدة</p>
                        <button class="btn" onclick="showTab('operations')">📦 إدخال فاتورة</button>
                    </div>
                    <div class="card">
                        <h3>🏪 حركة المخازن</h3>
                        <p>إضافة أو صرف من المخزون</p>
                        <button class="btn" onclick="showTab('inventory')">🏪 إدارة المخازن</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير المالية</h3>
                        <p>عرض وطباعة جميع التقارير والقوائم المالية</p>
                        <button class="btn" onclick="showTab('reports')">📈 عرض التقارير</button>
                    </div>
                </div>
            </div>

            <!-- العمليات اليومية -->
            <div id="operations" class="section">
                <h2>⚡ العمليات اليومية</h2>

                <div class="alert">
                    <strong>العمليات اليومية:</strong> إدخال وإدارة جميع العمليات اليومية للشركة
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إصدار فاتورة مبيعات</h3>
                        <div class="form-group">
                            <label>العميل:</label>
                            <select id="salesCustomer">
                                <option value="">اختر العميل</option>
                                <option value="شركة الأهرام للطباعة">شركة الأهرام للطباعة</option>
                                <option value="مؤسسة النيل للنشر">مؤسسة النيل للنشر</option>
                                <option value="شركة المستقبل للورق">شركة المستقبل للورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المنتج:</label>
                            <select id="salesProduct">
                                <option value="">اختر المنتج</option>
                                <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                                <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                                <option value="ورق صحف 45 جرام">ورق صحف 45 جرام</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية (طن):</label>
                            <input type="number" id="salesQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر (جنيه/طن):</label>
                            <input type="number" id="salesPrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createSalesInvoice()">📄 إصدار الفاتورة</button>
                        <button class="btn btn-secondary" onclick="printSalesInvoice()">🖨️ طباعة</button>
                    </div>

                    <div class="card">
                        <h3>📦 إدخال مشتريات</h3>
                        <div class="form-group">
                            <label>المورد:</label>
                            <select id="purchaseSupplier">
                                <option value="">اختر المورد</option>
                                <option value="شركة المواد الخام المصرية">شركة المواد الخام المصرية</option>
                                <option value="مؤسسة الكيماويات الصناعية">مؤسسة الكيماويات الصناعية</option>
                                <option value="شركة الوقود والطاقة">شركة الوقود والطاقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="purchaseItem">
                                <option value="">اختر الصنف</option>
                                <option value="لب الورق">لب الورق</option>
                                <option value="كيماويات">كيماويات</option>
                                <option value="وقود ديزل">وقود ديزل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية:</label>
                            <input type="number" id="purchaseQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر:</label>
                            <input type="number" id="purchasePrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createPurchaseInvoice()">📦 إدخال الفاتورة</button>
                    </div>

                    <div class="card">
                        <h3>📥 إضافة للمخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="addItem">
                                <option value="">اختر الصنف</option>
                                <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                                <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                                <option value="لب الورق">لب الورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المضافة:</label>
                            <input type="number" id="addQuantity" placeholder="أدخل الكمية">
                        </div>
                        <button class="btn" onclick="addToInventory()">📥 إضافة للمخزون</button>
                    </div>

                    <div class="card">
                        <h3>📤 صرف من المخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="issueItem">
                                <option value="">اختر الصنف</option>
                                <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                                <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                                <option value="لب الورق">لب الورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المصروفة:</label>
                            <input type="number" id="issueQuantity" placeholder="أدخل الكمية">
                        </div>
                        <button class="btn" onclick="issueFromInventory()">📤 صرف من المخزون</button>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <h2>📈 التقارير والقوائم المالية</h2>

                <div class="cards-grid">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p>إجمالي المدين: 12,750,000 جنيه</p>
                        <p>إجمالي الدائن: 12,750,000 جنيه</p>
                        <button class="btn" onclick="showTrialBalance()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printTrialBalance()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📋 قائمة الدخل</h3>
                        <p>إيرادات المبيعات: 5,500,000 جنيه</p>
                        <p>صافي الربح: 658,750 جنيه</p>
                        <button class="btn" onclick="showIncomeStatement()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printIncomeStatement()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📊 قائمة المركز المالي</h3>
                        <p>إجمالي الأصول: 11,000,000 جنيه</p>
                        <p>حقوق الملكية: 8,200,000 جنيه</p>
                        <button class="btn" onclick="showBalanceSheet()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printBalanceSheet()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>💰 قائمة التدفقات النقدية</h3>
                        <p>التدفق من العمليات: 958,750 جنيه</p>
                        <p>صافي التدفق النقدي: 458,750 جنيه</p>
                        <button class="btn" onclick="showCashFlow()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="printCashFlow()">🖨️ طباعة</button>
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <h2>🏪 إدارة المخازن</h2>

                <div class="alert">
                    <strong>إدارة المخازن:</strong> متابعة المخزون وحركة الأصناف
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الكمية الحالية</th>
                            <th>الوحدة</th>
                            <th>القيمة (جنيه)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>P001</td>
                            <td>ورق كتابة 80 جرام</td>
                            <td>500</td>
                            <td>طن</td>
                            <td>12,500,000</td>
                        </tr>
                        <tr>
                            <td>P002</td>
                            <td>ورق طباعة 70 جرام</td>
                            <td>300</td>
                            <td>طن</td>
                            <td>6,900,000</td>
                        </tr>
                        <tr>
                            <td>P003</td>
                            <td>ورق صحف 45 جرام</td>
                            <td>200</td>
                            <td>طن</td>
                            <td>3,600,000</td>
                        </tr>
                        <tr>
                            <td>R001</td>
                            <td>لب الورق</td>
                            <td>150</td>
                            <td>طن</td>
                            <td>2,250,000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- فوتر النظام -->
            <div class="footer">
                <div class="company-logo">AG</div>
                <div>
                    <h3 style="color: #ffd700; margin: 10px 0;">🏢 شركة ايه جي تكنولوجي سيستميز</h3>
                    <p style="margin: 5px 0;">AG Technology Systems</p>
                    <p style="margin: 5px 0; font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                    <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">© 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = {
            'admin': { password: 'admin123', name: 'مدير النظام' },
            'accountant': { password: 'acc123', name: 'محاسب رئيسي' }
        };

        let currentUser = null;

        // دالة ملء بيانات الدخول
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        // دالة تسجيل الدخول
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('loginMessage');

            if (!username || !password) {
                messageDiv.innerHTML = '<div class="message error">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                messageDiv.innerHTML = '<div class="message success">✅ تم تسجيل الدخول بنجاح...</div>';

                setTimeout(() => {
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('mainSystem').style.display = 'block';
                    document.getElementById('currentUser').textContent = currentUser.name;

                    alert(`🎉 مرحباً ${currentUser.name}!\n\nتم تسجيل الدخول بنجاح إلى نظام المحاسبة والمراجعة الداخلية\n\n✅ يمكنك الآن:\n• إصدار فواتير المبيعات\n• إدخال المشتريات\n• إدارة المخازن\n• عرض وطباعة التقارير المالية\n\n🏢 شركة ايه جي تكنولوجي سيستميز`);
                }, 1500);

            } else {
                messageDiv.innerHTML = '<div class="message error">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
            }
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                currentUser = null;
                document.getElementById('loginScreen').style.display = 'block';
                document.getElementById('mainSystem').style.display = 'none';
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('loginMessage').innerHTML = '';
            }
        }

        // دالة عرض التبويبات
        function showTab(tabId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));

            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إظهار القسم المطلوب
            document.getElementById(tabId).classList.add('active');

            // تفعيل التبويب المطلوب
            event.target.classList.add('active');
        }

        // دوال العمليات اليومية
        function createSalesInvoice() {
            const customer = document.getElementById('salesCustomer').value || 'شركة الأهرام للطباعة';
            const product = document.getElementById('salesProduct').value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('salesQuantity').value || '100';
            const price = document.getElementById('salesPrice').value || '25000';

            const total = quantity * price;
            const tax = total * 0.14;
            const grandTotal = total + tax;

            alert(`✅ تم إنشاء فاتورة مبيعات بنجاح!\n\nالعميل: ${customer}\nالمنتج: ${product}\nالكمية: ${quantity} طن\nالسعر: ${price} جنيه/طن\nالإجمالي قبل الضريبة: ${total.toLocaleString()} جنيه\nضريبة القيمة المضافة (14%): ${tax.toLocaleString()} جنيه\nالإجمالي شامل الضريبة: ${grandTotal.toLocaleString()} جنيه\n\n📄 رقم الفاتورة: INV-${Date.now()}`);

            // مسح النموذج
            document.getElementById('salesCustomer').value = '';
            document.getElementById('salesProduct').value = '';
            document.getElementById('salesQuantity').value = '';
            document.getElementById('salesPrice').value = '';
        }

        function createPurchaseInvoice() {
            const supplier = document.getElementById('purchaseSupplier').value || 'شركة المواد الخام المصرية';
            const item = document.getElementById('purchaseItem').value || 'لب الورق';
            const quantity = document.getElementById('purchaseQuantity').value || '50';
            const price = document.getElementById('purchasePrice').value || '15000';

            const total = quantity * price;

            alert(`✅ تم إدخال فاتورة مشتريات بنجاح!\n\nالمورد: ${supplier}\nالصنف: ${item}\nالكمية: ${quantity}\nالسعر: ${price}\nالإجمالي: ${total.toLocaleString()} جنيه\n\n📦 تم تسجيل الفاتورة في النظام`);

            // مسح النموذج
            document.getElementById('purchaseSupplier').value = '';
            document.getElementById('purchaseItem').value = '';
            document.getElementById('purchaseQuantity').value = '';
            document.getElementById('purchasePrice').value = '';
        }

        function addToInventory() {
            const item = document.getElementById('addItem').value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('addQuantity').value || '10';

            alert(`✅ تم إضافة للمخزون بنجاح!\n\nالصنف: ${item}\nالكمية المضافة: ${quantity}\n\n📥 تم تحديث أرصدة المخزون`);

            // مسح النموذج
            document.getElementById('addItem').value = '';
            document.getElementById('addQuantity').value = '';
        }

        function issueFromInventory() {
            const item = document.getElementById('issueItem').value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('issueQuantity').value || '5';

            alert(`✅ تم صرف من المخزون بنجاح!\n\nالصنف: ${item}\nالكمية المصروفة: ${quantity}\n\n📤 تم تحديث أرصدة المخزون`);

            // مسح النموذج
            document.getElementById('issueItem').value = '';
            document.getElementById('issueQuantity').value = '';
        }

        // دوال التقارير
        function showTrialBalance() {
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n⚖️ ميزان المراجعة\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nرقم الحساب | اسم الحساب | مدين | دائن\n─────────────────────────────────────────\n1000 | الأصول الثابتة | 6,000,000 |\n1100 | الأصول المتداولة | 5,000,000 |\n2000 | الخصوم المتداولة | | 1,800,000\n3000 | رأس المال | | 4,000,000\n4000 | إيرادات المبيعات | | 5,500,000\n5000 | تكلفة البضاعة المباعة | 3,850,000 |\n6000 | مصروفات التشغيل | 900,000 |\n─────────────────────────────────────────\nالإجمالي | 15,750,000 | 15,750,000\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        function showIncomeStatement() {
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n📋 قائمة الدخل\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nالإيرادات:\n• إيرادات المبيعات: 5,500,000 جنيه\n• إيرادات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي الإيرادات: 5,600,000 جنيه\n\nالتكاليف والمصروفات:\n• تكلفة البضاعة المباعة: 3,850,000 جنيه\n─────────────────────────────────────\nمجمل الربح: 1,750,000 جنيه\n\nالمصروفات التشغيلية:\n• مصروفات البيع والتوزيع: 450,000 جنيه\n• مصروفات إدارية وعمومية: 350,000 جنيه\n• مصروفات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي المصروفات التشغيلية: 900,000 جنيه\n\nصافي الربح قبل الضرائب: 850,000 جنيه\n• ضرائب الدخل (22.5%): 191,250 جنيه\n─────────────────────────────────────\nصافي الربح بعد الضرائب: 658,750 جنيه\n\n═══════════════════════════════════════\nهامش الربح الإجمالي: 31.25%\nهامش الربح الصافي: 11.76%\n\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        function showBalanceSheet() {
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n📊 قائمة المركز المالي (الميزانية العمومية)\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nالأصول:\n\nالأصول غير المتداولة:\n• الأراضي والمباني: 2,500,000 جنيه\n• الآلات والمعدات: 2,800,000 جنيه\n• وسائل النقل: 450,000 جنيه\n• أصول أخرى: 250,000 جنيه\n─────────────────────────────────────\nإجمالي الأصول غير المتداولة: 6,000,000 جنيه\n\nالأصول المتداولة:\n• النقدية والبنوك: 650,000 جنيه\n• العملاء: 1,450,000 جنيه\n• المخزون: 2,775,000 جنيه\n• مصروفات مقدمة: 125,000 جنيه\n─────────────────────────────────────\nإجمالي الأصول المتداولة: 5,000,000 جنيه\n\nإجمالي الأصول: 11,000,000 جنيه\n\n═══════════════════════════════════════\n\nالخصوم وحقوق الملكية:\n\nالخصوم المتداولة:\n• الموردون: 980,000 جنيه\n• مصروفات مستحقة: 220,000 جنيه\n• ضرائب مستحقة: 150,000 جنيه\n• قروض قصيرة الأجل: 450,000 جنيه\n─────────────────────────────────────\nإجمالي الخصوم المتداولة: 1,800,000 جنيه\n\nالخصوم طويلة الأجل:\n• قروض طويلة الأجل: 1,000,000 جنيه\n─────────────────────────────────────\nإجمالي الخصوم: 2,800,000 جنيه\n\nحقوق الملكية:\n• رأس المال: 4,000,000 جنيه\n• الأرباح المحتجزة: 3,541,250 جنيه\n• أرباح العام الجاري: 658,750 جنيه\n─────────────────────────────────────\nإجمالي حقوق الملكية: 8,200,000 جنيه\n\nإجمالي الخصوم وحقوق الملكية: 11,000,000 جنيه\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        function showCashFlow() {
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n💰 قائمة التدفقات النقدية\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nالتدفقات النقدية من الأنشطة التشغيلية:\n• صافي الربح: 658,750 جنيه\n• الاستهلاك: 450,000 جنيه\n• التغير في العملاء: (150,000) جنيه\n• التغير في المخزون: (200,000) جنيه\n• التغير في الموردين: 120,000 جنيه\n• التغير في المصروفات المستحقة: 80,000 جنيه\n─────────────────────────────────────\nصافي التدفق من الأنشطة التشغيلية: 958,750 جنيه\n\nالتدفقات النقدية من الأنشطة الاستثمارية:\n• شراء أصول ثابتة: (350,000) جنيه\n• بيع أصول ثابتة: 50,000 جنيه\n─────────────────────────────────────\nصافي التدفق من الأنشطة الاستثمارية: (300,000) جنيه\n\nالتدفقات النقدية من الأنشطة التمويلية:\n• قروض جديدة: 200,000 جنيه\n• سداد قروض: (150,000) جنيه\n• توزيعات أرباح: (250,000) جنيه\n─────────────────────────────────────\nصافي التدفق من الأنشطة التمويلية: (200,000) جنيه\n\nصافي التغير في النقدية: 458,750 جنيه\nرصيد النقدية أول الفترة: 191,250 جنيه\n─────────────────────────────────────\nرصيد النقدية آخر الفترة: 650,000 جنيه\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        // دوال الطباعة
        function printDocument(title, content) {
            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG');

            const printContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding-bottom: 20px; margin-bottom: 30px; }
        .company-logo { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #4CAF50, #81C784); display: inline-flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; color: white; margin-bottom: 15px; }
        .company-name { font-size: 24px; font-weight: bold; color: #4CAF50; margin: 10px 0; }
        .document-title { font-size: 20px; font-weight: bold; color: #333; margin: 15px 0; }
        .content { margin: 20px 0; white-space: pre-line; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding: 10px; background: white; }
        @media print { .no-print { display: none !important; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-logo">AG</div>
        <div class="company-name">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</div>
        <div class="document-title">${title}</div>
        <div>التاريخ: ${currentDate} | الوقت: ${currentTime}</div>
    </div>
    <div class="content">${content}</div>
    <div class="footer">
        <strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems<br>
        تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة
    </div>
    <script>
        window.onload = function() {
            window.print();
            setTimeout(function() { window.close(); }, 1000);
        }
    </script>
</body>
</html>`;

            printWindow.document.write(printContent);
            printWindow.document.close();
        }

        function printTrialBalance() {
            const content = `ميزان المراجعة

رقم الحساب | اسم الحساب | مدين | دائن
─────────────────────────────────────────
1000 | الأصول الثابتة | 6,000,000 |
1100 | الأصول المتداولة | 5,000,000 |
2000 | الخصوم المتداولة | | 1,800,000
3000 | رأس المال | | 4,000,000
4000 | إيرادات المبيعات | | 5,500,000
5000 | تكلفة البضاعة المباعة | 3,850,000 |
6000 | مصروفات التشغيل | 900,000 |
─────────────────────────────────────────
الإجمالي | 15,750,000 | 15,750,000`;

            printDocument('⚖️ ميزان المراجعة', content);
        }

        function printIncomeStatement() {
            const content = `قائمة الدخل

الإيرادات:
• إيرادات المبيعات: 5,500,000 جنيه
• إيرادات أخرى: 100,000 جنيه
─────────────────────────────────────
إجمالي الإيرادات: 5,600,000 جنيه

التكاليف والمصروفات:
• تكلفة البضاعة المباعة: 3,850,000 جنيه
─────────────────────────────────────
مجمل الربح: 1,750,000 جنيه

المصروفات التشغيلية:
• مصروفات البيع والتوزيع: 450,000 جنيه
• مصروفات إدارية وعمومية: 350,000 جنيه
• مصروفات أخرى: 100,000 جنيه
─────────────────────────────────────
إجمالي المصروفات التشغيلية: 900,000 جنيه

صافي الربح قبل الضرائب: 850,000 جنيه
• ضرائب الدخل (22.5%): 191,250 جنيه
─────────────────────────────────────
صافي الربح بعد الضرائب: 658,750 جنيه

هامش الربح الإجمالي: 31.25%
هامش الربح الصافي: 11.76%`;

            printDocument('📋 قائمة الدخل', content);
        }

        function printBalanceSheet() {
            const content = `قائمة المركز المالي (الميزانية العمومية)

الأصول:

الأصول غير المتداولة:
• الأراضي والمباني: 2,500,000 جنيه
• الآلات والمعدات: 2,800,000 جنيه
• وسائل النقل: 450,000 جنيه
• أصول أخرى: 250,000 جنيه
─────────────────────────────────────
إجمالي الأصول غير المتداولة: 6,000,000 جنيه

الأصول المتداولة:
• النقدية والبنوك: 650,000 جنيه
• العملاء: 1,450,000 جنيه
• المخزون: 2,775,000 جنيه
• مصروفات مقدمة: 125,000 جنيه
─────────────────────────────────────
إجمالي الأصول المتداولة: 5,000,000 جنيه

إجمالي الأصول: 11,000,000 جنيه

الخصوم وحقوق الملكية:

الخصوم المتداولة:
• الموردون: 980,000 جنيه
• مصروفات مستحقة: 220,000 جنيه
• ضرائب مستحقة: 150,000 جنيه
• قروض قصيرة الأجل: 450,000 جنيه
─────────────────────────────────────
إجمالي الخصوم المتداولة: 1,800,000 جنيه

الخصوم طويلة الأجل:
• قروض طويلة الأجل: 1,000,000 جنيه
─────────────────────────────────────
إجمالي الخصوم: 2,800,000 جنيه

حقوق الملكية:
• رأس المال: 4,000,000 جنيه
• الأرباح المحتجزة: 3,541,250 جنيه
• أرباح العام الجاري: 658,750 جنيه
─────────────────────────────────────
إجمالي حقوق الملكية: 8,200,000 جنيه

إجمالي الخصوم وحقوق الملكية: 11,000,000 جنيه`;

            printDocument('📊 قائمة المركز المالي', content);
        }

        function printCashFlow() {
            const content = `قائمة التدفقات النقدية

التدفقات النقدية من الأنشطة التشغيلية:
• صافي الربح: 658,750 جنيه
• الاستهلاك: 450,000 جنيه
• التغير في العملاء: (150,000) جنيه
• التغير في المخزون: (200,000) جنيه
• التغير في الموردين: 120,000 جنيه
• التغير في المصروفات المستحقة: 80,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التشغيلية: 958,750 جنيه

التدفقات النقدية من الأنشطة الاستثمارية:
• شراء أصول ثابتة: (350,000) جنيه
• بيع أصول ثابتة: 50,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة الاستثمارية: (300,000) جنيه

التدفقات النقدية من الأنشطة التمويلية:
• قروض جديدة: 200,000 جنيه
• سداد قروض: (150,000) جنيه
• توزيعات أرباح: (250,000) جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التمويلية: (200,000) جنيه

صافي التغير في النقدية: 458,750 جنيه
رصيد النقدية أول الفترة: 191,250 جنيه
─────────────────────────────────────
رصيد النقدية آخر الفترة: 650,000 جنيه`;

            printDocument('💰 قائمة التدفقات النقدية', content);
        }

        function printSalesInvoice() {
            const content = `فاتورة مبيعات

رقم الفاتورة: INV-${Date.now()}

بيانات العميل:
العميل: شركة الأهرام للطباعة والنشر
العنوان: القاهرة - مصر الجديدة
الرقم الضريبي: 123456789

تفاصيل الفاتورة:
المنتج: ورق كتابة 80 جرام - مقاس A4
الكمية: 100 طن
السعر: 25,000 جنيه/طن
الإجمالي قبل الضريبة: 2,500,000 جنيه
ضريبة القيمة المضافة (14%): 350,000 جنيه
الإجمالي شامل الضريبة: 2,850,000 جنيه

شروط السداد: 30 يوم من تاريخ الفاتورة
طريقة السداد: شيكات مؤجلة`;

            printDocument('📄 فاتورة مبيعات', content);
        }

        // تفعيل Enter للدخول
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && document.getElementById('loginScreen').style.display !== 'none') {
                login();
            }
        });

        console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
        console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
    </script>
</body>
</html>
