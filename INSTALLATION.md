# 🚀 دليل التثبيت والتشغيل - نظام المحاسبة والمراجعة الداخلية

## 📋 المتطلبات الأساسية

### 1. تثبيت Node.js
- **حمل Node.js** من: https://nodejs.org/en/download
- **اختر الإصدار LTS** (الموصى به)
- **شغل الملف** واتبع خطوات التثبيت
- **تأكد من التثبيت** بفتح Command Prompt وكتابة:
  ```bash
  node --version
  npm --version
  ```

### 2. تثبيت MySQL
- **حمل MySQL** من: https://dev.mysql.com/downloads/mysql/
- **أو استخدم XAMPP** من: https://www.apachefriends.org/
- **تأكد من تشغيل MySQL Server**

## 🔧 خطوات التثبيت

### الخطوة 1: تحضير المشروع
```bash
# افتح Command Prompt في مجلد المشروع
cd "c:\Users\<USER>\Downloads\تجربه عمل برنامج حسابات"

# تثبيت حزم الخادم
npm install
```

### الخطوة 2: إعداد قاعدة البيانات
```bash
# تشغيل إعداد قاعدة البيانات
npm run setup
```

### الخطوة 3: تثبيت واجهة المستخدم
```bash
# الانتقال لمجلد العميل
cd client

# تثبيت حزم React
npm install

# العودة للمجلد الرئيسي
cd ..
```

### الخطوة 4: تشغيل النظام
```bash
# تشغيل الخادم (في terminal منفصل)
npm start

# تشغيل واجهة المستخدم (في terminal آخر)
cd client
npm start
```

## 🌐 الوصول للنظام

### الخادم (Backend)
- **الرابط**: http://localhost:5000
- **API**: http://localhost:5000/api

### واجهة المستخدم (Frontend)
- **الرابط**: http://localhost:3000

### بيانات تسجيل الدخول
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📊 المميزات المتاحة

### ✅ تم تطويرها بالكامل:
1. **🔐 نظام المصادقة والأمان**
   - تسجيل دخول آمن
   - نظام صلاحيات متقدم
   - تشفير كلمات المرور

2. **📊 الحسابات المالية والتكاليف**
   - إدارة شجرة الحسابات
   - أذونات الصرف والتوريد
   - القيود المحاسبية
   - ميزان المراجعة

3. **🏪 إدارة المخازن**
   - 6 أنواع مخازن مختلفة
   - بطاقات الأصناف
   - حركة المخزون
   - نظام الجرد

4. **🛒 إدارة المشتريات**
   - طلبات الشراء
   - إدارة الموردين
   - أوامر التوريد

5. **💰 إدارة الخزينة**
   - خزائن منفصلة
   - حركات يومية
   - سجلات الشيكات

6. **📄 مخازن الورق**
   - إنتاج يومي
   - شحنات العملاء
   - تصاريح الخروج

7. **📈 التقارير والمراجعة**
   - تقارير شاملة
   - تصدير PDF
   - سجل مراجعة داخلية

8. **📥📤 الاستيراد والتصدير**
   - استيراد من Excel
   - تصدير PDF
   - قوالب جاهزة

## 🛠️ استكشاف الأخطاء

### مشكلة: "node is not recognized"
**الحل**: تأكد من تثبيت Node.js وإعادة تشغيل Command Prompt

### مشكلة: "Cannot connect to MySQL"
**الحل**: 
1. تأكد من تشغيل MySQL Server
2. تحقق من إعدادات قاعدة البيانات في ملف `.env`

### مشكلة: "Port 5000 already in use"
**الحل**: 
```bash
# إيقاف العملية المستخدمة للمنفذ
netstat -ano | findstr :5000
taskkill /PID [رقم العملية] /F
```

### مشكلة: "Module not found"
**الحل**: 
```bash
# إعادة تثبيت الحزم
rm -rf node_modules
npm install
```

## 📁 هيكل المشروع

```
📁 نظام المحاسبة/
├── 📄 server.js              # الخادم الرئيسي
├── 📄 setup.js               # إعداد قاعدة البيانات
├── 📄 package.json           # إعدادات المشروع
├── 📁 config/                # إعدادات النظام
├── 📁 database/              # قاعدة البيانات
├── 📁 routes/                # مسارات API
├── 📁 middleware/            # وسطاء الأمان
├── 📁 utils/                 # أدوات مساعدة
├── 📁 uploads/               # ملفات مرفوعة
└── 📁 client/                # واجهة المستخدم
    ├── 📁 public/
    ├── 📁 src/
    └── 📄 package.json
```

## 🔧 إعدادات متقدمة

### تخصيص قاعدة البيانات
عدل ملف `.env`:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=accounting_system
```

### تخصيص المنافذ
```env
PORT=5000                    # منفذ الخادم
REACT_APP_API_URL=http://localhost:5000/api
```

### إعدادات الأمان
```env
JWT_SECRET=your_secret_key
BCRYPT_ROUNDS=12
```

## 📞 الدعم والمساعدة

### الملفات المهمة:
- `README.md` - دليل شامل
- `INSTALLATION.md` - هذا الملف
- `database/schema.sql` - هيكل قاعدة البيانات

### في حالة المشاكل:
1. تحقق من ملفات السجل
2. راجع رسائل الخطأ في Console
3. تأكد من تشغيل جميع الخدمات

## 🎯 الخطوات التالية

بعد التثبيت الناجح:

1. **استكشف النظام** - جرب جميع الوحدات
2. **أضف بيانات تجريبية** - لاختبار الوظائف
3. **خصص النظام** - حسب احتياجاتك
4. **أنشئ نسخة احتياطية** - من قاعدة البيانات

## 🚀 تطوير إضافي

### إضافة مميزات جديدة:
- تقارير مخصصة
- ربط مع أنظمة خارجية
- تطبيق موبايل
- إشعارات تلقائية

### تحسينات الأداء:
- فهرسة قاعدة البيانات
- تحسين الاستعلامات
- ضغط الملفات
- تخزين مؤقت

---

**تم التطوير بواسطة**: Ashraf  
**الإصدار**: 1.0.0  
**التاريخ**: 2024
