import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// إعداد axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة التوكن تلقائياً للطلبات
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// معالجة الاستجابات والأخطاء
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // إزالة التوكن المنتهي الصلاحية
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    
    return Promise.reject({
      message: error.response?.data?.message || 'حدث خطأ في الخادم',
      status: error.response?.status,
      data: error.response?.data
    });
  }
);

export const authService = {
  // تسجيل الدخول
  async login(username, password) {
    try {
      const response = await api.post('/auth/login', {
        username,
        password
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // الحصول على بيانات المستخدم الحالي
  async getCurrentUser() {
    try {
      const response = await api.get('/auth/me');
      return response.data.user;
    } catch (error) {
      throw error;
    }
  },

  // تغيير كلمة المرور
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await api.put('/auth/change-password', {
        currentPassword,
        newPassword
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // إنشاء مستخدم جديد (للمديرين)
  async createUser(userData) {
    try {
      const response = await api.post('/auth/create-user', userData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // الحصول على قائمة المستخدمين
  async getUsers() {
    try {
      const response = await api.get('/auth/users');
      return response.data.users;
    } catch (error) {
      throw error;
    }
  },

  // تسجيل الخروج
  logout() {
    localStorage.removeItem('token');
    window.location.href = '/login';
  }
};

export default api;
