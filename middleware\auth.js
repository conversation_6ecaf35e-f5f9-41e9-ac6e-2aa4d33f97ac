const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// التحقق من صحة التوكن
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'مطلوب تسجيل الدخول للوصول لهذه الصفحة'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // التحقق من وجود المستخدم في قاعدة البيانات
    const user = await executeQuery(
      'SELECT id, username, email, full_name, department, role, is_active FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (!user.length || !user[0].is_active) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود أو غير مفعل'
      });
    }

    req.user = user[0];
    next();
  } catch (error) {
    console.error('خطأ في التحقق من التوكن:', error);
    return res.status(403).json({
      success: false,
      message: 'توكن غير صالح'
    });
  }
};

// التحقق من الصلاحيات
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'مطلوب تسجيل الدخول'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول لهذه الصفحة'
      });
    }

    next();
  };
};

// التحقق من صلاحية القسم
const authorizeDepartment = (...departments) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'مطلوب تسجيل الدخول'
      });
    }

    if (!departments.includes(req.user.department) && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول لهذا القسم'
      });
    }

    next();
  };
};

// تسجيل عمليات المراجعة
const auditLog = (action, tableName) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // تسجيل العملية في سجل المراجعة
      if (req.user && res.statusCode < 400) {
        executeQuery(
          `INSERT INTO audit_log (table_name, record_id, action, new_values, user_id, ip_address, user_agent) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            tableName,
            req.recordId || 0,
            action,
            JSON.stringify(req.body),
            req.user.id,
            req.ip,
            req.get('User-Agent')
          ]
        ).catch(err => console.error('خطأ في تسجيل المراجعة:', err));
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

// التحقق من صحة البيانات المدخلة
const validateInput = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: error.details.map(detail => ({
          field: detail.path[0],
          message: detail.message
        }))
      });
    }
    
    next();
  };
};

// معدل الطلبات لكل مستخدم
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    if (!req.user) return next();
    
    const userId = req.user.id;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!requests.has(userId)) {
      requests.set(userId, []);
    }
    
    const userRequests = requests.get(userId);
    
    // إزالة الطلبات القديمة
    const validRequests = userRequests.filter(time => time > windowStart);
    
    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'تم تجاوز الحد الأقصى للطلبات، يرجى المحاولة لاحقاً'
      });
    }
    
    validRequests.push(now);
    requests.set(userId, validRequests);
    
    next();
  };
};

module.exports = {
  authenticateToken,
  authorize,
  authorizeDepartment,
  auditLog,
  validateInput,
  userRateLimit
};
