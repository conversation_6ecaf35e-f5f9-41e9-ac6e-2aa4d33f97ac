<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة والمراجعة الداخلية - واجهة مستقلة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status {
            background: rgba(0,255,0,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid rgba(0,255,0,0.5);
            text-align: center;
        }
        
        .tabs {
            display: flex;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            background: transparent;
            color: white;
            font-size: 16px;
        }
        
        .tab.active {
            background: rgba(255,215,0,0.3);
            color: #ffd700;
        }
        
        .tab:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            min-height: 500px;
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .data-table th {
            background: rgba(255,215,0,0.3);
            color: #ffd700;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }
        
        .btn {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,215,0,0.4);
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
        }
        
        .alert-info {
            background: rgba(0,123,255,0.2);
            border: 2px solid rgba(0,123,255,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 نظام المحاسبة والمراجعة الداخلية</h1>
            <p>واجهة مستقلة - تعمل بدون خادم</p>
            
            <div class="status">
                <h3>✅ النظام جاهز للاستخدام!</h3>
                <p>واجهة تفاعلية مع بيانات تجريبية | الإصدار 1.0.0</p>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showSection('dashboard')">🏠 لوحة التحكم</button>
            <button class="tab" onclick="showSection('users')">👥 إدارة المستخدمين</button>
            <button class="tab" onclick="showSection('financial')">💰 الحسابات المالية</button>
            <button class="tab" onclick="showSection('inventory')">🏪 المخازن</button>
            <button class="tab" onclick="showSection('purchasing')">🛒 المشتريات</button>
            <button class="tab" onclick="showSection('reports')">📈 التقارير</button>
            <button class="tab" onclick="showSection('settings')">⚙️ الإعدادات</button>
        </div>

        <div class="content">
            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم الرئيسية</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="dashboardUsers">4</div>
                        <div class="stat-label">المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">الحسابات المالية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">الأصناف</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2</div>
                        <div class="stat-label">الموردين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">375,000</div>
                        <div class="stat-label">إجمالي الأصول</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="dashboardActiveUsers">3</div>
                        <div class="stat-label">المستخدمين النشطين</div>
                    </div>
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>👥 إدارة المستخدمين</h3>
                        <p>إضافة وتعديل المستخدمين وتحديد الصلاحيات والأدوار</p>
                        <button class="btn" onclick="showSection('users')">إدارة المستخدمين</button>
                    </div>
                    <div class="card">
                        <h3>📊 الحسابات المالية</h3>
                        <p>إدارة شجرة الحسابات وأذونات الصرف والقيود المحاسبية</p>
                        <button class="btn" onclick="showSection('financial')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>🏪 إدارة المخازن</h3>
                        <p>بطاقات الأصناف وحركة المخزون ونظام الجرد</p>
                        <button class="btn" onclick="showSection('inventory')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>🛒 إدارة المشتريات</h3>
                        <p>طلبات الشراء والموردين وأوامر التوريد</p>
                        <button class="btn" onclick="showSection('purchasing')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير</h3>
                        <p>ميزان المراجعة وتقارير المخازن والتحليلات</p>
                        <button class="btn" onclick="showSection('reports')">عرض التفاصيل</button>
                    </div>
                </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div id="users" class="section">
                <h2>👥 إدارة المستخدمين</h2>

                <div class="alert alert-info">
                    <strong>إدارة المستخدمين:</strong> يمكنك إضافة وتعديل وحذف المستخدمين وتحديد صلاحياتهم
                </div>

                <div style="margin-bottom: 20px;">
                    <button class="btn" onclick="showAddUserForm()">➕ إضافة مستخدم جديد</button>
                    <button class="btn btn-secondary" onclick="exportUsers()">📤 تصدير قائمة المستخدمين</button>
                </div>

                <!-- نموذج إضافة مستخدم جديد -->
                <div id="addUserForm" style="display: none; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h3>➕ إضافة مستخدم جديد</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label>اسم المستخدم:</label>
                            <input type="text" id="newUsername" placeholder="أدخل اسم المستخدم">
                        </div>
                        <div class="form-group">
                            <label>الاسم الكامل:</label>
                            <input type="text" id="newFullName" placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني:</label>
                            <input type="email" id="newEmail" placeholder="أدخل البريد الإلكتروني">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="newPhone" placeholder="أدخل رقم الهاتف">
                        </div>
                        <div class="form-group">
                            <label>القسم:</label>
                            <select id="newDepartment">
                                <option value="">اختر القسم</option>
                                <option value="admin">الإدارة</option>
                                <option value="financial">الحسابات المالية</option>
                                <option value="inventory">المخازن</option>
                                <option value="purchasing">المشتريات</option>
                                <option value="treasury">الخزينة</option>
                                <option value="paper_warehouse">مخازن الورق</option>
                                <option value="audit">المراجعة الداخلية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الدور:</label>
                            <select id="newRole">
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير القسم</option>
                                <option value="supervisor">مشرف</option>
                                <option value="user">مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور:</label>
                            <input type="password" id="newPassword" placeholder="أدخل كلمة المرور">
                        </div>
                        <div class="form-group">
                            <label>تأكيد كلمة المرور:</label>
                            <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="btn" onclick="addNewUser()">✅ إضافة المستخدم</button>
                        <button class="btn btn-secondary" onclick="hideAddUserForm()">❌ إلغاء</button>
                    </div>
                </div>

                <!-- جدول المستخدمين -->
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <tr>
                            <td>1</td>
                            <td>admin</td>
                            <td>مدير النظام</td>
                            <td><EMAIL></td>
                            <td>الإدارة</td>
                            <td>مدير النظام</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-01-01</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(1)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(1)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>accountant</td>
                            <td>محمد أحمد</td>
                            <td><EMAIL></td>
                            <td>الحسابات المالية</td>
                            <td>مستخدم عادي</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-01-15</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(2)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(2)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>warehouse_manager</td>
                            <td>علي محمود</td>
                            <td><EMAIL></td>
                            <td>المخازن</td>
                            <td>مدير القسم</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-02-01</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(3)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(3)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>purchasing_user</td>
                            <td>فاطمة سالم</td>
                            <td><EMAIL></td>
                            <td>المشتريات</td>
                            <td>مستخدم عادي</td>
                            <td><span style="color: #FF9800;">معطل</span></td>
                            <td>2024-02-10</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(4)">✏️ تعديل</button>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(4)">🔓 تفعيل</button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- إحصائيات المستخدمين -->
                <div class="stats-grid" style="margin-top: 30px;">
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">4</div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeUsers">3</div>
                        <div class="stat-label">المستخدمين النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="adminUsers">1</div>
                        <div class="stat-label">مديري النظام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="departmentCount">4</div>
                        <div class="stat-label">الأقسام المختلفة</div>
                    </div>
                </div>
            </div>

            <!-- الحسابات المالية -->
            <div id="financial" class="section">
                <h2>💰 الحسابات المالية</h2>
                
                <div class="alert alert-info">
                    <strong>ملاحظة:</strong> هذه بيانات تجريبية لعرض إمكانيات النظام
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الحساب</th>
                            <th>اسم الحساب</th>
                            <th>نوع الحساب</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1000</td>
                            <td>الأصول</td>
                            <td>أصول</td>
                            <td>150,000</td>
                        </tr>
                        <tr>
                            <td>1100</td>
                            <td>الأصول المتداولة</td>
                            <td>أصول</td>
                            <td>75,000</td>
                        </tr>
                        <tr>
                            <td>2000</td>
                            <td>الخصوم</td>
                            <td>خصوم</td>
                            <td>50,000</td>
                        </tr>
                        <tr>
                            <td>3000</td>
                            <td>حقوق الملكية</td>
                            <td>حقوق ملكية</td>
                            <td>100,000</td>
                        </tr>
                    </tbody>
                </table>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إنشاء إذن صرف</h3>
                        <div class="form-group">
                            <label>نوع الإذن:</label>
                            <select>
                                <option>إذن صرف نقدي</option>
                                <option>إذن صرف شيك</option>
                                <option>إذن صرف تحويل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المبلغ:</label>
                            <input type="number" placeholder="أدخل المبلغ">
                        </div>
                        <div class="form-group">
                            <label>الوصف:</label>
                            <input type="text" placeholder="وصف الإذن">
                        </div>
                        <button class="btn">إنشاء الإذن</button>
                    </div>
                    <div class="card">
                        <h3>📊 ملخص الحسابات</h3>
                        <p><strong>إجمالي الأصول:</strong> 225,000</p>
                        <p><strong>إجمالي الخصوم:</strong> 50,000</p>
                        <p><strong>حقوق الملكية:</strong> 100,000</p>
                        <p><strong>صافي الأصول:</strong> 175,000</p>
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <h2>🏪 إدارة المخازن</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>الوحدة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ITM001</td>
                            <td>مواد خام أساسية</td>
                            <td>مواد خام</td>
                            <td>500</td>
                            <td>كيلو</td>
                            <td>طبيعي</td>
                        </tr>
                        <tr>
                            <td>ITM002</td>
                            <td>وقود ديزل</td>
                            <td>وقود</td>
                            <td>1000</td>
                            <td>لتر</td>
                            <td>طبيعي</td>
                        </tr>
                        <tr>
                            <td>ITM003</td>
                            <td>قطع غيار</td>
                            <td>قطع غيار</td>
                            <td>50</td>
                            <td>قطعة</td>
                            <td>نقص مخزون</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- المشتريات -->
            <div id="purchasing" class="section">
                <h2>🛒 إدارة المشتريات</h2>

                <h3>🏢 قائمة الموردين</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم المورد</th>
                            <th>اسم المورد</th>
                            <th>رقم الاتصال</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>SUP001</td>
                            <td>شركة المواد الخام</td>
                            <td>***********</td>
                            <td>نشط</td>
                        </tr>
                        <tr>
                            <td>SUP002</td>
                            <td>مؤسسة الوقود</td>
                            <td>01987654321</td>
                            <td>نشط</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <h2>📈 التقارير والتحليلات</h2>

                <div class="cards-grid">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p>إجمالي المدين: 225,000</p>
                        <p>إجمالي الدائن: 150,000</p>
                        <button class="btn">عرض التفاصيل</button>
                        <button class="btn btn-secondary">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>📊 تقرير المخازن</h3>
                        <p>إجمالي الأصناف: 3</p>
                        <p>قيمة المخزون: 77,500</p>
                        <button class="btn">عرض التفاصيل</button>
                        <button class="btn btn-secondary">تصدير Excel</button>
                    </div>
                </div>
            </div>

            <!-- الإعدادات -->
            <div id="settings" class="section">
                <h2>⚙️ إعدادات النظام</h2>

                <div class="alert alert-success">
                    <strong>معلومات النظام:</strong><br>
                    الإصدار: 1.0.0<br>
                    المطور: Ashraf<br>
                    النوع: واجهة مستقلة<br>
                    التاريخ: 2024
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>👤 بيانات المستخدم</h3>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>الاسم الكامل:</strong> مدير النظام</p>
                        <p><strong>القسم:</strong> الإدارة</p>
                        <p><strong>الدور:</strong> مدير</p>
                    </div>
                    <div class="card">
                        <h3>🔧 إعدادات النظام</h3>
                        <button class="btn">تغيير كلمة المرور</button>
                        <button class="btn btn-secondary">نسخ احتياطي</button>
                        <button class="btn btn-secondary">استيراد بيانات</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));
            
            // إزالة التفعيل من جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // إظهار القسم المطلوب
            document.getElementById(sectionId).classList.add('active');
            
            // تفعيل التبويب المطلوب
            event.target.classList.add('active');
        }

        // بيانات المستخدمين (محاكاة قاعدة البيانات)
        let users = [
            {
                id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                phone: '***********',
                department: 'admin',
                role: 'admin',
                status: 'active',
                createdAt: '2024-01-01'
            },
            {
                id: 2,
                username: 'accountant',
                fullName: 'محمد أحمد',
                email: '<EMAIL>',
                phone: '***********',
                department: 'financial',
                role: 'user',
                status: 'active',
                createdAt: '2024-01-15'
            },
            {
                id: 3,
                username: 'warehouse_manager',
                fullName: 'علي محمود',
                email: '<EMAIL>',
                phone: '***********',
                department: 'inventory',
                role: 'manager',
                status: 'active',
                createdAt: '2024-02-01'
            },
            {
                id: 4,
                username: 'purchasing_user',
                fullName: 'فاطمة سالم',
                email: '<EMAIL>',
                phone: '***********',
                department: 'purchasing',
                role: 'user',
                status: 'inactive',
                createdAt: '2024-02-10'
            }
        ];

        // إظهار نموذج إضافة مستخدم
        function showAddUserForm() {
            document.getElementById('addUserForm').style.display = 'block';
        }

        // إخفاء نموذج إضافة مستخدم
        function hideAddUserForm() {
            document.getElementById('addUserForm').style.display = 'none';
            clearUserForm();
        }

        // مسح نموذج المستخدم
        function clearUserForm() {
            document.getElementById('newUsername').value = '';
            document.getElementById('newFullName').value = '';
            document.getElementById('newEmail').value = '';
            document.getElementById('newPhone').value = '';
            document.getElementById('newDepartment').value = '';
            document.getElementById('newRole').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }

        // إضافة مستخدم جديد
        function addNewUser() {
            const username = document.getElementById('newUsername').value;
            const fullName = document.getElementById('newFullName').value;
            const email = document.getElementById('newEmail').value;
            const phone = document.getElementById('newPhone').value;
            const department = document.getElementById('newDepartment').value;
            const role = document.getElementById('newRole').value;
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // التحقق من البيانات
            if (!username || !fullName || !email || !department || !role || !password) {
                alert('❌ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (password !== confirmPassword) {
                alert('❌ كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            if (users.find(user => user.username === username)) {
                alert('❌ اسم المستخدم موجود بالفعل');
                return;
            }

            // إضافة المستخدم الجديد
            const newUser = {
                id: users.length + 1,
                username: username,
                fullName: fullName,
                email: email,
                phone: phone,
                department: department,
                role: role,
                status: 'active',
                createdAt: new Date().toISOString().split('T')[0]
            };

            users.push(newUser);
            updateUsersTable();
            updateUserStats();
            hideAddUserForm();

            alert('✅ تم إضافة المستخدم بنجاح!');
        }

        // تحديث جدول المستخدمين
        function updateUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');

                const departmentNames = {
                    'admin': 'الإدارة',
                    'financial': 'الحسابات المالية',
                    'inventory': 'المخازن',
                    'purchasing': 'المشتريات',
                    'treasury': 'الخزينة',
                    'paper_warehouse': 'مخازن الورق',
                    'audit': 'المراجعة الداخلية'
                };

                const roleNames = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير القسم',
                    'supervisor': 'مشرف',
                    'user': 'مستخدم عادي'
                };

                const statusColor = user.status === 'active' ? '#4CAF50' : '#FF9800';
                const statusText = user.status === 'active' ? 'نشط' : 'معطل';
                const actionButton = user.status === 'active' ?
                    `<button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(${user.id})">🔒 تعطيل</button>` :
                    `<button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(${user.id})">🔓 تفعيل</button>`;

                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${departmentNames[user.department] || user.department}</td>
                    <td>${roleNames[user.role] || user.role}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td>${user.createdAt}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(${user.id})">✏️ تعديل</button>
                        ${actionButton}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث إحصائيات المستخدمين
        function updateUserStats() {
            const totalUsers = users.length;
            const activeUsers = users.filter(user => user.status === 'active').length;
            const adminUsers = users.filter(user => user.role === 'admin').length;
            const departments = [...new Set(users.map(user => user.department))].length;

            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('adminUsers').textContent = adminUsers;
            document.getElementById('departmentCount').textContent = departments;

            // تحديث إحصائيات لوحة التحكم
            document.getElementById('dashboardUsers').textContent = totalUsers;
            document.getElementById('dashboardActiveUsers').textContent = activeUsers;
        }

        // تبديل حالة المستخدم
        function toggleUserStatus(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                user.status = user.status === 'active' ? 'inactive' : 'active';
                updateUsersTable();
                updateUserStats();

                const statusText = user.status === 'active' ? 'تم تفعيل' : 'تم تعطيل';
                alert(`✅ ${statusText} المستخدم ${user.fullName} بنجاح`);
            }
        }

        // تعديل مستخدم
        function editUser(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                const newFullName = prompt('أدخل الاسم الكامل الجديد:', user.fullName);
                if (newFullName && newFullName !== user.fullName) {
                    user.fullName = newFullName;
                    updateUsersTable();
                    alert('✅ تم تحديث بيانات المستخدم بنجاح');
                }
            }
        }

        // تصدير قائمة المستخدمين
        function exportUsers() {
            let csvContent = "الرقم,اسم المستخدم,الاسم الكامل,البريد الإلكتروني,القسم,الدور,الحالة,تاريخ الإنشاء\n";

            users.forEach(user => {
                const departmentNames = {
                    'admin': 'الإدارة',
                    'financial': 'الحسابات المالية',
                    'inventory': 'المخازن',
                    'purchasing': 'المشتريات',
                    'treasury': 'الخزينة',
                    'paper_warehouse': 'مخازن الورق',
                    'audit': 'المراجعة الداخلية'
                };

                const roleNames = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير القسم',
                    'supervisor': 'مشرف',
                    'user': 'مستخدم عادي'
                };

                csvContent += `${user.id},${user.username},${user.fullName},${user.email},${departmentNames[user.department]},${roleNames[user.role]},${user.status === 'active' ? 'نشط' : 'معطل'},${user.createdAt}\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'users_list.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير قائمة المستخدمين بنجاح!');
        }

        // رسالة ترحيب عند تحميل الصفحة
        window.addEventListener('load', () => {
            updateUserStats();
            setTimeout(() => {
                alert('🎉 مرحباً بك في نظام المحاسبة والمراجعة الداخلية!\n\nتم إضافة شاشة إدارة المستخدمين الكاملة.\n\nيمكنك الآن إضافة وتعديل وإدارة المستخدمين بسهولة.');
            }, 1000);
        });
    </script>
</body>
</html>
