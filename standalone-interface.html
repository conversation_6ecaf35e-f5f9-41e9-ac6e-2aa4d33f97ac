<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة والمراجعة الداخلية - واجهة مستقلة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status {
            background: rgba(0,255,0,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid rgba(0,255,0,0.5);
            text-align: center;
        }
        
        .tabs {
            display: flex;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            background: transparent;
            color: white;
            font-size: 16px;
        }
        
        .tab.active {
            background: rgba(255,215,0,0.3);
            color: #ffd700;
        }
        
        .tab:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            min-height: 500px;
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .data-table th {
            background: rgba(255,215,0,0.3);
            color: #ffd700;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }
        
        .btn {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,215,0,0.4);
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
        }
        
        .alert-info {
            background: rgba(0,123,255,0.2);
            border: 2px solid rgba(0,123,255,0.5);
        }

        .footer {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 15px;
            margin-top: 30px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .developer-info {
            background: rgba(255,215,0,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid rgba(255,215,0,0.3);
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255,215,0,0.5);
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 50%, #A5D6A7 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .company-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(129,199,132,0.8) 0%, transparent 50%),
                linear-gradient(135deg, #4CAF50 0%, #66BB6A 25%, #81C784 50%, #A5D6A7 75%, #C8E6C9 100%);
            border-radius: 50%;
        }

        .company-logo::after {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            right: 10%;
            bottom: 10%;
            background:
                radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .company-logo-text {
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            font-family: 'Arial Black', Arial, sans-serif;
            background: linear-gradient(45deg, #ffffff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            0% {
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px rgba(255,215,0,0.3);
            }
            100% {
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px rgba(255,215,0,0.6);
            }
        }

        .company-logo:hover {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .company-logo:hover .company-logo-text {
            animation: logoSpin 1s ease-in-out;
        }

        @keyframes logoSpin {
            0% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
            100% { transform: rotate(360deg); }
        }

        .copyright {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 10px;
        }

        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .login-header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .login-header h2 {
            font-size: 1.5em;
            margin-bottom: 20px;
        }

        .login-form {
            margin: 30px 0;
        }

        .login-form h3 {
            color: #ffd700;
            margin-bottom: 20px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            margin-top: 10px;
        }

        .login-help {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: right;
        }

        .demo-accounts {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .demo-account {
            background: rgba(255,215,0,0.1);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255,215,0,0.3);
            cursor: pointer;
            transition: all 0.3s;
        }

        .demo-account:hover {
            background: rgba(255,215,0,0.2);
            transform: translateY(-2px);
        }

        .demo-account code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            color: #ffd700;
        }

        .error-message {
            background: rgba(255,0,0,0.2);
            border: 2px solid rgba(255,0,0,0.5);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            color: #ff6b6b;
        }

        .success-message {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="company-logo" style="width: 120px; height: 120px; font-size: 36px; margin: 0 auto 20px;">
                    <span class="company-logo-text">AG</span>
                </div>
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
                <p>تسجيل الدخول للنظام</p>
            </div>

            <div class="login-form">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="loginMessage"></div>
                <form id="mainLoginForm">
                    <div class="form-group">
                        <label for="loginUsername">👤 اسم المستخدم:</label>
                        <input type="text" id="loginUsername" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">🔒 كلمة المرور:</label>
                        <input type="password" id="loginPassword" placeholder="أدخل كلمة المرور" required>
                    </div>
                    <button type="submit" class="btn login-btn">🚀 دخول النظام</button>
                </form>

                <div class="login-help">
                    <h4>📋 بيانات تجريبية للدخول:</h4>
                    <div class="demo-accounts">
                        <div class="demo-account" onclick="fillLoginData('admin', 'admin123')">
                            <strong>👨‍💼 مدير النظام:</strong><br>
                            اسم المستخدم: <code>admin</code><br>
                            كلمة المرور: <code>admin123</code>
                        </div>
                        <div class="demo-account" onclick="fillLoginData('accountant', 'acc123')">
                            <strong>👨‍💰 محاسب:</strong><br>
                            اسم المستخدم: <code>accountant</code><br>
                            كلمة المرور: <code>acc123</code>
                        </div>
                        <div class="demo-account" onclick="fillLoginData('warehouse', 'wh123')">
                            <strong>👨‍🏭 مدير مخازن:</strong><br>
                            اسم المستخدم: <code>warehouse</code><br>
                            كلمة المرور: <code>wh123</code>
                        </div>
                    </div>
                </div>
            </div>

            <div class="developer-info" style="margin-top: 30px;">
                <div class="logo-container">
                    <div class="company-logo" style="width: 60px; height: 60px; font-size: 18px;">
                        <span class="company-logo-text">AG</span>
                    </div>
                    <div>
                        <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                        <span style="font-size: 14px;">AG Technology Systems</span>
                    </div>
                </div>
                <div class="copyright">
                    © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز
                </div>
            </div>
        </div>
    </div>

    <div class="container" id="mainSystem" style="display: none;">
        <div class="header">
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            <p>واجهة مستقلة - تعمل بدون خادم</p>

            <div class="status">
                <h3>✅ النظام جاهز للاستخدام!</h3>
                <p>شركة مصر ادفو للب وورق الكتابة والطباعة | الإصدار 1.0.0</p>
            </div>

            <!-- معلومات الشركة المصممة -->
            <div class="developer-info">
                <div class="logo-container">
                    <div class="company-logo" style="width: 120px; height: 120px; font-size: 36px;">
                        <span class="company-logo-text">AG</span>
                    </div>
                    <div>
                        <h3 style="color: #ffd700; margin: 0; font-size: 24px;">🏢 شركة ايه جي تكنولوجي سيستميز</h3>
                        <h4 style="margin: 5px 0; font-size: 18px; color: #81C784;">AG Technology Systems</h4>
                        <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                        <p style="margin: 0; font-size: 12px; opacity: 0.8;">🎯 حلول تقنية متطورة للشركات والمؤسسات</p>
                    </div>
                </div>
                <div class="copyright" style="font-size: 14px;">
                    © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | AG Technology Systems<br>
                    🌟 نفخر بتقديم نظام المحاسبة والمراجعة الداخلية لشركة مصر ادفو للب وورق الكتابة والطباعة
                </div>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showSection('dashboard')">🏠 لوحة التحكم</button>
            <button class="tab" onclick="showSection('operations')">⚡ العمليات اليومية</button>
            <button class="tab" onclick="showSection('users')">👥 إدارة المستخدمين</button>
            <button class="tab" onclick="showSection('sales_cycle')">📋 دورة المبيعات</button>
            <button class="tab" onclick="showSection('purchase_cycle')">📦 دورة المشتريات</button>
            <button class="tab" onclick="showSection('financial')">💰 الحسابات المالية</button>
            <button class="tab" onclick="showSection('inventory')">🏪 المخازن</button>
            <button class="tab" onclick="showSection('purchasing')">🛒 المشتريات</button>
            <button class="tab" onclick="showSection('reports')">📈 التقارير</button>
            <button class="tab" onclick="showSection('settings')">⚙️ الإعدادات</button>
        </div>

        <div class="content">
            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم الرئيسية</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="dashboardUsers">4</div>
                        <div class="stat-label">المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">الحسابات المالية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">الأصناف</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2</div>
                        <div class="stat-label">الموردين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">375,000</div>
                        <div class="stat-label">إجمالي الأصول</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="dashboardActiveUsers">3</div>
                        <div class="stat-label">المستخدمين النشطين</div>
                    </div>
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>👥 إدارة المستخدمين</h3>
                        <p>إضافة وتعديل المستخدمين وتحديد الصلاحيات والأدوار</p>
                        <button class="btn" onclick="showSection('users')">إدارة المستخدمين</button>
                    </div>
                    <div class="card">
                        <h3>📋 دورة المبيعات</h3>
                        <p>الدورة المستندية الكاملة للمبيعات - شركة ورق الكتابة (19 مرحلة)</p>
                        <button class="btn" onclick="showSection('sales_cycle')">دورة المبيعات</button>
                    </div>
                    <div class="card">
                        <h3>📦 دورة المشتريات</h3>
                        <p>الدورة المستندية للبضائع الواردة والتعليمات الإدارية (7 مراحل)</p>
                        <button class="btn" onclick="showSection('purchase_cycle')">دورة المشتريات</button>
                    </div>
                    <div class="card">
                        <h3>📊 الحسابات المالية</h3>
                        <p>إدارة شجرة الحسابات وأذونات الصرف والقيود المحاسبية</p>
                        <button class="btn" onclick="showSection('financial')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>🏪 إدارة المخازن</h3>
                        <p>بطاقات الأصناف وحركة المخزون ونظام الجرد</p>
                        <button class="btn" onclick="showSection('inventory')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>🛒 إدارة المشتريات</h3>
                        <p>طلبات الشراء والموردين وأوامر التوريد</p>
                        <button class="btn" onclick="showSection('purchasing')">عرض التفاصيل</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير</h3>
                        <p>ميزان المراجعة وتقارير المخازن والتحليلات</p>
                        <button class="btn" onclick="showSection('reports')">عرض التفاصيل</button>
                    </div>
                </div>
            </div>

            <!-- العمليات اليومية -->
            <div id="operations" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">⚡ العمليات اليومية</h2>
                    <p style="font-size: 14px; opacity: 0.9;">إدخال وإدارة العمليات اليومية للشركة</p>
                </div>

                <!-- عمليات المبيعات -->
                <h3>💰 عمليات المبيعات</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إصدار فاتورة مبيعات</h3>
                        <div class="form-group">
                            <label>العميل:</label>
                            <select id="salesCustomer">
                                <option value="">اختر العميل</option>
                                <option value="customer1">شركة الأهرام للطباعة</option>
                                <option value="customer2">مؤسسة النيل للنشر</option>
                                <option value="customer3">شركة المستقبل للورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المنتج:</label>
                            <select id="salesProduct">
                                <option value="">اختر المنتج</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="paper45">ورق صحف 45 جرام</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية (طن):</label>
                            <input type="number" id="salesQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر (جنيه/طن):</label>
                            <input type="number" id="salesPrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createSalesInvoice()">📄 إصدار الفاتورة</button>
                    </div>

                    <div class="card">
                        <h3>💳 تحصيل من العملاء</h3>
                        <div class="form-group">
                            <label>العميل:</label>
                            <select id="collectionCustomer">
                                <option value="">اختر العميل</option>
                                <option value="customer1">شركة الأهرام للطباعة</option>
                                <option value="customer2">مؤسسة النيل للنشر</option>
                                <option value="customer3">شركة المستقبل للورق</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المبلغ المحصل:</label>
                            <input type="number" id="collectionAmount" placeholder="أدخل المبلغ">
                        </div>
                        <div class="form-group">
                            <label>طريقة التحصيل:</label>
                            <select id="collectionMethod">
                                <option value="cash">نقدي</option>
                                <option value="check">شيك</option>
                                <option value="transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        <button class="btn" onclick="recordCollection()">💰 تسجيل التحصيل</button>
                    </div>
                </div>

                <!-- عمليات المشتريات -->
                <h3>🛒 عمليات المشتريات</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📦 إدخال فاتورة مشتريات</h3>
                        <div class="form-group">
                            <label>المورد:</label>
                            <select id="purchaseSupplier">
                                <option value="">اختر المورد</option>
                                <option value="supplier1">شركة المواد الخام المصرية</option>
                                <option value="supplier2">مؤسسة الكيماويات الصناعية</option>
                                <option value="supplier3">شركة الوقود والطاقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="purchaseItem">
                                <option value="">اختر الصنف</option>
                                <option value="pulp">لب الورق</option>
                                <option value="chemicals">كيماويات</option>
                                <option value="fuel">وقود ديزل</option>
                                <option value="spare">قطع غيار</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية:</label>
                            <input type="number" id="purchaseQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>السعر:</label>
                            <input type="number" id="purchasePrice" placeholder="أدخل السعر">
                        </div>
                        <button class="btn" onclick="createPurchaseInvoice()">📦 إدخال الفاتورة</button>
                    </div>

                    <div class="card">
                        <h3>💸 سداد للموردين</h3>
                        <div class="form-group">
                            <label>المورد:</label>
                            <select id="paymentSupplier">
                                <option value="">اختر المورد</option>
                                <option value="supplier1">شركة المواد الخام المصرية</option>
                                <option value="supplier2">مؤسسة الكيماويات الصناعية</option>
                                <option value="supplier3">شركة الوقود والطاقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المبلغ المسدد:</label>
                            <input type="number" id="paymentAmount" placeholder="أدخل المبلغ">
                        </div>
                        <div class="form-group">
                            <label>طريقة السداد:</label>
                            <select id="paymentMethod">
                                <option value="cash">نقدي</option>
                                <option value="check">شيك</option>
                                <option value="transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        <button class="btn" onclick="recordPayment()">💸 تسجيل السداد</button>
                    </div>
                </div>

                <!-- عمليات المخازن -->
                <h3>🏪 عمليات المخازن</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📥 إضافة للمخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="addInventoryItem">
                                <option value="">اختر الصنف</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="paper45">ورق صحف 45 جرام</option>
                                <option value="pulp">لب الورق</option>
                                <option value="chemicals">كيماويات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المضافة:</label>
                            <input type="number" id="addInventoryQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>سبب الإضافة:</label>
                            <select id="addInventoryReason">
                                <option value="production">إنتاج</option>
                                <option value="purchase">مشتريات</option>
                                <option value="return">مرتجع</option>
                                <option value="adjustment">تسوية</option>
                            </select>
                        </div>
                        <button class="btn" onclick="addToInventory()">📥 إضافة للمخزون</button>
                    </div>

                    <div class="card">
                        <h3>📤 صرف من المخزون</h3>
                        <div class="form-group">
                            <label>الصنف:</label>
                            <select id="issueInventoryItem">
                                <option value="">اختر الصنف</option>
                                <option value="paper80">ورق كتابة 80 جرام</option>
                                <option value="paper70">ورق طباعة 70 جرام</option>
                                <option value="paper45">ورق صحف 45 جرام</option>
                                <option value="pulp">لب الورق</option>
                                <option value="chemicals">كيماويات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الكمية المصروفة:</label>
                            <input type="number" id="issueInventoryQuantity" placeholder="أدخل الكمية">
                        </div>
                        <div class="form-group">
                            <label>سبب الصرف:</label>
                            <select id="issueInventoryReason">
                                <option value="sales">مبيعات</option>
                                <option value="production">إنتاج</option>
                                <option value="damage">تالف</option>
                                <option value="adjustment">تسوية</option>
                            </select>
                        </div>
                        <button class="btn" onclick="issueFromInventory()">📤 صرف من المخزون</button>
                    </div>
                </div>

                <!-- فوتر العمليات اليومية -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo"><span class="company-logo-text">AG</span></div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام العمليات اليومية</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div id="users" class="section">
                <h2>👥 إدارة المستخدمين</h2>

                <div class="alert alert-info">
                    <strong>إدارة المستخدمين:</strong> يمكنك إضافة وتعديل وحذف المستخدمين وتحديد صلاحياتهم
                </div>

                <div style="margin-bottom: 20px;">
                    <button class="btn" onclick="showAddUserForm()">➕ إضافة مستخدم جديد</button>
                    <button class="btn btn-secondary" onclick="exportUsers()">📤 تصدير قائمة المستخدمين</button>
                </div>

                <!-- نموذج إضافة مستخدم جديد -->
                <div id="addUserForm" style="display: none; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h3>➕ إضافة مستخدم جديد</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label>اسم المستخدم:</label>
                            <input type="text" id="newUsername" placeholder="أدخل اسم المستخدم">
                        </div>
                        <div class="form-group">
                            <label>الاسم الكامل:</label>
                            <input type="text" id="newFullName" placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني:</label>
                            <input type="email" id="newEmail" placeholder="أدخل البريد الإلكتروني">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="newPhone" placeholder="أدخل رقم الهاتف">
                        </div>
                        <div class="form-group">
                            <label>القسم:</label>
                            <select id="newDepartment">
                                <option value="">اختر القسم</option>
                                <option value="admin">الإدارة</option>
                                <option value="financial">الحسابات المالية</option>
                                <option value="inventory">المخازن</option>
                                <option value="purchasing">المشتريات</option>
                                <option value="treasury">الخزينة</option>
                                <option value="paper_warehouse">مخازن الورق</option>
                                <option value="audit">المراجعة الداخلية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الدور:</label>
                            <select id="newRole">
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير القسم</option>
                                <option value="supervisor">مشرف</option>
                                <option value="user">مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور:</label>
                            <input type="password" id="newPassword" placeholder="أدخل كلمة المرور">
                        </div>
                        <div class="form-group">
                            <label>تأكيد كلمة المرور:</label>
                            <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="btn" onclick="addNewUser()">✅ إضافة المستخدم</button>
                        <button class="btn btn-secondary" onclick="hideAddUserForm()">❌ إلغاء</button>
                    </div>
                </div>

                <!-- جدول المستخدمين -->
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <tr>
                            <td>1</td>
                            <td>admin</td>
                            <td>مدير النظام</td>
                            <td><EMAIL></td>
                            <td>الإدارة</td>
                            <td>مدير النظام</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-01-01</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(1)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(1)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>accountant</td>
                            <td>محمد أحمد</td>
                            <td><EMAIL></td>
                            <td>الحسابات المالية</td>
                            <td>مستخدم عادي</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-01-15</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(2)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(2)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>warehouse_manager</td>
                            <td>علي محمود</td>
                            <td><EMAIL></td>
                            <td>المخازن</td>
                            <td>مدير القسم</td>
                            <td><span style="color: #4CAF50;">نشط</span></td>
                            <td>2024-02-01</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(3)">✏️ تعديل</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(3)">🔒 تعطيل</button>
                            </td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>purchasing_user</td>
                            <td>فاطمة سالم</td>
                            <td><EMAIL></td>
                            <td>المشتريات</td>
                            <td>مستخدم عادي</td>
                            <td><span style="color: #FF9800;">معطل</span></td>
                            <td>2024-02-10</td>
                            <td>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(4)">✏️ تعديل</button>
                                <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(4)">🔓 تفعيل</button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- إحصائيات المستخدمين -->
                <div class="stats-grid" style="margin-top: 30px;">
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">4</div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeUsers">3</div>
                        <div class="stat-label">المستخدمين النشطين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="adminUsers">1</div>
                        <div class="stat-label">مديري النظام</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="departmentCount">4</div>
                        <div class="stat-label">الأقسام المختلفة</div>
                    </div>
                </div>

                <!-- فوتر إدارة المستخدمين -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo"><span class="company-logo-text">AG</span></div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- دورة المبيعات -->
            <div id="sales_cycle" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">📋 الدورة المستندية للمبيعات</h2>
                    <p style="font-size: 14px; opacity: 0.9;">نظام شامل لإدارة عمليات البيع من طلب العميل حتى التحصيل</p>
                </div>

                <div class="alert alert-info">
                    <strong>شركة مصر ادفو للب وورق الكتابة والطباعة</strong><br>
                    الدورة المستندية للمبيعات - نظام شامل لإدارة عمليات البيع من طلب العميل حتى التحصيل
                </div>

                <!-- مراحل الدورة المستندية -->
                <div class="cards-grid">
                    <div class="card">
                        <h3>1️⃣ طلب العميل</h3>
                        <p><strong>المستندات المطلوبة للبيع بالأجل:</strong></p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>السجل التجاري</li>
                            <li>البطاقة الضريبية</li>
                            <li>شهادة القيمة المضافة</li>
                            <li>اشتراك الدفع الإلكتروني</li>
                            <li>بيان البنوك المتعامل معها</li>
                            <li>ميزانية آخر 3 سنوات</li>
                        </ul>
                        <p><strong>البيع النقدي:</strong> البطاقة الضريبية + القيمة المضافة + السجل التجاري</p>
                        <button class="btn" onclick="showCustomerRequestForm()">➕ طلب جديد</button>
                    </div>

                    <div class="card">
                        <h3>2️⃣ لجنة تنمية المبيعات</h3>
                        <p>عرض طلبات البيع بالأجل على لجنة تنمية المبيعات ثم مجلس الإدارة</p>
                        <p><strong>يشمل:</strong> استعلام عن العميل والتأكد من صحة البيانات</p>
                        <button class="btn" onclick="showSalesCommittee()">📋 اللجنة</button>
                    </div>

                    <div class="card">
                        <h3>3️⃣ قائمة الأسعار</h3>
                        <p>الارتباط مع العميل وفقاً لآخر قائمة أسعار معتمدة من مجلس الإدارة</p>
                        <button class="btn" onclick="showPriceList()">💰 الأسعار</button>
                    </div>

                    <div class="card">
                        <h3>4️⃣ أمر التوريد</h3>
                        <p>إصدار أمر توريد من العميل يشمل:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>الكمية المطلوبة</li>
                            <li>الأسعار المتفق عليها</li>
                            <li>كيفية السداد</li>
                            <li>المواصفات الفنية</li>
                            <li>مواعيد الاستلام</li>
                        </ul>
                        <button class="btn" onclick="showSupplyOrder()">📄 أمر توريد</button>
                    </div>
                </div>

                <!-- مراحل التنفيذ -->
                <h3>🔄 مراحل التنفيذ</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>5️⃣ إرسال المذكرات</h3>
                        <p>إرسال مذكرة + صورة أمر التوريد إلى:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>القطاع المالي</li>
                            <li>قطاع المراجعة</li>
                            <li>الشئون الفنية</li>
                        </ul>
                        <p><strong>الطرق:</strong> فاكس الشركة أو واتساب (للضرورة القصوى)</p>
                    </div>

                    <div class="card">
                        <h3>6️⃣ السداد</h3>
                        <p><strong>البيع النقدي:</strong> شيك حال أو دفع إلكتروني</p>
                        <p><strong>البيع بالأجل:</strong> شيكات بقيمة أمر التوريد حسب فترة الائتمان</p>
                        <button class="btn" onclick="showPaymentMethods()">💳 طرق السداد</button>
                    </div>

                    <div class="card">
                        <h3>7️⃣ مذكرة الشحن</h3>
                        <p>إرسال مذكرة شحن من إدارة المبيعات إلى المركز الرئيسي</p>
                        <button class="btn" onclick="showShippingNote()">🚚 الشحن</button>
                    </div>

                    <div class="card">
                        <h3>8️⃣ إدارة الحسابات</h3>
                        <p>تسليم مذكرة + أمر التوريد + الشيكات لإدارة الحسابات قبل الشحن</p>
                        <p><strong>يشمل:</strong> إعداد حافظة شيكات وقيود التسوية</p>
                    </div>
                </div>

                <!-- مراحل الشحن والتسليم -->
                <h3>🚛 مراحل الشحن والتسليم</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>9️⃣ طلب السيارات</h3>
                        <p>الإدارة المالية تطلب السيارات من مقاول النقل المتعاقد معه</p>
                    </div>

                    <div class="card">
                        <h3>🔟 دخول السيارة</h3>
                        <p>تسجيل بسجل البوابة + الوزن الفارغ (وزنة أولى)</p>
                        <p><strong>البيانات:</strong> السيارة + العميل + التاريخ</p>
                    </div>

                    <div class="card">
                        <h3>1️⃣1️⃣ التحميل والوزن</h3>
                        <p><strong>لجنة التحميل:</strong> أمين المخزن + الإنتاج + المعمل</p>
                        <p><strong>لجنة الوزن:</strong> أمين المخزن + الوزان + المراجع + المندوب المالي + الأمن</p>
                        <p><strong>المخرجات:</strong> علم الوزن + تصريح خروج</p>
                    </div>

                    <div class="card">
                        <h3>1️⃣2️⃣ علم التصدير</h3>
                        <p>تحرير علم تصدير يشمل:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>التاريخ واسم العميل</li>
                            <li>كمية الورق وعدد البكر</li>
                            <li>الجرام والمقاس</li>
                            <li>بيانات السيارة والسائق</li>
                            <li>توقيع السائق ومقاول النقل</li>
                        </ul>
                    </div>
                </div>

                <!-- المراحل النهائية -->
                <h3>📊 المراحل النهائية</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>1️⃣3️⃣ إذن الصرف</h3>
                        <p>إعداد إذن صرف منتجات بناء على علم التصدير وعلم الوزن</p>
                        <p><strong>التوقيع:</strong> أمين المخزن + اعتماد المدير المالي</p>
                    </div>

                    <div class="card">
                        <h3>1️⃣4️⃣ تصريح الخروج</h3>
                        <p>تصريح خروج مرقم ومسلسل موقع من:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>أمين المخزن</li>
                            <li>المندوب المالي</li>
                            <li>مندوب الأمن</li>
                            <li>المراجع</li>
                            <li>اعتماد رئيس القطاع المالي</li>
                        </ul>
                    </div>

                    <div class="card">
                        <h3>1️⃣5️⃣ بيان المبيعات</h3>
                        <p>إعداد بيان بكميات الورق المباعة مرفق به:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>أمر الشحن</li>
                            <li>أصل علم التصدير</li>
                            <li>أصل إذن الصرف</li>
                        </ul>
                        <p>تسليم لإدارة الحسابات لإعداد الفاتورة</p>
                    </div>

                    <div class="card">
                        <h3>1️⃣6️⃣ الفاتورة النهائية</h3>
                        <p>إصدار الفاتورة النهائية بعد مطابقة:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>علوم التصدير</li>
                            <li>أمر التوريد</li>
                            <li>مذكرات الشحن</li>
                        </ul>
                    </div>
                </div>

                <!-- المتابعة والرقابة -->
                <h3>📈 المتابعة والرقابة</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>1️⃣7️⃣ تسليم الفواتير</h3>
                        <p>إرسال أصل الفواتير للقطاع التجاري + إعداد إذن تسوية</p>
                        <p><strong>يشمل:</strong> خصم قيمة الورق على حساب العميل</p>
                    </div>

                    <div class="card">
                        <h3>1️⃣8️⃣ بيان حركة العملاء</h3>
                        <p>إعداد بيان شهري يعرض على لجنة تنمية المبيعات يشمل:</p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>رصيد أول المدة</li>
                            <li>قيمة الكميات المباعة</li>
                            <li>المسدد من كل عميل</li>
                        </ul>
                    </div>

                    <div class="card">
                        <h3>1️⃣9️⃣ جرد المخازن</h3>
                        <p>جرد شهري منتظم بمعرفة لجنة من قطاع المراجعة</p>
                        <p><strong>يشمل:</strong> مطابقة الرصيد الدفتري مع الفعلي وإظهار الفروق</p>
                        <button class="btn" onclick="showInventoryAudit()">📋 الجرد</button>
                    </div>
                </div>

                <!-- أدوات سريعة -->
                <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3>🛠️ أدوات سريعة</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn" onclick="createNewSalesOrder()">📋 طلب مبيعات جديد</button>
                        <button class="btn" onclick="trackSalesOrder()">🔍 تتبع طلب</button>
                        <button class="btn" onclick="generateSalesReport()">📊 تقرير المبيعات</button>
                        <button class="btn btn-secondary" onclick="exportSalesCycle()">📤 تصدير الدورة</button>
                    </div>
                </div>

                <!-- فوتر دورة المبيعات -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام الدورة المستندية للمبيعات</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- دورة المشتريات -->
            <div id="purchase_cycle" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">📦 الدورة المستندية للبضائع الواردة</h2>
                    <p style="font-size: 14px; opacity: 0.9;">نظام شامل لإدارة البضائع الواردة من التسجيل حتى الاستلام</p>
                </div>

                <div class="alert alert-info">
                    <strong>شركة مصر ادفو للب وورق الكتابة والطباعة</strong><br>
                    الدورة المستندية للمشتريات - نظام شامل لإدارة البضائع الواردة من التسجيل حتى الاستلام
                </div>

                <!-- مراحل استلام البضائع -->
                <div class="cards-grid">
                    <div class="card">
                        <h3>1️⃣ تسجيل البوابة</h3>
                        <p><strong>سجل البضائع الواردة يشمل:</strong></p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>تاريخ ورود البضاعة</li>
                            <li>رقم الفاتورة وتاريخها</li>
                            <li>اسم المورد</li>
                            <li>بيان البضاعة</li>
                            <li>بيان السيارة واسم السائق</li>
                        </ul>
                        <button class="btn" onclick="showGateRegistration()">🚪 تسجيل البوابة</button>
                    </div>

                    <div class="card">
                        <h3>2️⃣ ختم البوابة</h3>
                        <p>توقيع أمين البوابة على خلف أصل الفاتورة وختمها بختم البوابة</p>
                        <p><strong>الغرض:</strong> إثبات دخول البضاعة رسمياً</p>
                    </div>

                    <div class="card">
                        <h3>3️⃣ الوزن</h3>
                        <p><strong>للسيارات التي تحسب بالطن:</strong></p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>وزن السيارة بالحمولة (وزنة أولى)</li>
                            <li>وزن السيارة فارغة (وزنة ثانية)</li>
                            <li>تسجيل صافي الوزن بكارت الميزان</li>
                            <li>عمل محضر عجز إن وجد</li>
                        </ul>
                        <p><strong>قطع الغيار:</strong> دخول مباشر للمخازن للفحص</p>
                        <button class="btn" onclick="showWeighing()">⚖️ الوزن</button>
                    </div>

                    <div class="card">
                        <h3>4️⃣ فحص العينة</h3>
                        <p><strong>للمواد البترولية (مازوت - سولار):</strong></p>
                        <p><strong>لجنة الفحص:</strong> إدارة المراجعة + المعمل + أمين المخزن</p>
                        <p><strong>الهدف:</strong> إثبات المطابقة الفنية من عدمها</p>
                        <button class="btn" onclick="showSampleTest()">🧪 فحص العينة</button>
                    </div>
                </div>

                <!-- مراحل الاستلام والفحص -->
                <h3>📋 مراحل الاستلام والفحص</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>5️⃣ دخول المخازن</h3>
                        <p><strong>مراجعة المستندات:</strong></p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>الفواتير وإثباتها بالبوابة</li>
                            <li>كارت الميزان</li>
                            <li>التسجيل بسجل البضائع الواردة للمخازن</li>
                        </ul>
                        <p><strong>المطابقة:</strong> إدارة المراجعة تطابق سجل البوابة مع سجل المخازن</p>
                    </div>

                    <div class="card">
                        <h3>6️⃣ محضر الفحص</h3>
                        <p><strong>التوقيع على المحضر:</strong></p>
                        <ul style="text-align: right; margin: 10px 0;">
                            <li>أمين المخزن</li>
                            <li>مراقب الأمن بالبوابات</li>
                            <li>المندوب الفني (مطابقة البضاعة)</li>
                        </ul>
                        <p><strong>الاعتماد:</strong> رئيس القطاع المختص + رئيس القطاع المالي</p>
                        <button class="btn" onclick="showInspectionReport()">📋 محضر الفحص</button>
                    </div>

                    <div class="card">
                        <h3>7️⃣ علم الاستلام</h3>
                        <p>بعد اعتماد محضر الفحص، يحرر أمين المخزن علم استلام</p>
                        <p><strong>الإرسال إلى:</strong> حسابات المخازن بالإدارة المالية</p>
                        <p><strong>الغرض:</strong> التسعير حسب فاتورة المورد وإثبات الكمية والقيمة</p>
                        <button class="btn" onclick="showReceiptNote()">📄 علم الاستلام</button>
                    </div>

                    <div class="card">
                        <h3>💻 النظام الآلي</h3>
                        <p>إثبات البضائع بحسابات المخازن كمية وقيمة</p>
                        <p><strong>باستخدام:</strong> برنامج الحاسب الآلي المعد لذلك</p>
                        <p><strong>البيانات:</strong> من محضر الفحص + علم الاستلام + فاتورة المورد</p>
                    </div>
                </div>

                <!-- أدوات سريعة للمشتريات -->
                <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3>🛠️ أدوات سريعة</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn" onclick="registerNewDelivery()">📦 تسجيل وارد جديد</button>
                        <button class="btn" onclick="trackDelivery()">🔍 تتبع شحنة</button>
                        <button class="btn" onclick="generatePurchaseReport()">📊 تقرير الواردات</button>
                        <button class="btn btn-secondary" onclick="exportPurchaseCycle()">📤 تصدير الدورة</button>
                    </div>
                </div>

                <!-- مخطط تدفق العمليات -->
                <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                    <h3>🔄 مخطط تدفق العمليات</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 20px 0;">
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>1. البوابة</strong><br>
                            تسجيل + ختم
                        </div>
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>2. الميزان</strong><br>
                            وزن + كارت
                        </div>
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>3. الفحص</strong><br>
                            عينة + مطابقة
                        </div>
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>4. المخازن</strong><br>
                            استلام + تسجيل
                        </div>
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>5. المحضر</strong><br>
                            فحص + اعتماد
                        </div>
                        <div style="background: rgba(255,215,0,0.3); padding: 10px; border-radius: 8px; text-align: center;">
                            <strong>6. الحسابات</strong><br>
                            تسعير + إثبات
                        </div>
                    </div>
                </div>

                <!-- فوتر دورة المشتريات -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام الدورة المستندية للمشتريات</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- الحسابات المالية -->
            <div id="financial" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">💰 الحسابات المالية</h2>
                </div>

                <div class="alert alert-info">
                    <strong>شركة مصر ادفو للب وورق الكتابة والطباعة</strong><br>
                    الحسابات المالية - هذه بيانات تجريبية لعرض إمكانيات النظام
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الحساب</th>
                            <th>اسم الحساب</th>
                            <th>نوع الحساب</th>
                            <th>الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1000</td>
                            <td>الأصول</td>
                            <td>أصول</td>
                            <td>150,000</td>
                        </tr>
                        <tr>
                            <td>1100</td>
                            <td>الأصول المتداولة</td>
                            <td>أصول</td>
                            <td>75,000</td>
                        </tr>
                        <tr>
                            <td>2000</td>
                            <td>الخصوم</td>
                            <td>خصوم</td>
                            <td>50,000</td>
                        </tr>
                        <tr>
                            <td>3000</td>
                            <td>حقوق الملكية</td>
                            <td>حقوق ملكية</td>
                            <td>100,000</td>
                        </tr>
                    </tbody>
                </table>

                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 إنشاء إذن صرف</h3>
                        <div class="form-group">
                            <label>نوع الإذن:</label>
                            <select>
                                <option>إذن صرف نقدي</option>
                                <option>إذن صرف شيك</option>
                                <option>إذن صرف تحويل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>المبلغ:</label>
                            <input type="number" placeholder="أدخل المبلغ">
                        </div>
                        <div class="form-group">
                            <label>الوصف:</label>
                            <input type="text" placeholder="وصف الإذن">
                        </div>
                        <button class="btn">إنشاء الإذن</button>
                    </div>
                    <div class="card">
                        <h3>📊 ملخص الحسابات</h3>
                        <p><strong>إجمالي الأصول:</strong> 225,000</p>
                        <p><strong>إجمالي الخصوم:</strong> 50,000</p>
                        <p><strong>حقوق الملكية:</strong> 100,000</p>
                        <p><strong>صافي الأصول:</strong> 175,000</p>
                    </div>
                </div>

                <!-- فوتر الحسابات المالية -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام الحسابات المالية والتكاليف</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">🏪 إدارة المخازن</h2>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>الوحدة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ITM001</td>
                            <td>مواد خام أساسية</td>
                            <td>مواد خام</td>
                            <td>500</td>
                            <td>كيلو</td>
                            <td>طبيعي</td>
                        </tr>
                        <tr>
                            <td>ITM002</td>
                            <td>وقود ديزل</td>
                            <td>وقود</td>
                            <td>1000</td>
                            <td>لتر</td>
                            <td>طبيعي</td>
                        </tr>
                        <tr>
                            <td>ITM003</td>
                            <td>قطع غيار</td>
                            <td>قطع غيار</td>
                            <td>50</td>
                            <td>قطعة</td>
                            <td>نقص مخزون</td>
                        </tr>
                    </tbody>
                </table>

                <!-- فوتر المخازن -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام إدارة المخازن والمخزون</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- المشتريات -->
            <div id="purchasing" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">🛒 إدارة المشتريات</h2>
                </div>

                <h3>🏢 قائمة الموردين</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم المورد</th>
                            <th>اسم المورد</th>
                            <th>رقم الاتصال</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>SUP001</td>
                            <td>شركة المواد الخام</td>
                            <td>0*********0</td>
                            <td>نشط</td>
                        </tr>
                        <tr>
                            <td>SUP002</td>
                            <td>مؤسسة الوقود</td>
                            <td>01987654321</td>
                            <td>نشط</td>
                        </tr>
                    </tbody>
                </table>

                <!-- فوتر المشتريات -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام إدارة المشتريات والموردين</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">📈 التقارير والتحليلات</h2>
                </div>

                <h3>📊 القوائم المالية الأساسية</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p>إجمالي المدين: 2,250,000 جنيه</p>
                        <p>إجمالي الدائن: 2,250,000 جنيه</p>
                        <button class="btn" onclick="showTrialBalance()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportTrialBalance()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📋 قائمة الدخل</h3>
                        <p>إيرادات المبيعات: 5,500,000 جنيه</p>
                        <p>صافي الربح: 850,000 جنيه</p>
                        <button class="btn" onclick="showIncomeStatement()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportIncomeStatement()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📊 قائمة المركز المالي</h3>
                        <p>إجمالي الأصول: 8,750,000 جنيه</p>
                        <p>حقوق الملكية: 6,200,000 جنيه</p>
                        <button class="btn" onclick="showBalanceSheet()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportBalanceSheet()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>💰 قائمة التدفقات النقدية</h3>
                        <p>التدفق من العمليات: 1,200,000 جنيه</p>
                        <p>صافي التدفق النقدي: 950,000 جنيه</p>
                        <button class="btn" onclick="showCashFlow()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportCashFlow()">تصدير PDF</button>
                    </div>
                </div>

                <h3>📈 التقارير التحليلية</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📊 تقرير المخازن</h3>
                        <p>إجمالي الأصناف: 150 صنف</p>
                        <p>قيمة المخزون: 2,775,000 جنيه</p>
                        <button class="btn" onclick="showInventoryReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportInventoryReport()">تصدير Excel</button>
                    </div>
                    <div class="card">
                        <h3>🏢 تقرير العملاء</h3>
                        <p>عدد العملاء: 85 عميل</p>
                        <p>أرصدة العملاء: 1,450,000 جنيه</p>
                        <button class="btn" onclick="showCustomersReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportCustomersReport()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>🏭 تقرير الموردين</h3>
                        <p>عدد الموردين: 45 مورد</p>
                        <p>أرصدة الموردين: 980,000 جنيه</p>
                        <button class="btn" onclick="showSuppliersReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportSuppliersReport()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>💳 تقرير الخزينة</h3>
                        <p>الرصيد النقدي: 650,000 جنيه</p>
                        <p>الشيكات تحت التحصيل: 320,000 جنيه</p>
                        <button class="btn" onclick="showTreasuryReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportTreasuryReport()">تصدير PDF</button>
                    </div>
                </div>

                <h3>📋 تقارير الدورة المستندية</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📄 تقرير المبيعات</h3>
                        <p>مبيعات الشهر: 2,500,000 جنيه</p>
                        <p>عدد الفواتير: 125 فاتورة</p>
                        <button class="btn" onclick="showSalesReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportSalesReport()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>📦 تقرير المشتريات</h3>
                        <p>مشتريات الشهر: 1,800,000 جنيه</p>
                        <p>عدد الفواتير: 75 فاتورة</p>
                        <button class="btn" onclick="showPurchasesReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportPurchasesReport()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>🔄 تقرير حركة المخازن</h3>
                        <p>إجمالي الحركات: 450 حركة</p>
                        <p>قيمة الحركات: 3,200,000 جنيه</p>
                        <button class="btn" onclick="showInventoryMovement()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportInventoryMovement()">تصدير Excel</button>
                    </div>
                    <div class="card">
                        <h3>📊 تقرير الإنتاج</h3>
                        <p>إنتاج الشهر: 850 طن</p>
                        <p>قيمة الإنتاج: 2,125,000 جنيه</p>
                        <button class="btn" onclick="showProductionReport()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportProductionReport()">تصدير PDF</button>
                    </div>
                </div>

                <h3>🎯 تقارير الأداء والتحليل</h3>
                <div class="cards-grid">
                    <div class="card">
                        <h3>📈 تحليل الربحية</h3>
                        <p>هامش الربح الإجمالي: 35%</p>
                        <p>هامش الربح الصافي: 15.5%</p>
                        <button class="btn" onclick="showProfitabilityAnalysis()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportProfitabilityAnalysis()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>⚡ تحليل السيولة</h3>
                        <p>نسبة السيولة السريعة: 1.8</p>
                        <p>نسبة السيولة الجارية: 2.3</p>
                        <button class="btn" onclick="showLiquidityAnalysis()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportLiquidityAnalysis()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>🔄 تحليل النشاط</h3>
                        <p>معدل دوران المخزون: 6.5 مرة</p>
                        <p>معدل دوران العملاء: 45 يوم</p>
                        <button class="btn" onclick="showActivityAnalysis()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportActivityAnalysis()">تصدير PDF</button>
                    </div>
                    <div class="card">
                        <h3>💪 تحليل الرافعة المالية</h3>
                        <p>نسبة الدين إلى حقوق الملكية: 0.41</p>
                        <p>نسبة تغطية الفوائد: 12.5 مرة</p>
                        <button class="btn" onclick="showLeverageAnalysis()">عرض التفاصيل</button>
                        <button class="btn btn-secondary" onclick="exportLeverageAnalysis()">تصدير PDF</button>
                    </div>
                </div>

                <!-- فوتر التقارير -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام التقارير والتحليلات المالية</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>

            <!-- الإعدادات -->
            <div id="settings" class="section">
                <div style="text-align: center; background: rgba(255,215,0,0.2); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="color: #ffd700; margin-bottom: 5px;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                    <h2 style="margin-bottom: 10px;">⚙️ إعدادات النظام</h2>
                </div>

                <div class="alert alert-success">
                    <strong>شركة مصر ادفو للب وورق الكتابة والطباعة</strong><br>
                    نظام المحاسبة والمراجعة الداخلية<br>
                    الإصدار: 1.0.0<br>
                    المطور: Ashraf<br>
                    النوع: واجهة مستقلة<br>
                    التاريخ: 2024
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <h3>👤 بيانات المستخدم</h3>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>الاسم الكامل:</strong> مدير النظام</p>
                        <p><strong>القسم:</strong> الإدارة</p>
                        <p><strong>الدور:</strong> مدير</p>
                    </div>
                    <div class="card">
                        <h3>🔧 إعدادات النظام</h3>
                        <button class="btn">تغيير كلمة المرور</button>
                        <button class="btn btn-secondary">نسخ احتياطي</button>
                        <button class="btn btn-secondary">استيراد بيانات</button>
                    </div>
                </div>

                <!-- فوتر الإعدادات -->
                <div class="footer">
                    <div class="logo-container">
                        <div class="company-logo">AG</div>
                        <div>
                            <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                            <span style="font-size: 14px;">AG Technology Systems</span><br>
                            <span style="font-size: 12px; opacity: 0.8;">نظام إعدادات وإدارة النظام</span>
                        </div>
                    </div>
                    <div class="copyright">
                        © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز | تصميم وتطوير الأنظمة المحاسبية
                    </div>
                </div>
            </div>
        </div>

        <!-- فوتر عام للنظام -->
        <div class="footer" style="margin: 30px auto; max-width: 1400px;">
            <div class="logo-container">
                <div class="company-logo" style="width: 120px; height: 120px; font-size: 36px; border: 4px solid rgba(255,215,0,0.7);">
                    <span class="company-logo-text">AG</span>
                </div>
                <div style="text-align: center;">
                    <h2 style="color: #ffd700; margin: 0;">🏢 شركة ايه جي تكنولوجي سيستميز</h2>
                    <h3 style="margin: 5px 0; font-size: 18px;">AG Technology Systems</h3>
                    <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                    <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">نظام المحاسبة والمراجعة الداخلية - شركة مصر ادفو للب وورق الكتابة والطباعة</p>
                </div>
            </div>
            <div class="copyright" style="font-size: 14px; margin-top: 15px;">
                © 2024 جميع الحقوق محفوظة لشركة ايه جي تكنولوجي سيستميز<br>
                AG Technology Systems - تصميم وتطوير الأنظمة المحاسبية والإدارية<br>
                الإصدار 1.0.0 | تاريخ الإصدار: 2024
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));
            
            // إزالة التفعيل من جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // إظهار القسم المطلوب
            document.getElementById(sectionId).classList.add('active');
            
            // تفعيل التبويب المطلوب
            event.target.classList.add('active');
        }

        // بيانات المستخدمين (محاكاة قاعدة البيانات)
        let users = [
            {
                id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                phone: '0*********0',
                department: 'admin',
                role: 'admin',
                status: 'active',
                createdAt: '2024-01-01'
            },
            {
                id: 2,
                username: 'accountant',
                fullName: 'محمد أحمد',
                email: '<EMAIL>',
                phone: '0*********1',
                department: 'financial',
                role: 'user',
                status: 'active',
                createdAt: '2024-01-15'
            },
            {
                id: 3,
                username: 'warehouse_manager',
                fullName: 'علي محمود',
                email: '<EMAIL>',
                phone: '0*********2',
                department: 'inventory',
                role: 'manager',
                status: 'active',
                createdAt: '2024-02-01'
            },
            {
                id: 4,
                username: 'purchasing_user',
                fullName: 'فاطمة سالم',
                email: '<EMAIL>',
                phone: '0*********3',
                department: 'purchasing',
                role: 'user',
                status: 'inactive',
                createdAt: '2024-02-10'
            }
        ];

        // إظهار نموذج إضافة مستخدم
        function showAddUserForm() {
            document.getElementById('addUserForm').style.display = 'block';
        }

        // إخفاء نموذج إضافة مستخدم
        function hideAddUserForm() {
            document.getElementById('addUserForm').style.display = 'none';
            clearUserForm();
        }

        // مسح نموذج المستخدم
        function clearUserForm() {
            document.getElementById('newUsername').value = '';
            document.getElementById('newFullName').value = '';
            document.getElementById('newEmail').value = '';
            document.getElementById('newPhone').value = '';
            document.getElementById('newDepartment').value = '';
            document.getElementById('newRole').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }

        // إضافة مستخدم جديد
        function addNewUser() {
            const username = document.getElementById('newUsername').value;
            const fullName = document.getElementById('newFullName').value;
            const email = document.getElementById('newEmail').value;
            const phone = document.getElementById('newPhone').value;
            const department = document.getElementById('newDepartment').value;
            const role = document.getElementById('newRole').value;
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // التحقق من البيانات
            if (!username || !fullName || !email || !department || !role || !password) {
                alert('❌ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (password !== confirmPassword) {
                alert('❌ كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            if (users.find(user => user.username === username)) {
                alert('❌ اسم المستخدم موجود بالفعل');
                return;
            }

            // إضافة المستخدم الجديد
            const newUser = {
                id: users.length + 1,
                username: username,
                fullName: fullName,
                email: email,
                phone: phone,
                department: department,
                role: role,
                status: 'active',
                createdAt: new Date().toISOString().split('T')[0]
            };

            users.push(newUser);
            updateUsersTable();
            updateUserStats();
            hideAddUserForm();

            alert('✅ تم إضافة المستخدم بنجاح!');
        }

        // تحديث جدول المستخدمين
        function updateUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');

                const departmentNames = {
                    'admin': 'الإدارة',
                    'financial': 'الحسابات المالية',
                    'inventory': 'المخازن',
                    'purchasing': 'المشتريات',
                    'treasury': 'الخزينة',
                    'paper_warehouse': 'مخازن الورق',
                    'audit': 'المراجعة الداخلية'
                };

                const roleNames = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير القسم',
                    'supervisor': 'مشرف',
                    'user': 'مستخدم عادي'
                };

                const statusColor = user.status === 'active' ? '#4CAF50' : '#FF9800';
                const statusText = user.status === 'active' ? 'نشط' : 'معطل';
                const actionButton = user.status === 'active' ?
                    `<button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(${user.id})">🔒 تعطيل</button>` :
                    `<button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="toggleUserStatus(${user.id})">🔓 تفعيل</button>`;

                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${departmentNames[user.department] || user.department}</td>
                    <td>${roleNames[user.role] || user.role}</td>
                    <td><span style="color: ${statusColor};">${statusText}</span></td>
                    <td>${user.createdAt}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="editUser(${user.id})">✏️ تعديل</button>
                        ${actionButton}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث إحصائيات المستخدمين
        function updateUserStats() {
            const totalUsers = users.length;
            const activeUsers = users.filter(user => user.status === 'active').length;
            const adminUsers = users.filter(user => user.role === 'admin').length;
            const departments = [...new Set(users.map(user => user.department))].length;

            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('adminUsers').textContent = adminUsers;
            document.getElementById('departmentCount').textContent = departments;

            // تحديث إحصائيات لوحة التحكم
            document.getElementById('dashboardUsers').textContent = totalUsers;
            document.getElementById('dashboardActiveUsers').textContent = activeUsers;
        }

        // تبديل حالة المستخدم
        function toggleUserStatus(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                user.status = user.status === 'active' ? 'inactive' : 'active';
                updateUsersTable();
                updateUserStats();

                const statusText = user.status === 'active' ? 'تم تفعيل' : 'تم تعطيل';
                alert(`✅ ${statusText} المستخدم ${user.fullName} بنجاح`);
            }
        }

        // تعديل مستخدم
        function editUser(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                const newFullName = prompt('أدخل الاسم الكامل الجديد:', user.fullName);
                if (newFullName && newFullName !== user.fullName) {
                    user.fullName = newFullName;
                    updateUsersTable();
                    alert('✅ تم تحديث بيانات المستخدم بنجاح');
                }
            }
        }

        // تصدير قائمة المستخدمين
        function exportUsers() {
            let csvContent = "الرقم,اسم المستخدم,الاسم الكامل,البريد الإلكتروني,القسم,الدور,الحالة,تاريخ الإنشاء\n";

            users.forEach(user => {
                const departmentNames = {
                    'admin': 'الإدارة',
                    'financial': 'الحسابات المالية',
                    'inventory': 'المخازن',
                    'purchasing': 'المشتريات',
                    'treasury': 'الخزينة',
                    'paper_warehouse': 'مخازن الورق',
                    'audit': 'المراجعة الداخلية'
                };

                const roleNames = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير القسم',
                    'supervisor': 'مشرف',
                    'user': 'مستخدم عادي'
                };

                csvContent += `${user.id},${user.username},${user.fullName},${user.email},${departmentNames[user.department]},${roleNames[user.role]},${user.status === 'active' ? 'نشط' : 'معطل'},${user.createdAt}\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'users_list.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير قائمة المستخدمين بنجاح!');
        }

        // دوال الدورة المستندية للمبيعات
        function showCustomerRequestForm() {
            alert('📋 نموذج طلب العميل\n\nسيتم فتح نموذج لإدخال:\n- بيانات العميل\n- نوع البيع (نقدي/أجل)\n- المستندات المطلوبة\n- الكميات المطلوبة');
        }

        function showSalesCommittee() {
            alert('📋 لجنة تنمية المبيعات\n\nعرض الطلبات المعلقة:\n- طلبات البيع بالأجل\n- استعلامات العملاء\n- التوصيات للمجلس');
        }

        function showPriceList() {
            alert('💰 قائمة الأسعار\n\nآخر قائمة أسعار معتمدة:\n- أسعار الورق بالجرام\n- أسعار المقاسات المختلفة\n- خصومات الكميات');
        }

        function showSupplyOrder() {
            alert('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n📄 أمر التوريد\n\nإنشاء أمر توريد جديد:\n- بيانات العميل\n- الكميات والمواصفات\n- الأسعار وطريقة السداد\n- مواعيد التسليم');
        }

        function showPaymentMethods() {
            alert('💳 طرق السداد\n\nالطرق المتاحة:\n- نقدي: شيك حال\n- إلكتروني: إشعار بنك\n- أجل: شيكات مؤجلة');
        }

        function showShippingNote() {
            alert('🚚 مذكرة الشحن\n\nإعداد مذكرة شحن تشمل:\n- بيانات العميل\n- الكميات المطلوبة\n- موعد الشحن\n- بيانات السيارة');
        }

        function showInventoryAudit() {
            alert('📋 جرد المخازن\n\nجرد شهري منتظم:\n- مطابقة الرصيد الدفتري\n- الرصيد الفعلي\n- إظهار الفروق\n- تقرير الجرد');
        }

        function createNewSalesOrder() {
            // إنشاء فاتورة مبيعات تحمل اسم الشركتين
            const invoiceData = {
                customer: 'شركة الأهرام للطباعة والنشر',
                product: 'ورق كتابة 80 جرام - مقاس A4',
                quantity: '100',
                price: '25,000',
                total: '2,500,000',
                subtotal: '2,500,000',
                tax: '350,000',
                grandTotal: '2,850,000',
                paymentMethod: 'شيكات مؤجلة'
            };

            if (confirm('هل تريد طباعة فاتورة المبيعات؟')) {
                printSalesInvoice(invoiceData);
            } else {
                const invoiceContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📄 فاتورة مبيعات

رقم الفاتورة: INV-2024-001
التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

بيانات العميل:
الاسم: شركة الأهرام للطباعة والنشر
العنوان: القاهرة - مصر الجديدة
الرقم الضريبي: *********

═══════════════════════════════════════

تفاصيل الفاتورة:
• ورق كتابة 80 جرام - مقاس A4
  الكمية: 100 طن
  السعر: 25,000 جنيه/طن
  الإجمالي: 2,500,000 جنيه

• ضريبة القيمة المضافة (14%): 350,000 جنيه
• الإجمالي شامل الضريبة: 2,850,000 جنيه

═══════════════════════════════════════

شروط السداد: 30 يوم من تاريخ الفاتورة
طريقة السداد: شيكات مؤجلة

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
نظام المحاسبة والمراجعة الداخلية

تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
AG Technology Systems
© 2024 جميع الحقوق محفوظة
                `;

                alert(invoiceContent);
            }
        }

        function trackSalesOrder() {
            const orderNumber = prompt('أدخل رقم الطلب لتتبعه:');
            if (orderNumber) {
                alert(`🔍 تتبع الطلب رقم: ${orderNumber}\n\nالحالة الحالية: قيد التنفيذ\nالمرحلة: الشحن\nالموقع: المخزن الرئيسي`);
            }
        }

        function generateSalesReport() {
            // إنشاء تقرير مبيعات يحمل اسم الشركة والمطور
            const reportContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📊 تقرير المبيعات الشهري

التاريخ: ${new Date().toLocaleDateString('ar-EG')}
الفترة: ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}

═══════════════════════════════════════

📈 ملخص المبيعات:
• إجمالي المبيعات: 2,500,000 جنيه
• عدد الفواتير: 45 فاتورة
• متوسط قيمة الفاتورة: 55,556 جنيه

📋 المبيعات بالعميل:
• شركة الأهرام للطباعة: 850,000 جنيه
• مؤسسة النيل للنشر: 650,000 جنيه
• شركة المستقبل للورق: 500,000 جنيه
• عملاء آخرون: 500,000 جنيه

📦 المبيعات بالمنتج:
• ورق كتابة 80 جرام: 1,200,000 جنيه
• ورق طباعة 70 جرام: 800,000 جنيه
• ورق صحف 45 جرام: 500,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
نظام المحاسبة والمراجعة الداخلية

تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
AG Technology Systems
© 2024 جميع الحقوق محفوظة
            `;

            alert(reportContent);
        }

        function exportSalesCycle() {
            alert('📤 تصدير دورة المبيعات\n\nسيتم تصدير:\n- مخطط الدورة المستندية\n- النماذج والمستندات\n- التعليمات التفصيلية');
        }

        // دوال الدورة المستندية للمشتريات
        function showGateRegistration() {
            alert('🚪 تسجيل البوابة\n\nسجل البضائع الواردة:\n- تاريخ الوصول\n- بيانات المورد\n- بيانات السيارة\n- وصف البضاعة');
        }

        function showWeighing() {
            alert('⚖️ عملية الوزن\n\nمراحل الوزن:\n- وزن السيارة محملة\n- وزن السيارة فارغة\n- حساب صافي الوزن\n- إصدار كارت الميزان');
        }

        function showSampleTest() {
            alert('🧪 فحص العينة\n\nفحص المواد البترولية:\n- أخذ عينة للتحليل\n- فحص المطابقة الفنية\n- إصدار تقرير الفحص\n- اعتماد النتائج');
        }

        function showInspectionReport() {
            alert('📋 محضر الفحص\n\nإعداد محضر فحص شامل:\n- فحص الكمية\n- فحص الجودة\n- المطابقة مع الفاتورة\n- توقيعات اللجنة');
        }

        function showReceiptNote() {
            alert('📄 علم الاستلام\n\nإصدار علم استلام نهائي:\n- تأكيد الاستلام\n- الكميات المستلمة\n- حالة البضاعة\n- إرسال للحسابات');
        }

        function registerNewDelivery() {
            alert('📦 تسجيل وارد جديد\n\nسيتم فتح نموذج لتسجيل:\n- بيانات المورد\n- تفاصيل الشحنة\n- المستندات المرفقة\n- موعد الوصول');
        }

        function trackDelivery() {
            const deliveryNumber = prompt('أدخل رقم الشحنة لتتبعها:');
            if (deliveryNumber) {
                alert(`🔍 تتبع الشحنة رقم: ${deliveryNumber}\n\nالحالة: وصلت للبوابة\nالمرحلة: الوزن\nالموقع: الميزان`);
            }
        }

        function generatePurchaseReport() {
            // إنشاء تقرير واردات يحمل اسم الشركة
            const reportContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📦 تقرير الواردات الشهري

التاريخ: ${new Date().toLocaleDateString('ar-EG')}
الفترة: ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}

═══════════════════════════════════════

📈 ملخص الواردات:
• إجمالي الواردات: 1,800,000 جنيه
• عدد الشحنات: 25 شحنة
• متوسط قيمة الشحنة: 72,000 جنيه

🏢 الواردات بالمورد:
• شركة المواد الخام المصرية: 650,000 جنيه
• مؤسسة الكيماويات الصناعية: 450,000 جنيه
• شركة الوقود والطاقة: 400,000 جنيه
• موردون آخرون: 300,000 جنيه

📋 الواردات بالنوع:
• مواد خام (لب الورق): 800,000 جنيه
• كيماويات ومواد مساعدة: 500,000 جنيه
• وقود ومواد بترولية: 300,000 جنيه
• قطع غيار ومستلزمات: 200,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
نظام المحاسبة والمراجعة الداخلية
            `;

            alert(reportContent);
        }

        function exportPurchaseCycle() {
            alert('📤 تصدير دورة المشتريات\n\nسيتم تصدير:\n- مخطط دورة الواردات\n- النماذج والمحاضر\n- التعليمات الإدارية');
        }

        // دوال القوائم المالية الأساسية
        function showTrialBalance() {
            const trialBalanceContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
⚖️ ميزان المراجعة

التاريخ: ${new Date().toLocaleDateString('ar-EG')}
الفترة: ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}

═══════════════════════════════════════

رقم الحساب | اسم الحساب                    | مدين        | دائن
─────────────────────────────────────────────────────────
1000       | الأصول الثابتة               | 3,500,000   |
1100       | الأصول المتداولة             | 2,750,000   |
1110       | النقدية والبنوك              | 650,000     |
1120       | العملاء                      | 1,450,000   |
1130       | المخزون                      | 650,000     |
2000       | الخصوم المتداولة             |             | 1,200,000
2100       | الموردون                     |             | 980,000
2200       | مصروفات مستحقة               |             | 220,000
3000       | رأس المال                    |             | 4,000,000
3100       | الأرباح المحتجزة             |             | 1,200,000
4000       | إيرادات المبيعات             |             | 5,500,000
5000       | تكلفة البضاعة المباعة        | 3,850,000   |
6000       | مصروفات التشغيل              | 1,200,000   |
7000       | مصروفات إدارية               | 350,000     |

─────────────────────────────────────────────────────────
الإجمالي                              | 12,750,000  | 12,750,000

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(trialBalanceContent);
        }

        function showIncomeStatement() {
            const incomeStatementContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📋 قائمة الدخل

التاريخ: ${new Date().toLocaleDateString('ar-EG')}
الفترة: ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}

═══════════════════════════════════════

الإيرادات:
• إيرادات المبيعات                    5,500,000 جنيه
• إيرادات أخرى                        100,000 جنيه
─────────────────────────────────────
إجمالي الإيرادات                      5,600,000 جنيه

التكاليف والمصروفات:
• تكلفة البضاعة المباعة               3,850,000 جنيه
─────────────────────────────────────
مجمل الربح                           1,750,000 جنيه

المصروفات التشغيلية:
• مصروفات البيع والتوزيع              450,000 جنيه
• مصروفات إدارية وعمومية             350,000 جنيه
• مصروفات أخرى                       100,000 جنيه
─────────────────────────────────────
إجمالي المصروفات التشغيلية           900,000 جنيه

صافي الربح قبل الضرائب               850,000 جنيه
• ضرائب الدخل (22.5%)                191,250 جنيه
─────────────────────────────────────
صافي الربح بعد الضرائب               658,750 جنيه

═══════════════════════════════════════
هامش الربح الإجمالي: 31.25%
هامش الربح الصافي: 11.76%

شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(incomeStatementContent);
        }

        function showBalanceSheet() {
            const balanceSheetContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
📊 قائمة المركز المالي (الميزانية العمومية)

التاريخ: ${new Date().toLocaleDateString('ar-EG')}

═══════════════════════════════════════

الأصول:

الأصول غير المتداولة:
• الأراضي والمباني                   2,500,000 جنيه
• الآلات والمعدات                    2,800,000 جنيه
• وسائل النقل                        450,000 جنيه
• أصول أخرى                          250,000 جنيه
─────────────────────────────────────
إجمالي الأصول غير المتداولة          6,000,000 جنيه

الأصول المتداولة:
• النقدية والبنوك                    650,000 جنيه
• العملاء                           1,450,000 جنيه
• المخزون                           2,775,000 جنيه
• مصروفات مقدمة                      125,000 جنيه
─────────────────────────────────────
إجمالي الأصول المتداولة             3,000,000 جنيه

إجمالي الأصول                       9,000,000 جنيه

═══════════════════════════════════════

الخصوم وحقوق الملكية:

الخصوم المتداولة:
• الموردون                          980,000 جنيه
• مصروفات مستحقة                    220,000 جنيه
• ضرائب مستحقة                      150,000 جنيه
• قروض قصيرة الأجل                  450,000 جنيه
─────────────────────────────────────
إجمالي الخصوم المتداولة             1,800,000 جنيه

الخصوم طويلة الأجل:
• قروض طويلة الأجل                  1,000,000 جنيه
─────────────────────────────────────
إجمالي الخصوم                       2,800,000 جنيه

حقوق الملكية:
• رأس المال                         4,000,000 جنيه
• الأرباح المحتجزة                  1,541,250 جنيه
• أرباح العام الجاري                658,750 جنيه
─────────────────────────────────────
إجمالي حقوق الملكية                 6,200,000 جنيه

إجمالي الخصوم وحقوق الملكية         9,000,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(balanceSheetContent);
        }

        function showCashFlow() {
            const cashFlowContent = `
🏭 شركة مصر ادفو للب وورق الكتابة والطباعة
💰 قائمة التدفقات النقدية

التاريخ: ${new Date().toLocaleDateString('ar-EG')}
الفترة: ${new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}

═══════════════════════════════════════

التدفقات النقدية من الأنشطة التشغيلية:
• صافي الربح                        658,750 جنيه
• الاستهلاك                         450,000 جنيه
• التغير في العملاء                 (150,000) جنيه
• التغير في المخزون                 (200,000) جنيه
• التغير في الموردين                120,000 جنيه
• التغير في المصروفات المستحقة       80,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التشغيلية    958,750 جنيه

التدفقات النقدية من الأنشطة الاستثمارية:
• شراء أصول ثابتة                   (350,000) جنيه
• بيع أصول ثابتة                    50,000 جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة الاستثمارية  (300,000) جنيه

التدفقات النقدية من الأنشطة التمويلية:
• قروض جديدة                        200,000 جنيه
• سداد قروض                         (150,000) جنيه
• توزيعات أرباح                     (250,000) جنيه
─────────────────────────────────────
صافي التدفق من الأنشطة التمويلية    (200,000) جنيه

صافي التغير في النقدية              458,750 جنيه
رصيد النقدية أول الفترة             191,250 جنيه
─────────────────────────────────────
رصيد النقدية آخر الفترة             650,000 جنيه

═══════════════════════════════════════
شركة مصر ادفو للب وورق الكتابة والطباعة
تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;
            alert(cashFlowContent);
        }

        // دوال التقارير التحليلية
        function showInventoryReport() {
            alert('📊 تقرير المخازن التفصيلي\n\nسيتم عرض:\n• أرصدة جميع الأصناف\n• حركة المخزون\n• الأصناف بطيئة الحركة\n• تقييم المخزون');
        }

        function showCustomersReport() {
            alert('🏢 تقرير العملاء\n\nسيتم عرض:\n• أرصدة العملاء\n• أعمار الديون\n• حدود الائتمان\n• تحليل المبيعات بالعميل');
        }

        function showSuppliersReport() {
            alert('🏭 تقرير الموردين\n\nسيتم عرض:\n• أرصدة الموردين\n• تحليل المشتريات\n• تقييم الأداء\n• شروط السداد');
        }

        function showTreasuryReport() {
            alert('💳 تقرير الخزينة\n\nسيتم عرض:\n• الأرصدة النقدية\n• الشيكات تحت التحصيل\n• حركة البنوك\n• التدفقات النقدية');
        }

        function showSalesReport() {
            alert('📄 تقرير المبيعات التفصيلي\n\nسيتم عرض:\n• مبيعات الفترة\n• تحليل بالعميل\n• تحليل بالمنتج\n• هوامش الربح');
        }

        function showPurchasesReport() {
            alert('📦 تقرير المشتريات التفصيلي\n\nسيتم عرض:\n• مشتريات الفترة\n• تحليل بالمورد\n• تحليل بالصنف\n• تكاليف الشراء');
        }

        function showInventoryMovement() {
            alert('🔄 تقرير حركة المخازن\n\nسيتم عرض:\n• جميع حركات الصرف والإضافة\n• تحليل الحركة بالصنف\n• معدلات الدوران\n• الكميات المتحركة');
        }

        function showProductionReport() {
            alert('📊 تقرير الإنتاج\n\nسيتم عرض:\n• كميات الإنتاج\n• تكاليف الإنتاج\n• معدلات الإنتاجية\n• تحليل الكفاءة');
        }

        // دوال التحليل المالي
        function showProfitabilityAnalysis() {
            alert('📈 تحليل الربحية\n\nسيتم عرض:\n• هوامش الربح\n• العائد على الأصول\n• العائد على حقوق الملكية\n• تحليل الاتجاهات');
        }

        function showLiquidityAnalysis() {
            alert('⚡ تحليل السيولة\n\nسيتم عرض:\n• نسب السيولة\n• رأس المال العامل\n• دورة التشغيل\n• تحليل التدفقات');
        }

        function showActivityAnalysis() {
            alert('🔄 تحليل النشاط\n\nسيتم عرض:\n• معدلات الدوران\n• كفاءة استخدام الأصول\n• دورات التحصيل والسداد\n• مؤشرات الأداء');
        }

        function showLeverageAnalysis() {
            alert('💪 تحليل الرافعة المالية\n\nسيتم عرض:\n• نسب المديونية\n• تغطية الفوائد\n• الرافعة التشغيلية\n• المخاطر المالية');
        }

        // دوال التصدير الحقيقية
        function exportTrialBalance() {
            const trialBalanceData = `
شركة مصر ادفو للب وورق الكتابة والطباعة
ميزان المراجعة
التاريخ: ${new Date().toLocaleDateString('ar-EG')}

رقم الحساب,اسم الحساب,مدين,دائن
1000,الأصول الثابتة,3500000,
1100,الأصول المتداولة,2750000,
1110,النقدية والبنوك,650000,
1120,العملاء,1450000,
1130,المخزون,650000,
2000,الخصوم المتداولة,,1200000
2100,الموردون,,980000
2200,مصروفات مستحقة,,220000
3000,رأس المال,,4000000
3100,الأرباح المحتجزة,,1200000
4000,إيرادات المبيعات,,5500000
5000,تكلفة البضاعة المباعة,3850000,
6000,مصروفات التشغيل,1200000,
7000,مصروفات إدارية,350000,
الإجمالي,12750000,12750000

تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;

            downloadFile(trialBalanceData, 'ميزان_المراجعة.csv', 'text/csv;charset=utf-8;');
            alert('✅ تم تصدير ميزان المراجعة بنجاح!');
        }

        // دالة التحميل العامة
        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        function exportIncomeStatement() {
            const incomeStatementData = `
شركة مصر ادفو للب وورق الكتابة والطباعة
قائمة الدخل
التاريخ: ${new Date().toLocaleDateString('ar-EG')}

البيان,المبلغ (جنيه)
الإيرادات,
إيرادات المبيعات,5500000
إيرادات أخرى,100000
إجمالي الإيرادات,5600000
,
التكاليف والمصروفات,
تكلفة البضاعة المباعة,3850000
مجمل الربح,1750000
,
المصروفات التشغيلية,
مصروفات البيع والتوزيع,450000
مصروفات إدارية وعمومية,350000
مصروفات أخرى,100000
إجمالي المصروفات التشغيلية,900000
,
صافي الربح قبل الضرائب,850000
ضرائب الدخل (22.5%),191250
صافي الربح بعد الضرائب,658750
,
النسب المالية,
هامش الربح الإجمالي,31.25%
هامش الربح الصافي,11.76%

تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز
© 2024 جميع الحقوق محفوظة
            `;

            downloadFile(incomeStatementData, 'قائمة_الدخل.csv', 'text/csv;charset=utf-8;');
            alert('✅ تم تصدير قائمة الدخل بنجاح!');
        }

        function exportBalanceSheet() {
            alert('📤 تصدير قائمة المركز المالي\n\nسيتم تصدير التقرير بصيغة PDF مع:\n• تنسيق معياري\n• نسب مالية\n• تحليل الاتجاهات');
        }

        function exportCashFlow() {
            alert('📤 تصدير قائمة التدفقات النقدية\n\nسيتم تصدير التقرير بصيغة PDF مع:\n• تحليل مصادر واستخدامات الأموال\n• مؤشرات السيولة\n• توقعات مستقبلية');
        }

        // بيانات المستخدمين للدخول
        const loginUsers = {
            'admin': {
                password: 'admin123',
                name: 'مدير النظام',
                role: 'admin',
                permissions: ['all']
            },
            'accountant': {
                password: 'acc123',
                name: 'محاسب رئيسي',
                role: 'accountant',
                permissions: ['financial', 'reports']
            },
            'warehouse': {
                password: 'wh123',
                name: 'مدير المخازن',
                role: 'warehouse',
                permissions: ['inventory', 'purchasing']
            }
        };

        let currentUser = null;

        // دالة ملء بيانات الدخول
        function fillLoginData(username, password) {
            document.getElementById('loginUsername').value = username;
            document.getElementById('loginPassword').value = password;
        }

        // دالة تسجيل الدخول
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();
            const messageDiv = document.getElementById('loginMessage');

            // التحقق من البيانات
            if (!username || !password) {
                showLoginMessage('❌ يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            // التحقق من صحة البيانات
            if (loginUsers[username] && loginUsers[username].password === password) {
                currentUser = {
                    username: username,
                    ...loginUsers[username]
                };

                showLoginMessage('✅ تم تسجيل الدخول بنجاح...', 'success');

                setTimeout(() => {
                    // إخفاء شاشة تسجيل الدخول
                    document.getElementById('loginScreen').style.display = 'none';
                    // إظهار النظام الرئيسي
                    document.getElementById('mainSystem').style.display = 'block';

                    // تحديث واجهة المستخدم
                    updateUserInterface();
                    updateUserStats();

                    // رسالة ترحيب
                    setTimeout(() => {
                        alert(`🎉 مرحباً ${currentUser.name}!\n\nتم تسجيل الدخول بنجاح إلى:\n🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n🎉 نظام المحاسبة والمراجعة الداخلية\n\n✅ النظام يشمل:\n• إدارة المستخدمين الكاملة\n• الدورة المستندية للمبيعات (19 مرحلة)\n• الدورة المستندية للمشتريات (7 مراحل)\n• جميع القوائم المالية الأساسية\n• التقارير التحليلية والإدارية\n• تقارير الأداء والتحليل المالي\n\n🏢 تصميم وتطوير:\nشركة ايه جي تكنولوجي سيستميز\nAG Technology Systems\n\n© 2024 جميع الحقوق محفوظة`);
                    }, 500);
                }, 1500);

            } else {
                showLoginMessage('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        }

        // دالة عرض رسائل تسجيل الدخول
        function showLoginMessage(message, type) {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.innerHTML = message;
            messageDiv.style.display = 'block';

            if (type === 'error') {
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 3000);
            }
        }

        // دالة تحديث واجهة المستخدم حسب صلاحيات المستخدم
        function updateUserInterface() {
            // تحديث معلومات المستخدم في الإعدادات
            const userInfoSection = document.querySelector('#settings .card h3');
            if (userInfoSection && userInfoSection.textContent.includes('👤 بيانات المستخدم')) {
                const userCard = userInfoSection.parentElement;
                userCard.innerHTML = `
                    <h3>👤 بيانات المستخدم</h3>
                    <p><strong>اسم المستخدم:</strong> ${currentUser.username}</p>
                    <p><strong>الاسم الكامل:</strong> ${currentUser.name}</p>
                    <p><strong>الدور:</strong> ${currentUser.role}</p>
                    <p><strong>وقت الدخول:</strong> ${new Date().toLocaleString('ar-EG')}</p>
                    <button class="btn btn-secondary" onclick="logout()">🚪 تسجيل الخروج</button>
                `;
            }

            // إضافة زر تسجيل الخروج في الهيدر
            const header = document.querySelector('.header');
            if (header && !header.querySelector('.logout-btn')) {
                const logoutBtn = document.createElement('button');
                logoutBtn.className = 'btn btn-secondary logout-btn';
                logoutBtn.innerHTML = '🚪 تسجيل الخروج';
                logoutBtn.onclick = logout;
                logoutBtn.style.position = 'absolute';
                logoutBtn.style.top = '20px';
                logoutBtn.style.left = '20px';
                header.style.position = 'relative';
                header.appendChild(logoutBtn);
            }
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                currentUser = null;
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainSystem').style.display = 'none';
                document.getElementById('loginUsername').value = '';
                document.getElementById('loginPassword').value = '';
                document.getElementById('loginMessage').style.display = 'none';
            }
        }

        // ربط نموذج تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('mainLoginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
        });

        // دوال الطباعة
        function printDocument(title, content, isTable = false) {
            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG');

            const printContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50 0%, #81C784 50%, #A5D6A7 100%);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 15px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin: 10px 0;
        }
        .document-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 15px 0;
        }
        .document-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .content {
            margin: 20px 0;
            white-space: pre-line;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            padding-top: 20px;
        }
        .signature-box {
            text-align: center;
            width: 200px;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
        }
        @media print {
            .no-print { display: none !important; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-logo">AG</div>
        <div class="company-name">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</div>
        <div class="document-title">${title}</div>
        <div class="document-info">
            <strong>التاريخ:</strong> ${currentDate} | <strong>الوقت:</strong> ${currentTime}
        </div>
    </div>

    <div class="content">
        ${content}
    </div>

    <div class="signatures">
        <div class="signature-box">
            <div class="signature-line">المحاسب</div>
        </div>
        <div class="signature-box">
            <div class="signature-line">المدير المالي</div>
        </div>
        <div class="signature-box">
            <div class="signature-line">المدير العام</div>
        </div>
    </div>

    <div class="footer">
        <div>
            <strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems<br>
            تصميم وتطوير الأنظمة المحاسبية والإدارية<br>
            © 2024 جميع الحقوق محفوظة
        </div>
    </div>

    <script>
        window.onload = function() {
            window.print();
            setTimeout(function() {
                window.close();
            }, 1000);
        }
    </script>
</body>
</html>
            `;

            printWindow.document.write(printContent);
            printWindow.document.close();
        }

        // طباعة ميزان المراجعة
        function printTrialBalance() {
            const content = `
<table>
    <thead>
        <tr>
            <th>رقم الحساب</th>
            <th>اسم الحساب</th>
            <th>مدين</th>
            <th>دائن</th>
        </tr>
    </thead>
    <tbody>
        <tr><td>1000</td><td>الأصول الثابتة</td><td>3,500,000</td><td>-</td></tr>
        <tr><td>1100</td><td>الأصول المتداولة</td><td>2,750,000</td><td>-</td></tr>
        <tr><td>1110</td><td>النقدية والبنوك</td><td>650,000</td><td>-</td></tr>
        <tr><td>1120</td><td>العملاء</td><td>1,450,000</td><td>-</td></tr>
        <tr><td>1130</td><td>المخزون</td><td>650,000</td><td>-</td></tr>
        <tr><td>2000</td><td>الخصوم المتداولة</td><td>-</td><td>1,200,000</td></tr>
        <tr><td>2100</td><td>الموردون</td><td>-</td><td>980,000</td></tr>
        <tr><td>2200</td><td>مصروفات مستحقة</td><td>-</td><td>220,000</td></tr>
        <tr><td>3000</td><td>رأس المال</td><td>-</td><td>4,000,000</td></tr>
        <tr><td>3100</td><td>الأرباح المحتجزة</td><td>-</td><td>1,200,000</td></tr>
        <tr><td>4000</td><td>إيرادات المبيعات</td><td>-</td><td>5,500,000</td></tr>
        <tr><td>5000</td><td>تكلفة البضاعة المباعة</td><td>3,850,000</td><td>-</td></tr>
        <tr><td>6000</td><td>مصروفات التشغيل</td><td>1,200,000</td><td>-</td></tr>
        <tr><td>7000</td><td>مصروفات إدارية</td><td>350,000</td><td>-</td></tr>
        <tr style="font-weight: bold; background-color: #e8f5e8;">
            <td colspan="2"><strong>الإجمالي</strong></td>
            <td><strong>12,750,000</strong></td>
            <td><strong>12,750,000</strong></td>
        </tr>
    </tbody>
</table>
            `;

            printDocument('⚖️ ميزان المراجعة', content, true);
        }

        // طباعة قائمة الدخل
        function printIncomeStatement() {
            const content = `
<table>
    <thead>
        <tr>
            <th>البيان</th>
            <th>المبلغ (جنيه)</th>
        </tr>
    </thead>
    <tbody>
        <tr style="background-color: #e3f2fd;"><td colspan="2"><strong>الإيرادات</strong></td></tr>
        <tr><td>إيرادات المبيعات</td><td>5,500,000</td></tr>
        <tr><td>إيرادات أخرى</td><td>100,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي الإيرادات</td><td>5,600,000</td></tr>

        <tr style="background-color: #fff3e0;"><td colspan="2"><strong>التكاليف والمصروفات</strong></td></tr>
        <tr><td>تكلفة البضاعة المباعة</td><td>3,850,000</td></tr>
        <tr style="font-weight: bold; color: #4CAF50;"><td>مجمل الربح</td><td>1,750,000</td></tr>

        <tr style="background-color: #fce4ec;"><td colspan="2"><strong>المصروفات التشغيلية</strong></td></tr>
        <tr><td>مصروفات البيع والتوزيع</td><td>450,000</td></tr>
        <tr><td>مصروفات إدارية وعمومية</td><td>350,000</td></tr>
        <tr><td>مصروفات أخرى</td><td>100,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي المصروفات التشغيلية</td><td>900,000</td></tr>

        <tr style="font-weight: bold; color: #2196F3;"><td>صافي الربح قبل الضرائب</td><td>850,000</td></tr>
        <tr><td>ضرائب الدخل (22.5%)</td><td>191,250</td></tr>
        <tr style="font-weight: bold; color: #4CAF50; background-color: #e8f5e8;">
            <td><strong>صافي الربح بعد الضرائب</strong></td>
            <td><strong>658,750</strong></td>
        </tr>

        <tr style="background-color: #f3e5f5;"><td colspan="2"><strong>النسب المالية</strong></td></tr>
        <tr><td>هامش الربح الإجمالي</td><td>31.25%</td></tr>
        <tr><td>هامش الربح الصافي</td><td>11.76%</td></tr>
    </tbody>
</table>
            `;

            printDocument('📋 قائمة الدخل', content, true);
        }

        // طباعة قائمة المركز المالي
        function printBalanceSheet() {
            const content = `
<table>
    <thead>
        <tr>
            <th>البيان</th>
            <th>المبلغ (جنيه)</th>
        </tr>
    </thead>
    <tbody>
        <tr style="background-color: #e3f2fd;"><td colspan="2"><strong>الأصول</strong></td></tr>

        <tr style="background-color: #f0f8ff;"><td colspan="2"><strong>الأصول غير المتداولة</strong></td></tr>
        <tr><td>الأراضي والمباني</td><td>2,500,000</td></tr>
        <tr><td>الآلات والمعدات</td><td>2,800,000</td></tr>
        <tr><td>وسائل النقل</td><td>450,000</td></tr>
        <tr><td>أصول أخرى</td><td>250,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي الأصول غير المتداولة</td><td>6,000,000</td></tr>

        <tr style="background-color: #f0f8ff;"><td colspan="2"><strong>الأصول المتداولة</strong></td></tr>
        <tr><td>النقدية والبنوك</td><td>650,000</td></tr>
        <tr><td>العملاء</td><td>1,450,000</td></tr>
        <tr><td>المخزون</td><td>2,775,000</td></tr>
        <tr><td>مصروفات مقدمة</td><td>125,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي الأصول المتداولة</td><td>5,000,000</td></tr>

        <tr style="font-weight: bold; color: #2196F3; background-color: #e3f2fd;">
            <td><strong>إجمالي الأصول</strong></td>
            <td><strong>11,000,000</strong></td>
        </tr>

        <tr style="background-color: #fff3e0;"><td colspan="2"><strong>الخصوم وحقوق الملكية</strong></td></tr>

        <tr style="background-color: #fef7e0;"><td colspan="2"><strong>الخصوم المتداولة</strong></td></tr>
        <tr><td>الموردون</td><td>980,000</td></tr>
        <tr><td>مصروفات مستحقة</td><td>220,000</td></tr>
        <tr><td>ضرائب مستحقة</td><td>150,000</td></tr>
        <tr><td>قروض قصيرة الأجل</td><td>450,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي الخصوم المتداولة</td><td>1,800,000</td></tr>

        <tr style="background-color: #fef7e0;"><td colspan="2"><strong>الخصوم طويلة الأجل</strong></td></tr>
        <tr><td>قروض طويلة الأجل</td><td>1,000,000</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي الخصوم</td><td>2,800,000</td></tr>

        <tr style="background-color: #e8f5e8;"><td colspan="2"><strong>حقوق الملكية</strong></td></tr>
        <tr><td>رأس المال</td><td>4,000,000</td></tr>
        <tr><td>الأرباح المحتجزة</td><td>3,541,250</td></tr>
        <tr><td>أرباح العام الجاري</td><td>658,750</td></tr>
        <tr style="font-weight: bold;"><td>إجمالي حقوق الملكية</td><td>8,200,000</td></tr>

        <tr style="font-weight: bold; color: #4CAF50; background-color: #e8f5e8;">
            <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
            <td><strong>11,000,000</strong></td>
        </tr>
    </tbody>
</table>
            `;

            printDocument('📊 قائمة المركز المالي (الميزانية العمومية)', content, true);
        }

        // طباعة فاتورة مبيعات
        function printSalesInvoice(invoiceData) {
            const content = `
<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
    <h3 style="color: #4CAF50; margin: 0;">📄 فاتورة مبيعات</h3>
    <p><strong>رقم الفاتورة:</strong> INV-${Date.now()}</p>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
    <div>
        <h4 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">بيانات العميل</h4>
        <p><strong>اسم العميل:</strong> ${invoiceData?.customer || 'شركة الأهرام للطباعة'}</p>
        <p><strong>العنوان:</strong> القاهرة - مصر الجديدة</p>
        <p><strong>الرقم الضريبي:</strong> *********</p>
    </div>
    <div>
        <h4 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">بيانات الفاتورة</h4>
        <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-EG')}</p>
        <p><strong>طريقة السداد:</strong> ${invoiceData?.paymentMethod || 'شيكات مؤجلة'}</p>
        <p><strong>مدة السداد:</strong> 30 يوم</p>
    </div>
</div>

<table>
    <thead>
        <tr>
            <th>الصنف</th>
            <th>الكمية</th>
            <th>الوحدة</th>
            <th>السعر</th>
            <th>الإجمالي</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>${invoiceData?.product || 'ورق كتابة 80 جرام - مقاس A4'}</td>
            <td>${invoiceData?.quantity || '100'}</td>
            <td>طن</td>
            <td>${invoiceData?.price || '25,000'}</td>
            <td>${invoiceData?.total || '2,500,000'}</td>
        </tr>
        <tr style="background-color: #f0f8ff;">
            <td colspan="4" style="text-align: left;"><strong>الإجمالي قبل الضريبة</strong></td>
            <td><strong>${invoiceData?.subtotal || '2,500,000'}</strong></td>
        </tr>
        <tr>
            <td colspan="4" style="text-align: left;">ضريبة القيمة المضافة (14%)</td>
            <td>${invoiceData?.tax || '350,000'}</td>
        </tr>
        <tr style="background-color: #e8f5e8; font-weight: bold; color: #4CAF50;">
            <td colspan="4" style="text-align: left;"><strong>الإجمالي شامل الضريبة</strong></td>
            <td><strong>${invoiceData?.grandTotal || '2,850,000'}</strong></td>
        </tr>
    </tbody>
</table>

<div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
    <h4 style="color: #333;">شروط وأحكام:</h4>
    <ul style="margin: 10px 0; padding-right: 20px;">
        <li>البضاعة المباعة لا ترد ولا تستبدل إلا في حالة العيب</li>
        <li>يتم السداد خلال المدة المتفق عليها</li>
        <li>أي تأخير في السداد يترتب عليه فوائد تأخير</li>
        <li>المحكمة المختصة هي محاكم القاهرة</li>
    </ul>
</div>
            `;

            printDocument('📄 فاتورة مبيعات', content, true);
        }

        // تحديث دوال التصدير لتشمل الطباعة
        function exportTrialBalance() {
            if (confirm('هل تريد طباعة ميزان المراجعة؟')) {
                printTrialBalance();
            }
        }

        function exportIncomeStatement() {
            if (confirm('هل تريد طباعة قائمة الدخل؟')) {
                printIncomeStatement();
            }
        }

        function exportBalanceSheet() {
            if (confirm('هل تريد طباعة قائمة المركز المالي؟')) {
                printBalanceSheet();
            }
        }

        // رسالة ترحيب عند تحميل الصفحة (للمطورين فقط)
        window.addEventListener('load', () => {
            // عرض شاشة تسجيل الدخول أولاً
            console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
            console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
        });
    </script>
</body>
</html>
