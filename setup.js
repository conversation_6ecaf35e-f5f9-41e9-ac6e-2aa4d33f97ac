const fs = require('fs');
const path = require('path');
const { createDatabaseIfNotExists, testConnection, executeQuery } = require('./config/database');

async function setupDatabase() {
  console.log('🚀 بدء إعداد قاعدة البيانات...');

  try {
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    await createDatabaseIfNotExists();

    // اختبار الاتصال
    const connectionTest = await testConnection();
    if (!connectionTest) {
      throw new Error('فشل الاتصال بقاعدة البيانات');
    }

    // قراءة ملف SQL وتنفيذه
    const schemaPath = path.join(__dirname, 'database', 'schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error('ملف schema.sql غير موجود');
    }

    const sqlContent = fs.readFileSync(schemaPath, 'utf8');
    
    // تقسيم الاستعلامات
    const queries = sqlContent
      .split(';')
      .map(query => query.trim())
      .filter(query => query.length > 0 && !query.startsWith('--') && !query.startsWith('/*'));

    console.log(`📝 تنفيذ ${queries.length} استعلام..`);

    // تنفيذ كل استعلام على حدة
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];
      
      try {
        // تجاهل استعلامات USE و CREATE DATABASE
        if (query.toLowerCase().includes('use ') || 
            query.toLowerCase().includes('create database')) {
          continue;
        }

        await executeQuery(query);
        console.log(`✅ تم تنفيذ الاستعلام ${i + 1}/${queries.length}`);
      } catch (error) {
        // تجاهل أخطاء الجداول الموجودة بالفعل
        if (error.message.includes('already exists') || 
            error.message.includes('Duplicate entry')) {
          console.log(`⚠️  تم تجاهل: ${error.message.substring(0, 100)}...`);
          continue;
        }
        
        console.error(`❌ خطأ في الاستعلام ${i + 1}:`, error.message);
        console.error('الاستعلام:', query.substring(0, 200) + '...');
      }
    }

    // التحقق من إنشاء الجداول
    const tables = await executeQuery('SHOW TABLES');
    console.log(`📊 تم إنشاء ${tables.length} جدول:`);
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`   - ${tableName}`);
    });

    // التحقق من وجود المستخدم الإداري
    const adminUsers = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE role = "admin"'
    );

    if (adminUsers[0].count === 0) {
      console.log('👤 إنشاء المستخدم الإداري الافتراضي...');
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await executeQuery(
        `INSERT INTO users (username, email, password_hash, full_name, department, role) 
         VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 'admin', 'admin')`,
        [hashedPassword]
      );
      
      console.log('✅ تم إنشاء المستخدم الإداري');
      console.log('   اسم المستخدم: admin');
      console.log('   كلمة المرور: admin123');
    }

    console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('');
    console.log('📋 معلومات النظام:');
    console.log('   - قاعدة البيانات: accounting_system');
    console.log('   - المستخدم الإداري: admin / admin123');
    console.log('   - المنفذ: 5000');
    console.log('');
    console.log('🚀 يمكنك الآن تشغيل النظام باستخدام: npm start');

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
    process.exit(1);
  }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
