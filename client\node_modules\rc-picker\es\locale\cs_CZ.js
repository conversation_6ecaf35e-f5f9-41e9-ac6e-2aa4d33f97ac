import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'cs_CZ',
  today: 'Dnes',
  now: 'Nyn<PERSON>',
  backToToday: '<PERSON><PERSON><PERSON><PERSON> na dne<PERSON>',
  ok: 'OK',
  clear: 'Vymazat',
  week: 'Týden',
  month: 'Měs<PERSON>c',
  year: 'Rok',
  timeSelect: 'Vybrat čas',
  dateSelect: 'Vybrat datum',
  monthSelect: 'Vyberte měsíc',
  yearSelect: 'Vyberte rok',
  decadeSelect: 'Vyberte dek<PERSON>du',
  dateFormat: 'D.M.YYYY',
  dateTimeFormat: 'D.M.YYYY HH:mm:ss',
  previousMonth: 'P<PERSON>ed<PERSON><PERSON><PERSON> mě<PERSON> (PageUp)',
  nextMonth: 'N<PERSON>ledujíc<PERSON> (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rok (Control + left)',
  nextYear: 'Následuj<PERSON><PERSON><PERSON> rok (Control + right)',
  previousDecade: 'P<PERSON>ed<PERSON><PERSON><PERSON> dek<PERSON>da',
  nextDecade: 'Následující dekáda',
  previousCentury: 'Předchozí století',
  nextCentury: 'Následující století'
});
export default locale;