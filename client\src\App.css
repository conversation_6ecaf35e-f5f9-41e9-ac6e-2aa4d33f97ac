/* تخصيص الخطوط والاتجاه */
* {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body {
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  background-color: #f0f2f5;
}

.App {
  min-height: 100vh;
}

/* شاشة التحميل */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-top: 5px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تخصيص مكونات Ant Design للعربية */
.ant-layout {
  direction: rtl;
}

.ant-menu {
  direction: rtl;
}

.ant-menu-item {
  text-align: right;
}

.ant-table {
  direction: rtl;
}

.ant-table th {
  text-align: right;
}

.ant-table td {
  text-align: right;
}

.ant-form {
  direction: rtl;
}

.ant-form-item-label {
  text-align: right;
}

.ant-input {
  text-align: right;
}

.ant-select {
  text-align: right;
}

.ant-btn {
  font-family: 'Cairo', sans-serif;
}

/* تخصيص الألوان */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --border-color: #d9d9d9;
  --background-color: #f0f2f5;
}

/* تخصيص البطاقات */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(90deg, #fafafa 0%, #ffffff 100%);
}

/* تخصيص الأزرار */
.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* تخصيص الجداول */
.ant-table-thead > tr > th {
  background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff;
}

/* تخصيص النماذج */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* تخصيص الرسائل */
.ant-message {
  direction: rtl;
}

.ant-notification {
  direction: rtl;
}

/* تخصيص القوائم */
.ant-menu-dark {
  background: linear-gradient(180deg, #001529 0%, #002140 100%);
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(90deg, #1890ff 0%, #096dd9 100%);
}

/* تخصيص الإحصائيات */
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stats-number {
  font-size: 2.5em;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 1.1em;
  color: #666;
  font-weight: 500;
}

/* تخصيص الرسوم البيانية */
.chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تخصيص الصفحات */
.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 1.8em;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.page-description {
  color: #666;
  margin-top: 8px;
  font-size: 1.1em;
}

/* تخصيص الأيقونات */
.anticon {
  font-size: 1.2em;
}

/* تخصيص التنبيهات */
.ant-alert {
  border-radius: 8px;
  margin-bottom: 16px;
}

/* تخصيص التبويبات */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab-active {
  color: #1890ff !important;
}

/* تخصيص المودال */
.ant-modal-header {
  background: linear-gradient(90deg, #fafafa 0%, #ffffff 100%);
  border-radius: 8px 8px 0 0;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

/* تخصيص التقويم */
.ant-picker {
  text-align: right;
}

/* تخصيص الدرج */
.ant-drawer-header {
  background: linear-gradient(90deg, #fafafa 0%, #ffffff 100%);
}

/* تخصيص الخطوات */
.ant-steps-item-title {
  font-weight: 500;
}

/* تخصيص التقدم */
.ant-progress-text {
  color: #262626;
  font-weight: 500;
}

/* تخصيص الشارات */
.ant-badge-count {
  font-family: 'Cairo', sans-serif;
}

/* تخصيص التبديل */
.ant-switch-checked {
  background-color: #1890ff;
}

/* تخصيص التقييم */
.ant-rate-star {
  color: #faad14;
}

/* تخصيص الانهيار */
.ant-collapse-header {
  font-weight: 500;
}

/* تخصيص الشجرة */
.ant-tree-title {
  font-weight: 500;
}

/* تخصيص النقل */
.ant-transfer-list-header {
  background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
}

/* تخصيص الجدول الزمني */
.ant-timeline-item-head {
  background-color: #1890ff;
}

/* تخصيص الإرساء */
.ant-anchor-link-title {
  font-weight: 500;
}

/* تخصيص الصورة الرمزية */
.ant-avatar {
  font-family: 'Cairo', sans-serif;
}

/* تخصيص الشريط الجانبي */
.ant-back-top {
  left: 24px;
  right: auto;
}
