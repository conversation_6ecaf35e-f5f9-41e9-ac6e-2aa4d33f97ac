const express = require('express');
const { executeQuery, executeTransaction } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('inventory', 'warehouses', 'audit'));

// الحصول على جميع الأصناف
router.get('/items', async (req, res) => {
  try {
    const { category, active_only = 'true' } = req.query;
    
    let whereClause = '';
    let params = [];

    if (category) {
      whereClause += 'WHERE item_category = ?';
      params.push(category);
    }

    if (active_only === 'true') {
      whereClause += whereClause ? ' AND is_active = TRUE' : 'WHERE is_active = TRUE';
    }

    const items = await executeQuery(`
      SELECT * FROM items 
      ${whereClause}
      ORDER BY item_code
    `, params);

    res.json({
      success: true,
      data: { items }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الأصناف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء صنف جديد
router.post('/items', auditLog('INSERT', 'items'), async (req, res) => {
  try {
    const { 
      item_code, 
      item_name, 
      item_category, 
      unit_of_measure, 
      minimum_stock, 
      maximum_stock, 
      unit_cost 
    } = req.body;

    if (!item_code || !item_name || !item_category || !unit_of_measure) {
      return res.status(400).json({
        success: false,
        message: 'رقم الصنف واسم الصنف وفئة الصنف ووحدة القياس مطلوبة'
      });
    }

    // التحقق من عدم تكرار رقم الصنف
    const existingItem = await executeQuery(
      'SELECT id FROM items WHERE item_code = ?',
      [item_code]
    );

    if (existingItem.length) {
      return res.status(400).json({
        success: false,
        message: 'رقم الصنف موجود بالفعل'
      });
    }

    const result = await executeQuery(
      `INSERT INTO items 
       (item_code, item_name, item_category, unit_of_measure, minimum_stock, maximum_stock, unit_cost) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [item_code, item_name, item_category, unit_of_measure, minimum_stock || 0, maximum_stock || 0, unit_cost || 0]
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الصنف بنجاح',
      data: { itemId: result.insertId }
    });

  } catch (error) {
    console.error('خطأ في إنشاء الصنف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على أرصدة الأصناف
router.get('/balances', async (req, res) => {
  try {
    const { warehouse_id, item_category } = req.query;
    
    let whereClause = '';
    let params = [];

    if (warehouse_id) {
      whereClause += 'WHERE ic.warehouse_id = ?';
      params.push(warehouse_id);
    }

    if (item_category) {
      whereClause += whereClause ? ' AND i.item_category = ?' : 'WHERE i.item_category = ?';
      params.push(item_category);
    }

    const balances = await executeQuery(`
      SELECT 
        i.id as item_id,
        i.item_code,
        i.item_name,
        i.item_category,
        i.unit_of_measure,
        w.warehouse_name,
        ic.warehouse_id,
        ic.balance_quantity,
        ic.balance_value,
        i.minimum_stock,
        i.maximum_stock,
        CASE 
          WHEN ic.balance_quantity <= i.minimum_stock THEN 'low_stock'
          WHEN ic.balance_quantity >= i.maximum_stock THEN 'overstock'
          ELSE 'normal'
        END as stock_status
      FROM items i
      CROSS JOIN warehouses w
      LEFT JOIN (
        SELECT 
          item_id, 
          warehouse_id, 
          balance_quantity, 
          balance_value,
          ROW_NUMBER() OVER (PARTITION BY item_id, warehouse_id ORDER BY created_at DESC) as rn
        FROM item_cards
      ) ic ON i.id = ic.item_id AND w.id = ic.warehouse_id AND ic.rn = 1
      ${whereClause}
      ORDER BY i.item_code, w.warehouse_name
    `, params);

    res.json({
      success: true,
      data: { balances }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الأرصدة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على حركة صنف معين
router.get('/items/:itemId/movements', async (req, res) => {
  try {
    const { itemId } = req.params;
    const { warehouse_id, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE ic.item_id = ?';
    let params = [itemId];

    if (warehouse_id) {
      whereClause += ' AND ic.warehouse_id = ?';
      params.push(warehouse_id);
    }

    if (start_date) {
      whereClause += ' AND ic.transaction_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += ' AND ic.transaction_date <= ?';
      params.push(end_date);
    }

    const movements = await executeQuery(`
      SELECT 
        ic.*,
        i.item_name,
        i.item_code,
        w.warehouse_name,
        u.full_name as created_by_name
      FROM item_cards ic
      JOIN items i ON ic.item_id = i.id
      JOIN warehouses w ON ic.warehouse_id = w.id
      JOIN users u ON ic.created_by = u.id
      ${whereClause}
      ORDER BY ic.transaction_date DESC, ic.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد الحركات الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM item_cards ic ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        movements,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على حركة الصنف:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إضافة حركة مخزون (استلام/صرف/تسوية)
router.post('/movements', auditLog('INSERT', 'item_cards'), async (req, res) => {
  try {
    const { 
      item_id, 
      warehouse_id, 
      transaction_type, 
      quantity_in, 
      quantity_out, 
      unit_cost, 
      reference_number, 
      notes 
    } = req.body;

    if (!item_id || !warehouse_id || !transaction_type) {
      return res.status(400).json({
        success: false,
        message: 'رقم الصنف ورقم المخزن ونوع الحركة مطلوبة'
      });
    }

    if (!quantity_in && !quantity_out) {
      return res.status(400).json({
        success: false,
        message: 'يجب إدخال كمية الوارد أو المنصرف'
      });
    }

    // الحصول على آخر رصيد
    const lastBalance = await executeQuery(`
      SELECT balance_quantity, balance_value 
      FROM item_cards 
      WHERE item_id = ? AND warehouse_id = ? 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [item_id, warehouse_id]);

    const previousQuantity = lastBalance.length ? lastBalance[0].balance_quantity : 0;
    const previousValue = lastBalance.length ? lastBalance[0].balance_value : 0;

    // حساب الرصيد الجديد
    const quantityChange = (quantity_in || 0) - (quantity_out || 0);
    const newQuantity = previousQuantity + quantityChange;

    if (newQuantity < 0) {
      return res.status(400).json({
        success: false,
        message: 'الكمية المطلوب صرفها أكبر من الرصيد المتاح'
      });
    }

    // حساب القيمة الجديدة
    const costPerUnit = unit_cost || 0;
    const valueChange = quantityChange * costPerUnit;
    const newValue = previousValue + valueChange;

    const result = await executeQuery(`
      INSERT INTO item_cards 
      (item_id, warehouse_id, transaction_date, transaction_type, reference_number, 
       quantity_in, quantity_out, unit_cost, total_value, balance_quantity, balance_value, notes, created_by) 
      VALUES (?, ?, CURDATE(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        item_id, warehouse_id, transaction_type, reference_number,
        quantity_in || 0, quantity_out || 0, costPerUnit, valueChange,
        newQuantity, newValue, notes, req.user.id
      ]
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إضافة الحركة بنجاح',
      data: { 
        movementId: result.insertId,
        newBalance: {
          quantity: newQuantity,
          value: newValue
        }
      }
    });

  } catch (error) {
    console.error('خطأ في إضافة حركة المخزون:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
