<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة</title>
</head>
<body style="font-family: Arial; background: #667eea; color: white; padding: 20px; direction: rtl;">
    
    <div style="max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 10px;">
        
        <!-- شاشة الدخول -->
        <div id="login">
            <h1 style="text-align: center;">🏭 شركة مصر ادفو للورق</h1>
            <h2 style="text-align: center;">نظام المحاسبة</h2>
            
            <div id="msg" style="text-align: center; margin: 20px 0;"></div>
            
            <input type="text" id="user" placeholder="اسم المستخدم" style="width: 100%; padding: 10px; margin: 10px 0; border: none; border-radius: 5px; font-size: 16px; box-sizing: border-box;">
            
            <input type="password" id="pass" placeholder="كلمة المرور" style="width: 100%; padding: 10px; margin: 10px 0; border: none; border-radius: 5px; font-size: 16px; box-sizing: border-box;">
            
            <button onclick="doLogin()" style="width: 100%; background: #4CAF50; color: white; border: none; padding: 15px; border-radius: 5px; cursor: pointer; font-size: 18px; margin: 10px 0;">دخول النظام</button>
            
            <div onclick="fastLogin()" style="background: rgba(255,215,0,0.2); padding: 15px; border-radius: 5px; margin: 15px 0; cursor: pointer; border: 1px solid gold; text-align: center;">
                اضغط هنا للدخول السريع: admin / 123
            </div>
            
            <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">
            <p style="text-align: center;">شركة ايه جي تكنولوجي سيستميز</p>
        </div>

        <!-- النظام الرئيسي -->
        <div id="main" style="display: none;">
            <h1 style="text-align: center;">🏭 نظام المحاسبة</h1>
            <p style="text-align: center;">مرحباً مدير النظام</p>
            <button onclick="doLogout()" style="background: red; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px;">خروج</button>
            
            <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">
            
            <h3>الإحصائيات:</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;">
                <div style="background: rgba(76,175,80,0.2); padding: 20px; border-radius: 5px; text-align: center; border: 1px solid #4CAF50;">
                    <div style="font-size: 2em; font-weight: bold;">4</div>
                    <div>المستخدمين</div>
                </div>
                <div style="background: rgba(33,150,243,0.2); padding: 20px; border-radius: 5px; text-align: center; border: 1px solid #2196F3;">
                    <div style="font-size: 2em; font-weight: bold;">2.5M</div>
                    <div>المبيعات</div>
                </div>
            </div>
            
            <h3>العمليات:</h3>
            
            <button onclick="showInvoice()" style="background: #4CAF50; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">📄 فاتورة مبيعات</button>
            
            <button onclick="showPurchase()" style="background: #4CAF50; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">📦 فاتورة مشتريات</button>
            
            <button onclick="showReports()" style="background: #4CAF50; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">📈 التقارير</button>
            
            <button onclick="showInventory()" style="background: #4CAF50; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">🏪 المخازن</button>
            
            <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">
            
            <h3>الطباعة:</h3>
            
            <button onclick="doPrint()" style="background: #2196F3; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">🖨️ طباعة فاتورة</button>
            
            <button onclick="doPrintReport()" style="background: #2196F3; color: white; border: none; padding: 15px 25px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;">🖨️ طباعة تقرير</button>
            
            <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 20px 0;">
            <p style="text-align: center;">شركة ايه جي تكنولوجي سيستميز</p>
        </div>
    </div>

    <script>
        function fastLogin() {
            document.getElementById('user').value = 'admin';
            document.getElementById('pass').value = '123';
            doLogin();
        }

        function doLogin() {
            var u = document.getElementById('user').value;
            var p = document.getElementById('pass').value;
            var msg = document.getElementById('msg');
            
            if (u == 'admin' && p == '123') {
                msg.innerHTML = '<div style="color: green; font-weight: bold;">✅ تم الدخول بنجاح</div>';
                setTimeout(function() {
                    document.getElementById('login').style.display = 'none';
                    document.getElementById('main').style.display = 'block';
                    alert('مرحباً بك في نظام المحاسبة!\n\nيمكنك الآن استخدام جميع الوظائف');
                }, 1000);
            } else {
                msg.innerHTML = '<div style="color: red; font-weight: bold;">❌ خطأ في البيانات</div>';
            }
        }

        function doLogout() {
            if (confirm('هل تريد الخروج؟')) {
                document.getElementById('login').style.display = 'block';
                document.getElementById('main').style.display = 'none';
                document.getElementById('user').value = '';
                document.getElementById('pass').value = '';
                document.getElementById('msg').innerHTML = '';
            }
        }

        function showInvoice() {
            alert('📄 فاتورة مبيعات\n\nرقم الفاتورة: INV-' + Date.now() + '\nالتاريخ: ' + new Date().toLocaleDateString() + '\n\nالعميل: شركة الأهرام للطباعة\nالمنتج: ورق كتابة 80 جرام\nالكمية: 100 طن\nالسعر: 25,000 جنيه/طن\nالإجمالي قبل الضريبة: 2,500,000 جنيه\nضريبة القيمة المضافة: 350,000 جنيه\nالإجمالي شامل الضريبة: 2,850,000 جنيه\n\n✅ تم إنشاء الفاتورة بنجاح');
        }

        function showPurchase() {
            alert('📦 فاتورة مشتريات\n\nرقم الفاتورة: PUR-' + Date.now() + '\nالتاريخ: ' + new Date().toLocaleDateString() + '\n\nالمورد: شركة المواد الخام المصرية\nالصنف: لب الورق\nالكمية: 50 طن\nالسعر: 15,000 جنيه/طن\nالإجمالي: 750,000 جنيه\n\n✅ تم تسجيل فاتورة المشتريات');
        }

        function showReports() {
            alert('📈 التقارير المالية\n\nميزان المراجعة:\nإجمالي المدين: 12,750,000 جنيه\nإجمالي الدائن: 12,750,000 جنيه\n\nقائمة الدخل:\nإيرادات المبيعات: 5,500,000 جنيه\nتكلفة البضاعة المباعة: 3,850,000 جنيه\nمجمل الربح: 1,650,000 جنيه\nالمصروفات: 900,000 جنيه\nصافي الربح: 750,000 جنيه\n\nقائمة المركز المالي:\nإجمالي الأصول: 11,000,000 جنيه\nإجمالي الخصوم: 2,800,000 جنيه\nحقوق الملكية: 8,200,000 جنيه');
        }

        function showInventory() {
            alert('🏪 أرصدة المخازن\n\nالمنتجات النهائية:\n• ورق كتابة 80 جرام: 500 طن - 12,500,000 جنيه\n• ورق طباعة 70 جرام: 300 طن - 6,900,000 جنيه\n• ورق صحف 45 جرام: 200 طن - 3,600,000 جنيه\n\nالمواد الخام:\n• لب الورق: 150 طن - 2,250,000 جنيه\n• كيماويات: 50 طن - 750,000 جنيه\n\nإجمالي قيمة المخزون: 26,000,000 جنيه');
        }

        function doPrint() {
            var w = window.open('', '_blank', 'width=800,height=600');
            w.document.write('<!DOCTYPE html><html><head><meta charset="UTF-8"><title>فاتورة مبيعات</title><style>body{font-family:Arial;direction:rtl;margin:20px;}.header{text-align:center;border-bottom:2px solid #4CAF50;padding:20px;margin-bottom:20px;}</style></head><body><div class="header"><h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1><h2>📄 فاتورة مبيعات</h2><p>رقم الفاتورة: INV-' + Date.now() + '</p><p>التاريخ: ' + new Date().toLocaleDateString() + '</p></div><div><h3>بيانات العميل:</h3><p><strong>العميل:</strong> شركة الأهرام للطباعة والنشر</p><p><strong>العنوان:</strong> القاهرة - مصر الجديدة</p><h3>تفاصيل الفاتورة:</h3><p><strong>المنتج:</strong> ورق كتابة 80 جرام</p><p><strong>الكمية:</strong> 100 طن</p><p><strong>السعر:</strong> 25,000 جنيه/طن</p><p><strong>الإجمالي قبل الضريبة:</strong> 2,500,000 جنيه</p><p><strong>ضريبة القيمة المضافة (14%):</strong> 350,000 جنيه</p><p style="font-size:18px;color:#4CAF50;"><strong>الإجمالي شامل الضريبة: 2,850,000 جنيه</strong></p></div><div style="position:fixed;bottom:0;text-align:center;width:100%;font-size:12px;"><p><strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems</p><p>© 2024 جميع الحقوق محفوظة</p></div><script>window.onload=function(){window.print();}</script></body></html>');
            w.document.close();
        }

        function doPrintReport() {
            var w = window.open('', '_blank', 'width=800,height=600');
            w.document.write('<!DOCTYPE html><html><head><meta charset="UTF-8"><title>ميزان المراجعة</title><style>body{font-family:Arial;direction:rtl;margin:20px;}.header{text-align:center;border-bottom:2px solid #4CAF50;padding:20px;margin-bottom:20px;}table{width:100%;border-collapse:collapse;margin:20px 0;}th,td{border:1px solid #ddd;padding:10px;text-align:right;}th{background-color:#f2f2f2;}</style></head><body><div class="header"><h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1><h2>⚖️ ميزان المراجعة</h2><p>التاريخ: ' + new Date().toLocaleDateString() + '</p></div><table><tr><th>رقم الحساب</th><th>اسم الحساب</th><th>مدين</th><th>دائن</th></tr><tr><td>1000</td><td>الأصول الثابتة</td><td>6,000,000</td><td></td></tr><tr><td>1100</td><td>الأصول المتداولة</td><td>5,000,000</td><td></td></tr><tr><td>1200</td><td>المخزون</td><td>1,750,000</td><td></td></tr><tr><td>2000</td><td>الخصوم المتداولة</td><td></td><td>1,800,000</td></tr><tr><td>3000</td><td>رأس المال</td><td></td><td>4,000,000</td></tr><tr><td>4000</td><td>إيرادات المبيعات</td><td></td><td>5,500,000</td></tr><tr><td>5000</td><td>تكلفة البضاعة المباعة</td><td>3,850,000</td><td></td></tr><tr><td>6000</td><td>مصروفات التشغيل</td><td>900,000</td><td></td></tr><tr style="font-weight:bold;background-color:#f9f9f9;"><td colspan="2">الإجمالي</td><td>17,500,000</td><td>17,500,000</td></tr></table><div style="position:fixed;bottom:0;text-align:center;width:100%;font-size:12px;"><p><strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems</p><p>© 2024 جميع الحقوق محفوظة</p></div><script>window.onload=function(){window.print();}</script></body></html>');
            w.document.close();
        }

        // Enter للدخول
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && document.getElementById('login').style.display !== 'none') {
                doLogin();
            }
        });
    </script>
</body>
</html>
