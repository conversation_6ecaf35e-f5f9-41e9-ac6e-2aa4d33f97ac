"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _labelText = require("./label-text");
Object.keys(_labelText).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _labelText[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _labelText[key];
    }
  });
});
var _placeholderText = require("./placeholder-text");
Object.keys(_placeholderText).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _placeholderText[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _placeholderText[key];
    }
  });
});
var _text = require("./text");
Object.keys(_text).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _text[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _text[key];
    }
  });
});
var _displayValue = require("./display-value");
Object.keys(_displayValue).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _displayValue[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _displayValue[key];
    }
  });
});
var _altText = require("./alt-text");
Object.keys(_altText).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _altText[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _altText[key];
    }
  });
});
var _title = require("./title");
Object.keys(_title).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _title[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _title[key];
    }
  });
});
var _role = require("./role");
Object.keys(_role).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _role[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _role[key];
    }
  });
});
var _testId = require("./test-id");
Object.keys(_testId).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _testId[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _testId[key];
    }
  });
});