import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Alert, Typography, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { authService } from '../../services/authService';
import './Login.css';

const { Title, Text } = Typography;

const Login = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');

    try {
      const response = await authService.login(values.username, values.password);
      
      if (response.success) {
        onLogin(response.data.user, response.data.token);
      } else {
        setError(response.message || 'خطأ في تسجيل الدخول');
      }
    } catch (error) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay">
          <Card className="login-card" bordered={false}>
            <div className="login-header">
              <div className="login-logo">
                <div className="logo-icon">📊</div>
              </div>
              <Title level={2} className="login-title">
                نظام المحاسبة والمراجعة الداخلية
              </Title>
              <Text className="login-subtitle">
                مرحباً بك في النظام المحاسبي الشامل
              </Text>
            </div>

            {error && (
              <Alert
                message="خطأ في تسجيل الدخول"
                description={error}
                type="error"
                showIcon
                closable
                onClose={() => setError('')}
                style={{ marginBottom: 24 }}
              />
            )}

            <Form
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
              layout="vertical"
            >
              <Form.Item
                name="username"
                label="اسم المستخدم"
                rules={[
                  { required: true, message: 'يرجى إدخال اسم المستخدم' },
                  { min: 3, message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="أدخل اسم المستخدم"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="كلمة المرور"
                rules={[
                  { required: true, message: 'يرجى إدخال كلمة المرور' },
                  { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="أدخل كلمة المرور"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                  className="login-button"
                >
                  {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                </Button>
              </Form.Item>
            </Form>

            <div className="login-footer">
              <Space direction="vertical" size="small" style={{ width: '100%', textAlign: 'center' }}>
                <Text type="secondary">
                  للتجربة استخدم:
                </Text>
                <Text code>اسم المستخدم: admin</Text>
                <Text code>كلمة المرور: admin123</Text>
              </Space>
            </div>

            <div className="login-info">
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>مميزات النظام:</Text>
                <Text>📊 الحسابات المالية والتكاليف</Text>
                <Text>🏪 إدارة المخازن والمستودعات</Text>
                <Text>🛒 إدارة المشتريات والموردين</Text>
                <Text>💰 إدارة الخزينة والمدفوعات</Text>
                <Text>📄 مخازن الورق والإنتاج</Text>
                <Text>📈 التقارير والمراجعة الداخلية</Text>
              </Space>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
