// خادم سريع للنظام المحاسبي
const http = require('http');

// بيانات تجريبية
const mockData = {
  users: [
    { id: 1, username: 'admin', full_name: 'مدير النظام', department: 'admin', role: 'admin' },
    { id: 2, username: 'accountant', full_name: 'محاسب', department: 'financial', role: 'user' }
  ],
  accounts: [
    { id: 1, account_code: '1000', account_name: 'الأصول', account_type: 'asset', balance: 150000 },
    { id: 2, account_code: '1100', account_name: 'الأصول المتداولة', account_type: 'asset', balance: 75000 },
    { id: 3, account_code: '2000', account_name: 'الخصوم', account_type: 'liability', balance: 50000 },
    { id: 4, account_code: '3000', account_name: 'حقوق الملكية', account_type: 'equity', balance: 100000 }
  ],
  items: [
    { id: 1, item_code: 'ITM001', item_name: 'مو<PERSON> خام أساسية', category: 'raw_material', quantity: 500, unit: 'كيلو' },
    { id: 2, item_code: 'ITM002', item_name: 'وقود ديزل', category: 'fuel', quantity: 1000, unit: 'لتر' },
    { id: 3, item_code: 'ITM003', item_name: 'قطع غيار', category: 'spare_parts', quantity: 50, unit: 'قطعة' }
  ],
  suppliers: [
    { id: 1, supplier_code: 'SUP001', supplier_name: 'شركة المواد الخام', contact: '***********' },
    { id: 2, supplier_code: 'SUP002', supplier_name: 'مؤسسة الوقود', contact: '***********' }
  ]
};

const server = http.createServer((req, res) => {
  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json; charset=utf-8');

  // معالجة OPTIONS request
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  const method = req.method;

  console.log(`📥 ${method} ${url}`);

  // الصفحة الرئيسية
  if (url === '/' || url === '/api') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'مرحباً بك في نظام المحاسبة والمراجعة الداخلية! 🎉',
      version: '1.0.0',
      status: 'يعمل بنجاح',
      timestamp: new Date().toISOString(),
      endpoints: [
        'GET /api/health - حالة النظام',
        'GET /api/info - معلومات النظام',
        'POST /api/auth/login - تسجيل الدخول',
        'GET /api/auth/users - قائمة المستخدمين',
        'GET /api/financial/accounts - الحسابات المالية',
        'GET /api/inventory/items - الأصناف',
        'GET /api/purchasing/suppliers - الموردين'
      ]
    }, null, 2));

  // حالة النظام
  } else if (url === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      message: 'النظام يعمل بشكل طبيعي',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    }, null, 2));

  // معلومات النظام
  } else if (url === '/api/info') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      system: {
        name: 'نظام المحاسبة والمراجعة الداخلية',
        version: '1.0.0',
        author: 'Ashraf',
        description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية'
      },
      modules: {
        financial: 'الحسابات المالية والتكاليف',
        inventory: 'إدارة المخازن',
        purchasing: 'إدارة المشتريات',
        treasury: 'إدارة الخزينة',
        paper_warehouse: 'مخازن الورق',
        reports: 'التقارير والمراجعة'
      },
      statistics: {
        total_accounts: mockData.accounts.length,
        total_items: mockData.items.length,
        total_suppliers: mockData.suppliers.length,
        total_users: mockData.users.length
      }
    }, null, 2));

  // تسجيل الدخول
  } else if (url === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const { username, password } = JSON.parse(body);
        
        if (username === 'admin' && password === 'admin123') {
          res.writeHead(200);
          res.end(JSON.stringify({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            data: {
              token: 'mock-jwt-token-' + Date.now(),
              user: {
                id: 1,
                username: 'admin',
                full_name: 'مدير النظام',
                department: 'admin',
                role: 'admin'
              }
            }
          }, null, 2));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({
            success: false,
            message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
          }, null, 2));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          message: 'بيانات غير صحيحة'
        }, null, 2));
      }
    });

  // قائمة المستخدمين
  } else if (url === '/api/auth/users') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: { users: mockData.users }
    }, null, 2));

  // الحسابات المالية
  } else if (url === '/api/financial/accounts') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: { accounts: mockData.accounts }
    }, null, 2));

  // الأصناف
  } else if (url === '/api/inventory/items') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: { items: mockData.items }
    }, null, 2));

  // أرصدة المخازن
  } else if (url === '/api/inventory/balances') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        balances: mockData.items.map(item => ({
          ...item,
          warehouse_name: 'المخزن الرئيسي',
          current_value: item.quantity * 50,
          stock_status: item.quantity > 100 ? 'طبيعي' : 'نقص في المخزون'
        }))
      }
    }, null, 2));

  // الموردين
  } else if (url === '/api/purchasing/suppliers') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: { suppliers: mockData.suppliers }
    }, null, 2));

  // ميزان المراجعة
  } else if (url === '/api/reports/trial-balance') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        trial_balance: mockData.accounts.map(acc => ({
          ...acc,
          total_debit: acc.account_type === 'asset' ? acc.balance : 0,
          total_credit: acc.account_type !== 'asset' ? acc.balance : 0
        })),
        totals: {
          total_debits: 225000,
          total_credits: 150000
        }
      }
    }, null, 2));

  // صفحة غير موجودة
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({
      success: false,
      message: 'الصفحة المطلوبة غير موجودة',
      available_endpoints: [
        'GET /api/health',
        'GET /api/info', 
        'POST /api/auth/login',
        'GET /api/financial/accounts',
        'GET /api/inventory/items',
        'GET /api/purchasing/suppliers'
      ]
    }, null, 2));
  }
});

const PORT = 5000;

server.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 خادم النظام المحاسبي يعمل بنجاح!');
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
  console.log(`📅 الوقت: ${new Date().toLocaleString('ar-EG')}`);
  console.log('✅ جاهز لاستقبال الطلبات من الواجهة');
  console.log('');
  console.log('📋 APIs المتاحة:');
  console.log('   - GET /api/health');
  console.log('   - GET /api/info');
  console.log('   - POST /api/auth/login');
  console.log('   - GET /api/financial/accounts');
  console.log('   - GET /api/inventory/items');
  console.log('   - GET /api/purchasing/suppliers');
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ المنفذ ${PORT} مستخدم بالفعل`);
    console.log('💡 أوقف العملية الأخرى أو استخدم منفذ آخر');
  } else {
    console.error('❌ خطأ في الخادم:', err.message);
  }
});

module.exports = server;
