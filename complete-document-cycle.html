<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>الدورة المستندية الكاملة - شركة مصر ادفو</title>
<style>
body {
    font-family: Arial;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    direction: rtl;
    margin: 0;
}
.container {
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(255,255,255,0.1);
    padding: 30px;
    border-radius: 15px;
}
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
}
.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #4CAF50, #81C784);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    margin: 0 auto 20px;
}
button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 5px;
    transition: all 0.3s;
}
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.btn-blue { background: linear-gradient(45deg, #2196F3, #1976D2); }
.btn-red { background: linear-gradient(45deg, #f44336, #d32f2f); }
.btn-orange { background: linear-gradient(45deg, #FF9800, #F57C00); }
input, select, textarea {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    box-sizing: border-box;
    background: rgba(255,255,255,0.9);
    color: #333;
}
textarea { height: 80px; resize: vertical; }
.hidden { display: none; }
.quick {
    background: rgba(255,215,0,0.3);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    cursor: pointer;
    border: 2px solid rgba(255,215,0,0.7);
    text-align: center;
    font-weight: bold;
}
.quick:hover {
    background: rgba(255,215,0,0.5);
    transform: scale(1.02);
}
.cycle-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 20px 0;
}
.cycle-card {
    background: rgba(255,255,255,0.15);
    padding: 25px;
    border-radius: 15px;
    border: 2px solid rgba(255,255,255,0.3);
}
.cycle-card h3 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 20px;
    text-align: center;
}
.step {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    border-left: 4px solid #666;
    font-size: 14px;
    line-height: 1.6;
}
.step.completed {
    background: rgba(76,175,80,0.2);
    border-left-color: #4CAF50;
}
.step.current {
    background: rgba(255,193,7,0.2);
    border-left-color: #FFC107;
    animation: pulse 2s infinite;
}
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255,193,7,0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255,193,7,0); }
    100% { box-shadow: 0 0 0 0 rgba(255,193,7,0); }
}
.step-number {
    display: inline-block;
    background: #4CAF50;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    margin-left: 10px;
    font-weight: bold;
    font-size: 14px;
}
.step.current .step-number {
    background: #FFC107;
    color: #333;
}
.form-section {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 15px 0;
}
.document-list {
    background: rgba(255,255,255,0.05);
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    border: 1px solid rgba(255,255,255,0.2);
}
.document-item {
    padding: 8px;
    margin: 5px 0;
    background: rgba(255,255,255,0.1);
    border-radius: 5px;
    font-size: 13px;
}
.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4CAF50, #81C784);
    transition: width 0.5s ease;
}
.footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    border-top: 2px solid rgba(255,255,255,0.3);
}
</style>
</head>
<body>

<div class="container">
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen">
        <div class="header">
            <div class="logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>📋 نظام الدورة المستندية الكاملة</h2>
            <p>تطبيق الدورة المستندية وفقاً للتعليمات الإدارية المعتمدة</p>
        </div>
        
        <div id="loginMessage"></div>
        
        <input type="text" id="username" placeholder="👤 اسم المستخدم">
        <input type="password" id="password" placeholder="🔒 كلمة المرور">
        
        <button onclick="login()" style="width: 100%; font-size: 18px;">🚀 دخول النظام</button>
        
        <div class="quick" onclick="quickLogin()">
            🔥 دخول سريع للتجربة 🔥<br>
            اضغط هنا للدخول: admin / admin123
        </div>
        
        <div class="footer">
            <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
            <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
            <p>AG Technology Systems</p>
            <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- النظام الرئيسي -->
    <div id="mainSystem" class="hidden">
        <div class="header">
            <div class="logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>📋 نظام الدورة المستندية الكاملة</h2>
            <p>مرحباً <span id="currentUser" style="color: #ffd700;">مدير النظام</span> | 
               <button class="btn-red" onclick="logout()">🚪 خروج</button></p>
        </div>

        <!-- اختيار نوع الدورة -->
        <div id="cycleSelection">
            <h3 style="text-align: center; color: #ffd700; margin: 30px 0;">اختر نوع الدورة المستندية:</h3>
            
            <div class="cycle-container">
                <div class="cycle-card">
                    <h3>📄 دورة مبيعات الورق</h3>
                    <p style="text-align: center; margin: 15px 0;">الدورة المستندية الكاملة لمبيعات الورق (19 خطوة)</p>
                    <p style="font-size: 12px; text-align: center;">وفقاً للتعليمات الإدارية المعتمدة من الشركة</p>
                    <button onclick="startSalesCycle()" style="width: 100%;">🚀 بدء دورة المبيعات</button>
                </div>
                
                <div class="cycle-card">
                    <h3>📦 دورة البضاعة الواردة</h3>
                    <p style="text-align: center; margin: 15px 0;">دورة استلام وفحص البضاعة الواردة (7 خطوات)</p>
                    <p style="font-size: 12px; text-align: center;">وفقاً للتعليمات الإدارية للبضائع الواردة</p>
                    <button onclick="startIncomingCycle()" style="width: 100%;">🚀 بدء دورة الوارد</button>
                </div>
            </div>
        </div>

        <!-- دورة المبيعات -->
        <div id="salesCycle" class="hidden">
            <h3 style="text-align: center; color: #ffd700;">📄 دورة مبيعات الورق (19 خطوة)</h3>
            <button class="btn-blue" onclick="showCycleSelection()">🔙 العودة للقائمة الرئيسية</button>
            
            <div class="progress-bar">
                <div id="salesProgress" class="progress-fill" style="width: 0%;"></div>
            </div>
            <p style="text-align: center; margin: 10px 0;">التقدم: <span id="salesProgressText">0%</span></p>
            
            <div id="salesSteps">
                <!-- سيتم إضافة الخطوات هنا بواسطة JavaScript -->
            </div>
            
            <div class="form-section">
                <h4 style="color: #ffd700;">📝 بيانات العملية:</h4>
                
                <div class="form-row">
                    <div>
                        <label>اسم العميل:</label>
                        <input type="text" id="customerName" placeholder="اسم العميل">
                    </div>
                    <div>
                        <label>نوع العميل:</label>
                        <select id="customerType">
                            <option value="">اختر نوع العميل</option>
                            <option value="نقدي">عميل نقدي</option>
                            <option value="ائتماني">عميل ائتماني (أجل)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label>نوع الورق:</label>
                        <select id="paperType">
                            <option value="">اختر نوع الورق</option>
                            <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                            <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                            <option value="ورق صحف 45 جرام">ورق صحف 45 جرام</option>
                        </select>
                    </div>
                    <div>
                        <label>المقاس:</label>
                        <select id="paperSize">
                            <option value="">اختر المقاس</option>
                            <option value="A4">A4</option>
                            <option value="A3">A3</option>
                            <option value="70×100">70×100 سم</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label>الكمية (طن):</label>
                        <input type="number" id="quantity" placeholder="الكمية المطلوبة">
                    </div>
                    <div>
                        <label>سعر الطن (جنيه):</label>
                        <input type="number" id="unitPrice" placeholder="سعر الطن">
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label>طريقة السداد:</label>
                        <select id="paymentMethod">
                            <option value="">اختر طريقة السداد</option>
                            <option value="نقدي">نقدي</option>
                            <option value="شيك حال">شيك حال</option>
                            <option value="دفع إلكتروني">دفع إلكتروني</option>
                            <option value="شيكات آجلة">شيكات آجلة</option>
                        </select>
                    </div>
                    <div>
                        <label>فترة الائتمان (يوم):</label>
                        <input type="number" id="creditPeriod" placeholder="فترة الائتمان">
                    </div>
                </div>
                
                <div>
                    <label>ملاحظات:</label>
                    <textarea id="notes" placeholder="ملاحظات إضافية"></textarea>
                </div>
                
                <button onclick="nextSalesStep()" style="width: 100%;">⏭️ الخطوة التالية</button>
                <button class="btn-orange" onclick="resetSalesCycle()">🔄 إعادة تشغيل الدورة</button>
            </div>
        </div>

        <!-- دورة البضاعة الواردة -->
        <div id="incomingCycle" class="hidden">
            <h3 style="text-align: center; color: #ffd700;">📦 دورة البضاعة الواردة (7 خطوات)</h3>
            <button class="btn-blue" onclick="showCycleSelection()">🔙 العودة للقائمة الرئيسية</button>
            
            <div class="progress-bar">
                <div id="incomingProgress" class="progress-fill" style="width: 0%;"></div>
            </div>
            <p style="text-align: center; margin: 10px 0;">التقدم: <span id="incomingProgressText">0%</span></p>
            
            <div id="incomingSteps">
                <!-- سيتم إضافة الخطوات هنا بواسطة JavaScript -->
            </div>
            
            <div class="form-section">
                <h4 style="color: #ffd700;">📝 بيانات الشحنة:</h4>
                
                <div class="form-row">
                    <div>
                        <label>اسم المورد:</label>
                        <input type="text" id="supplierName" placeholder="اسم المورد">
                    </div>
                    <div>
                        <label>رقم فاتورة المورد:</label>
                        <input type="text" id="invoiceNumber" placeholder="رقم الفاتورة">
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label>نوع المادة:</label>
                        <select id="materialType">
                            <option value="">اختر نوع المادة</option>
                            <option value="لب الورق">لب الورق</option>
                            <option value="كيماويات">كيماويات</option>
                            <option value="مازوت">مازوت</option>
                            <option value="سولار">سولار</option>
                            <option value="قطع غيار">قطع غيار</option>
                        </select>
                    </div>
                    <div>
                        <label>الكمية المستلمة:</label>
                        <input type="number" id="receivedQuantity" placeholder="الكمية">
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label>رقم السيارة:</label>
                        <input type="text" id="truckNumber" placeholder="رقم السيارة">
                    </div>
                    <div>
                        <label>اسم السائق:</label>
                        <input type="text" id="driverName" placeholder="اسم السائق">
                    </div>
                </div>
                
                <button onclick="nextIncomingStep()" style="width: 100%;">⏭️ الخطوة التالية</button>
                <button class="btn-orange" onclick="resetIncomingCycle()">🔄 إعادة تشغيل الدورة</button>
            </div>
        </div>

        <div class="footer">
            <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
            <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
            <p>AG Technology Systems</p>
            <p style="font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
            <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>
</div>

<script>
// متغيرات النظام
let currentSalesStep = 0;
let currentIncomingStep = 0;

// خطوات دورة المبيعات (19 خطوة) - وفقاً للتعليمات الإدارية
const salesSteps = [
    "استلام طلب العميل مع المستندات المطلوبة والتأكد من توفر المخزون",
    "عرض على لجنة تنمية المبيعات ومجلس الإدارة للموافقة على التعامل بالأجل",
    "الارتباط مع العميل وفقاً لآخر قائمة أسعار معتمدة من مجلس الإدارة",
    "استلام أمر التوريد مختوم من العميل مع المواصفات والمواعيد",
    "إرسال مذكرة بأمر التوريد للمركز الرئيسي (المالي - المراجعة - الفنية)",
    "استلام الشيكات أو إشعار الدفع الإلكتروني حسب طريقة السداد",
    "إرسال مذكرة شحن للمركز الرئيسي",
    "تسليم مذكرة لإدارة الحسابات مع أصل الشيك قبل الشحن",
    "طلب السيارات من مقاول النقل المتعاقد معه",
    "دخول السيارة من البوابة وتسجيلها بسجل البوابة ووزنها فارغة",
    "شحن الكمية بواسطة لجنة (أمين المخزن - الإنتاج - المعمل) ووزن السيارة محملة",
    "تحرير علم تصدير وتوقيع السائق ومقاول النقل على استلام البضاعة",
    "إعداد إذن صرف منتجات بناء على علم التصدير وعلم الوزن",
    "إعداد تصريح خروج ورق مرقم ومسلسل واعتماده من رئيس القطاع المالي",
    "إعداد بيان بكميات الورق المباعة وتسليمه لإدارة الحسابات",
    "استلام البيان ومطابقته مع المستندات وإصدار الفاتورة النهائية",
    "إرسال أصل الفواتير للقطاع التجاري وإعداد إذن تسوية لحساب العميل",
    "إعداد بيان شهري بحركة حسابات العملاء وعرضه على لجنة تنمية المبيعات",
    "جرد مخازن الورق شهرياً ومطابقة الرصيد الدفتري مع الفعلي"
];

// خطوات دورة البضاعة الواردة (7 خطوات) - وفقاً للتعليمات الإدارية
const incomingSteps = [
    "تسجيل البضاعة بسجل البوابة وتوقيع أمين البوابة على الفاتورة",
    "وزن السيارة بالحمولة ثم فارغة وتسجيل صافي الوزن بكارت الميزان",
    "فحص عينة للمواد البترولية بواسطة لجنة المراجعة والمعمل وأمين المخزن",
    "دخول السيارة للتفريغ ومراجعة المستندات والتسجيل بسجل المخازن",
    "تحرير محضر فحص وتوقيعه من أمين المخزن ومراقب الأمن والمندوب الفني",
    "اعتماد محضر الفحص من رئيس القطاع المختص ورئيس القطاع المالي",
    "تحرير علم استلام وإرساله لحسابات المخازن للتسعير والإثبات بالحاسب"
];

// دوال تسجيل الدخول
function quickLogin() {
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';
    login();
}

function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const messageDiv = document.getElementById('loginMessage');

    if (!username || !password) {
        messageDiv.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
        return;
    }

    if (username === 'admin' && password === 'admin123') {
        messageDiv.innerHTML = '<div style="color: green; text-align: center; padding: 10px;">✅ تم تسجيل الدخول بنجاح...</div>';

        setTimeout(function() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainSystem').classList.remove('hidden');
            alert('🎉 مرحباً بك في نظام الدورة المستندية!\n\nالنظام يطبق:\n• دورة مبيعات الورق (19 خطوة)\n• دورة البضاعة الواردة (7 خطوات)\n\nوفقاً للتعليمات الإدارية المعتمدة\n\n🏢 شركة ايه جي تكنولوجي سيستميز');
        }, 1500);

    } else {
        messageDiv.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
    }
}

function logout() {
    if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('mainSystem').classList.add('hidden');
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('loginMessage').innerHTML = '';
        showCycleSelection();
    }
}

// دوال التنقل
function showCycleSelection() {
    document.getElementById('cycleSelection').classList.remove('hidden');
    document.getElementById('salesCycle').classList.add('hidden');
    document.getElementById('incomingCycle').classList.add('hidden');
}

function startSalesCycle() {
    document.getElementById('cycleSelection').classList.add('hidden');
    document.getElementById('salesCycle').classList.remove('hidden');
    resetSalesCycle();
}

function startIncomingCycle() {
    document.getElementById('cycleSelection').classList.add('hidden');
    document.getElementById('incomingCycle').classList.remove('hidden');
    resetIncomingCycle();
}

// دوال دورة المبيعات
function resetSalesCycle() {
    currentSalesStep = 0;
    displaySalesSteps();
    updateSalesProgress();
}

function displaySalesSteps() {
    const container = document.getElementById('salesSteps');
    container.innerHTML = '';

    salesSteps.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = 'step';

        if (index < currentSalesStep) {
            stepDiv.classList.add('completed');
        } else if (index === currentSalesStep) {
            stepDiv.classList.add('current');
        }

        stepDiv.innerHTML = `
            <span class="step-number">${index + 1}</span>
            <strong>${step}</strong>
            ${index === currentSalesStep ? ' ← الخطوة الحالية' : ''}
            ${index < currentSalesStep ? ' ✅ مكتملة' : ''}
        `;

        container.appendChild(stepDiv);
    });
}

function updateSalesProgress() {
    const progress = (currentSalesStep / salesSteps.length) * 100;
    document.getElementById('salesProgress').style.width = progress + '%';
    document.getElementById('salesProgressText').textContent = Math.round(progress) + '%';
}

function nextSalesStep() {
    if (currentSalesStep === 0) {
        // التحقق من البيانات المطلوبة
        const customer = document.getElementById('customerName').value;
        const customerType = document.getElementById('customerType').value;
        const paperType = document.getElementById('paperType').value;
        const quantity = document.getElementById('quantity').value;
        const price = document.getElementById('unitPrice').value;

        if (!customer || !customerType || !paperType || !quantity || !price) {
            alert('❌ يرجى إدخال جميع البيانات المطلوبة قبل بدء الدورة:\n• اسم العميل\n• نوع العميل\n• نوع الورق\n• الكمية\n• سعر الطن');
            return;
        }
    }

    if (currentSalesStep < salesSteps.length) {
        // تنفيذ الخطوة الحالية
        executeSalesStep(currentSalesStep);
        currentSalesStep++;
        displaySalesSteps();
        updateSalesProgress();

        if (currentSalesStep >= salesSteps.length) {
            alert('🎉 تم إكمال دورة المبيعات بنجاح!\n\nتم تنفيذ جميع الخطوات الـ 19 للدورة المستندية\nوفقاً للتعليمات الإدارية المعتمدة\n\n✅ العملية مكتملة وجاهزة للأرشفة\n\n📁 رقم الملف: SALES-' + Date.now());
        }
    }
}
