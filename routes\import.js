const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken, authorize, auditLog } = require('../middleware/auth');
const ExcelImporter = require('../utils/excelImporter');

const router = express.Router();

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/imports';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx أو .xls)'));
    }
  }
});

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);

// تحميل قوالب الاستيراد
router.get('/templates/:type', (req, res) => {
  try {
    const { type } = req.params;
    
    const buffer = ExcelImporter.createTemplate(type);
    
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="template-${type}.xlsx"`);
    res.send(buffer);

  } catch (error) {
    console.error('خطأ في إنشاء القالب:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

// استيراد الحسابات المالية
router.post('/financial-accounts', 
  authorize('admin', 'manager'), 
  upload.single('file'), 
  auditLog('IMPORT', 'financial_accounts'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'يرجى رفع ملف Excel'
        });
      }

      const importer = new ExcelImporter();
      const data = importer.readExcelFile(req.file.path);
      const result = await importer.importFinancialAccounts(data, req.user.id);

      // حذف الملف المؤقت
      fs.unlinkSync(req.file.path);

      res.json({
        success: result.success,
        message: result.summary,
        data: {
          successCount: result.successCount,
          errors: result.errors,
          warnings: result.warnings
        }
      });

    } catch (error) {
      // حذف الملف المؤقت في حالة الخطأ
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      console.error('خطأ في استيراد الحسابات المالية:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في استيراد البيانات',
        error: error.message
      });
    }
  }
);

// استيراد الأصناف
router.post('/items', 
  authorize('admin', 'manager'), 
  upload.single('file'), 
  auditLog('IMPORT', 'items'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'يرجى رفع ملف Excel'
        });
      }

      const importer = new ExcelImporter();
      const data = importer.readExcelFile(req.file.path);
      const result = await importer.importItems(data, req.user.id);

      // حذف الملف المؤقت
      fs.unlinkSync(req.file.path);

      res.json({
        success: result.success,
        message: result.summary,
        data: {
          successCount: result.successCount,
          errors: result.errors,
          warnings: result.warnings
        }
      });

    } catch (error) {
      // حذف الملف المؤقت في حالة الخطأ
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      console.error('خطأ في استيراد الأصناف:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في استيراد البيانات',
        error: error.message
      });
    }
  }
);

// استيراد الموردين
router.post('/suppliers', 
  authorize('admin', 'manager'), 
  upload.single('file'), 
  auditLog('IMPORT', 'suppliers'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'يرجى رفع ملف Excel'
        });
      }

      const importer = new ExcelImporter();
      const data = importer.readExcelFile(req.file.path);
      const result = await importer.importSuppliers(data, req.user.id);

      // حذف الملف المؤقت
      fs.unlinkSync(req.file.path);

      res.json({
        success: result.success,
        message: result.summary,
        data: {
          successCount: result.successCount,
          errors: result.errors,
          warnings: result.warnings
        }
      });

    } catch (error) {
      // حذف الملف المؤقت في حالة الخطأ
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      console.error('خطأ في استيراد الموردين:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ في استيراد البيانات',
        error: error.message
      });
    }
  }
);

// معاينة البيانات قبل الاستيراد
router.post('/preview', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'يرجى رفع ملف Excel'
      });
    }

    const importer = new ExcelImporter();
    const data = importer.readExcelFile(req.file.path);

    // حذف الملف المؤقت
    fs.unlinkSync(req.file.path);

    // إرجاع أول 10 صفوف للمعاينة
    const preview = data.slice(0, 11); // الرأس + 10 صفوف

    res.json({
      success: true,
      data: {
        headers: preview[0] || [],
        rows: preview.slice(1) || [],
        totalRows: data.length - 1,
        previewRows: Math.min(10, data.length - 1)
      }
    });

  } catch (error) {
    // حذف الملف المؤقت في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    console.error('خطأ في معاينة البيانات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في قراءة الملف',
      error: error.message
    });
  }
});

// معالج الأخطاء لـ multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'
      });
    }
  }

  if (error.message) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }

  next(error);
});

module.exports = router;
