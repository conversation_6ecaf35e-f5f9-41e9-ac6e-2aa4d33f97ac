// خادم بسيط للاختبار
const http = require('http');

const server = http.createServer((req, res) => {
  // إعداد headers للاستجابة
  res.writeHead(200, {
    'Content-Type': 'application/json; charset=utf-8',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });

  // الاستجابة حسب المسار
  if (req.url === '/') {
    res.end(JSON.stringify({
      success: true,
      message: '🎉 مرحباً بك في نظام المحاسبة والمراجعة الداخلية!',
      version: '1.0.0',
      status: 'يعمل بنجاح',
      features: [
        '📊 الحسابات المالية والتكاليف',
        '🏪 إدارة المخازن',
        '🛒 إدارة المشتريات',
        '💰 إدارة الخزينة',
        '📄 مخازن الورق',
        '📈 التقارير والمراجعة'
      ],
      next_steps: [
        '1. قم بإعداد قاعدة البيانات: npm run setup',
        '2. استخدم النظام الكامل: npm start',
        '3. سجل دخول باستخدام: admin / admin123'
      ]
    }, null, 2));
  } else if (req.url === '/api/info') {
    res.end(JSON.stringify({
      success: true,
      system: {
        name: 'نظام المحاسبة والمراجعة الداخلية',
        version: '1.0.0',
        author: 'Ashraf',
        description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية',
        created: new Date().toISOString()
      },
      modules: {
        financial: 'الحسابات المالية والتكاليف',
        inventory: 'إدارة المخازن',
        purchasing: 'إدارة المشتريات',
        treasury: 'إدارة الخزينة',
        paper_warehouse: 'مخازن الورق',
        reports: 'التقارير والمراجعة'
      }
    }, null, 2));
  } else if (req.url === '/api/health') {
    res.end(JSON.stringify({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'النظام يعمل بشكل طبيعي',
      uptime: process.uptime(),
      memory: process.memoryUsage()
    }, null, 2));
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({
      success: false,
      message: 'الصفحة المطلوبة غير موجودة',
      available_endpoints: [
        'GET /',
        'GET /api/info',
        'GET /api/health'
      ]
    }, null, 2));
  }
});

const PORT = 5000;

server.listen(PORT, () => {
  console.log('🚀 الخادم البسيط يعمل على المنفذ', PORT);
  console.log('🌐 افتح المتصفح على: http://localhost:' + PORT);
  console.log('');
  console.log('📋 الروابط المتاحة:');
  console.log('   - الصفحة الرئيسية: http://localhost:' + PORT);
  console.log('   - معلومات النظام: http://localhost:' + PORT + '/api/info');
  console.log('   - حالة النظام: http://localhost:' + PORT + '/api/health');
  console.log('');
  console.log('✅ النظام جاهز للاستخدام!');
});

server.on('error', (err) => {
  console.error('❌ خطأ في الخادم:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log('💡 المنفذ 5000 مستخدم بالفعل. جرب منفذ آخر أو أوقف العملية الأخرى.');
  }
});

module.exports = server;
