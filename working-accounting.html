<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة</title>
    <style>
        body {
            font-family: Arial;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #4CAF50, #81C784);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-blue {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        .btn-red {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            box-sizing: border-box;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        .quick-login {
            background: rgba(255,215,0,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            cursor: pointer;
            border: 2px solid rgba(255,215,0,0.5);
            text-align: center;
            transition: all 0.3s;
        }
        .quick-login:hover {
            background: rgba(255,215,0,0.4);
            transform: scale(1.02);
        }
        .hidden {
            display: none;
        }
        .message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .success {
            background: rgba(76,175,80,0.3);
            border: 2px solid #4CAF50;
        }
        .error {
            background: rgba(244,67,54,0.3);
            border: 2px solid #f44336;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }
        .operations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .operation-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .operation-card h3 {
            color: #ffd700;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen">
            <div class="logo">AG</div>
            <h1 style="text-align: center;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2 style="text-align: center;">🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            
            <div id="loginMessage"></div>
            
            <input type="text" id="username" placeholder="👤 اسم المستخدم">
            <input type="password" id="password" placeholder="🔒 كلمة المرور">
            
            <button class="btn" onclick="performLogin()" style="width: 100%;">🚀 دخول النظام</button>
            
            <div class="quick-login" onclick="quickLogin()">
                <strong>🔥 دخول سريع للتجربة</strong><br>
                اضغط هنا للدخول بحساب المدير: admin / admin123
            </div>
            
            <div class="footer">
                <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
                <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
                <p>AG Technology Systems</p>
                <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="hidden">
            <div class="logo">AG</div>
            <h1 style="text-align: center;">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2 style="text-align: center;">🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            
            <div style="text-align: center; margin: 20px 0;">
                <span>مرحباً </span><span id="currentUser" style="color: #ffd700; font-weight: bold;">مدير النظام</span>
                <button class="btn btn-red" onclick="performLogout()">🚪 تسجيل الخروج</button>
            </div>

            <!-- الإحصائيات -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div>المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2.5M</div>
                    <div>المبيعات (جنيه)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">150</div>
                    <div>الأصناف</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">85</div>
                    <div>العملاء</div>
                </div>
            </div>

            <!-- العمليات -->
            <div class="operations">
                <div class="operation-card">
                    <h3>📄 فواتير المبيعات</h3>
                    <p>إنشاء وإدارة فواتير المبيعات</p>
                    <button class="btn" onclick="showSalesInvoice()">📄 إنشاء فاتورة</button>
                    <button class="btn btn-blue" onclick="printSalesInvoice()">🖨️ طباعة</button>
                </div>
                
                <div class="operation-card">
                    <h3>📦 فواتير المشتريات</h3>
                    <p>تسجيل وإدارة فواتير المشتريات</p>
                    <button class="btn" onclick="showPurchaseInvoice()">📦 إدخال فاتورة</button>
                </div>
                
                <div class="operation-card">
                    <h3>📈 التقارير المالية</h3>
                    <p>عرض وطباعة التقارير والقوائم المالية</p>
                    <button class="btn" onclick="showFinancialReports()">📈 عرض التقارير</button>
                    <button class="btn btn-blue" onclick="printFinancialReports()">🖨️ طباعة</button>
                </div>
                
                <div class="operation-card">
                    <h3>🏪 إدارة المخازن</h3>
                    <p>متابعة المخزون وحركة الأصناف</p>
                    <button class="btn" onclick="showInventory()">🏪 عرض المخازن</button>
                </div>
            </div>

            <div class="footer">
                <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
                <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
                <p>AG Technology Systems</p>
                <p style="font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </div>

    <script>
        // دالة الدخول السريع
        function quickLogin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            performLogin();
        }

        // دالة تسجيل الدخول
        function performLogin() {
            var username = document.getElementById('username').value;
            var password = document.getElementById('password').value;
            var messageDiv = document.getElementById('loginMessage');
            
            if (!username || !password) {
                messageDiv.innerHTML = '<div class="message error">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }
            
            if (username === 'admin' && password === 'admin123') {
                messageDiv.innerHTML = '<div class="message success">✅ تم تسجيل الدخول بنجاح...</div>';
                
                setTimeout(function() {
                    document.getElementById('loginScreen').classList.add('hidden');
                    document.getElementById('mainSystem').classList.remove('hidden');
                    
                    alert('🎉 مرحباً بك في نظام المحاسبة!\n\n✅ تم تسجيل الدخول بنجاح\n\n🏢 شركة ايه جي تكنولوجي سيستميز\nAG Technology Systems\n\nيمكنك الآن استخدام جميع وظائف النظام');
                }, 1500);
                
            } else {
                messageDiv.innerHTML = '<div class="message error">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
            }
        }

        // دالة تسجيل الخروج
        function performLogout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('mainSystem').classList.add('hidden');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('loginMessage').innerHTML = '';
            }
        }

        // دوال العمليات
        function showSalesInvoice() {
            alert('📄 فاتورة مبيعات\n\n' +
                  'رقم الفاتورة: INV-' + Date.now() + '\n' +
                  'التاريخ: ' + new Date().toLocaleDateString('ar-EG') + '\n\n' +
                  'بيانات العميل:\n' +
                  'العميل: شركة الأهرام للطباعة والنشر\n' +
                  'العنوان: القاهرة - مصر الجديدة\n\n' +
                  'تفاصيل الفاتورة:\n' +
                  'المنتج: ورق كتابة 80 جرام\n' +
                  'الكمية: 100 طن\n' +
                  'السعر: 25,000 جنيه/طن\n' +
                  'الإجمالي قبل الضريبة: 2,500,000 جنيه\n' +
                  'ضريبة القيمة المضافة (14%): 350,000 جنيه\n' +
                  'الإجمالي شامل الضريبة: 2,850,000 جنيه\n\n' +
                  '✅ تم إنشاء الفاتورة بنجاح');
        }

        function showPurchaseInvoice() {
            alert('📦 فاتورة مشتريات\n\n' +
                  'رقم الفاتورة: PUR-' + Date.now() + '\n' +
                  'التاريخ: ' + new Date().toLocaleDateString('ar-EG') + '\n\n' +
                  'بيانات المورد:\n' +
                  'المورد: شركة المواد الخام المصرية\n' +
                  'العنوان: الإسكندرية - المنطقة الصناعية\n\n' +
                  'تفاصيل الفاتورة:\n' +
                  'الصنف: لب الورق المستورد\n' +
                  'الكمية: 50 طن\n' +
                  'السعر: 15,000 جنيه/طن\n' +
                  'الإجمالي: 750,000 جنيه\n\n' +
                  '✅ تم تسجيل فاتورة المشتريات بنجاح');
        }

        function showFinancialReports() {
            alert('📈 التقارير المالية\n\n' +
                  '⚖️ ميزان المراجعة:\n' +
                  'إجمالي المدين: 12,750,000 جنيه\n' +
                  'إجمالي الدائن: 12,750,000 جنيه\n' +
                  'الميزان متوازن ✅\n\n' +
                  '📋 قائمة الدخل:\n' +
                  'إيرادات المبيعات: 5,500,000 جنيه\n' +
                  'تكلفة البضاعة المباعة: 3,850,000 جنيه\n' +
                  'مجمل الربح: 1,650,000 جنيه\n' +
                  'المصروفات التشغيلية: 900,000 جنيه\n' +
                  'صافي الربح: 658,750 جنيه\n\n' +
                  '📊 قائمة المركز المالي:\n' +
                  'إجمالي الأصول: 11,000,000 جنيه\n' +
                  'إجمالي الخصوم: 2,800,000 جنيه\n' +
                  'حقوق الملكية: 8,200,000 جنيه');
        }

        function showInventory() {
            alert('🏪 أرصدة المخازن\n\n' +
                  'التاريخ: ' + new Date().toLocaleDateString('ar-EG') + '\n\n' +
                  '📦 المنتجات النهائية:\n' +
                  '• ورق كتابة 80 جرام: 500 طن - 12,500,000 جنيه\n' +
                  '• ورق طباعة 70 جرام: 300 طن - 6,900,000 جنيه\n' +
                  '• ورق صحف 45 جرام: 200 طن - 3,600,000 جنيه\n\n' +
                  '🏭 المواد الخام:\n' +
                  '• لب الورق: 150 طن - 2,250,000 جنيه\n' +
                  '• كيماويات: 50 طن - 750,000 جنيه\n\n' +
                  '💰 إجمالي قيمة المخزون: 26,000,000 جنيه');
        }

        function printSalesInvoice() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة مبيعات</title>
                    <style>
                        body { font-family: Arial; direction: rtl; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding: 20px; margin-bottom: 30px; }
                        .content { margin: 20px 0; }
                        .footer { position: fixed; bottom: 0; text-align: center; width: 100%; font-size: 12px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                        <h2>📄 فاتورة مبيعات</h2>
                        <p>رقم الفاتورة: INV-${Date.now()}</p>
                        <p>التاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
                    </div>
                    <div class="content">
                        <h3>بيانات العميل:</h3>
                        <p><strong>العميل:</strong> شركة الأهرام للطباعة والنشر</p>
                        <p><strong>العنوان:</strong> القاهرة - مصر الجديدة</p>
                        <p><strong>الرقم الضريبي:</strong> 123456789</p>
                        
                        <h3>تفاصيل الفاتورة:</h3>
                        <table>
                            <tr><th>المنتج</th><th>الكمية</th><th>الوحدة</th><th>السعر</th><th>الإجمالي</th></tr>
                            <tr><td>ورق كتابة 80 جرام</td><td>100</td><td>طن</td><td>25,000</td><td>2,500,000</td></tr>
                        </table>
                        
                        <div style="text-align: left; margin-top: 20px;">
                            <p><strong>الإجمالي قبل الضريبة: 2,500,000 جنيه</strong></p>
                            <p><strong>ضريبة القيمة المضافة (14%): 350,000 جنيه</strong></p>
                            <p style="font-size: 18px; color: #4CAF50;"><strong>الإجمالي شامل الضريبة: 2,850,000 جنيه</strong></p>
                        </div>
                    </div>
                    <div class="footer">
                        <p><strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems</p>
                        <p>تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة</p>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }

        function printFinancialReports() {
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>ميزان المراجعة</title>
                    <style>
                        body { font-family: Arial; direction: rtl; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding: 20px; margin-bottom: 30px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        .footer { position: fixed; bottom: 0; text-align: center; width: 100%; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                        <h2>⚖️ ميزان المراجعة</h2>
                        <p>التاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
                    </div>
                    <table>
                        <tr><th>رقم الحساب</th><th>اسم الحساب</th><th>مدين</th><th>دائن</th></tr>
                        <tr><td>1000</td><td>الأصول الثابتة</td><td>6,000,000</td><td></td></tr>
                        <tr><td>1100</td><td>الأصول المتداولة</td><td>5,000,000</td><td></td></tr>
                        <tr><td>1200</td><td>المخزون</td><td>1,750,000</td><td></td></tr>
                        <tr><td>2000</td><td>الخصوم المتداولة</td><td></td><td>1,800,000</td></tr>
                        <tr><td>2100</td><td>قروض طويلة الأجل</td><td></td><td>1,000,000</td></tr>
                        <tr><td>3000</td><td>رأس المال</td><td></td><td>4,000,000</td></tr>
                        <tr><td>4000</td><td>إيرادات المبيعات</td><td></td><td>5,500,000</td></tr>
                        <tr><td>5000</td><td>تكلفة البضاعة المباعة</td><td>3,850,000</td><td></td></tr>
                        <tr><td>6000</td><td>مصروفات التشغيل</td><td>900,000</td><td></td></tr>
                        <tr style="font-weight: bold; background-color: #f9f9f9;">
                            <td colspan="2">الإجمالي</td><td>17,500,000</td><td>17,500,000</td>
                        </tr>
                    </table>
                    <div class="footer">
                        <p><strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems</p>
                        <p>تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة</p>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                        }
                    </script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }

        // تفعيل Enter للدخول
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && !document.getElementById('loginScreen').classList.contains('hidden')) {
                performLogin();
            }
        });
    </script>
</body>
</html>
