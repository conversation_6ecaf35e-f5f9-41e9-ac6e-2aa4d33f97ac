import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import arEG from 'antd/locale/ar_EG';
import './App.css';

// Components
import Login from './components/Auth/Login';
import Dashboard from './components/Dashboard/Dashboard';
import Layout from './components/Layout/Layout';

// Pages
import FinancialAccounts from './pages/Financial/FinancialAccounts';
import FinancialVouchers from './pages/Financial/FinancialVouchers';
import Inventory from './pages/Inventory/Inventory';
import Warehouses from './pages/Warehouses/Warehouses';
import Purchasing from './pages/Purchasing/Purchasing';
import Treasury from './pages/Treasury/Treasury';
import PaperWarehouse from './pages/PaperWarehouse/PaperWarehouse';
import Reports from './pages/Reports/Reports';
import Settings from './pages/Settings/Settings';

// Services
import { authService } from './services/authService';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const userData = await authService.getCurrentUser();
        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = (userData, token) => {
    localStorage.setItem('token', token);
    setUser(userData);
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    setUser(null);
    setIsAuthenticated(false);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>جاري تحميل النظام...</p>
        </div>
      </div>
    );
  }

  return (
    <ConfigProvider 
      locale={arEG}
      direction="rtl"
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
          fontFamily: 'Cairo, sans-serif',
        },
      }}
    >
      <Router>
        <div className="App">
          {!isAuthenticated ? (
            <Login onLogin={handleLogin} />
          ) : (
            <Layout user={user} onLogout={handleLogout}>
              <Routes>
                <Route path="/" element={<Dashboard user={user} />} />
                <Route path="/dashboard" element={<Dashboard user={user} />} />
                
                {/* الحسابات المالية */}
                <Route path="/financial/accounts" element={<FinancialAccounts />} />
                <Route path="/financial/vouchers" element={<FinancialVouchers />} />
                
                {/* المخازن */}
                <Route path="/inventory" element={<Inventory />} />
                <Route path="/warehouses" element={<Warehouses />} />
                
                {/* المشتريات */}
                <Route path="/purchasing" element={<Purchasing />} />
                
                {/* الخزينة */}
                <Route path="/treasury" element={<Treasury />} />
                
                {/* مخازن الورق */}
                <Route path="/paper-warehouse" element={<PaperWarehouse />} />
                
                {/* التقارير */}
                <Route path="/reports" element={<Reports />} />
                
                {/* الإعدادات */}
                <Route path="/settings" element={<Settings />} />
                
                {/* إعادة توجيه */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </Layout>
          )}
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App;
