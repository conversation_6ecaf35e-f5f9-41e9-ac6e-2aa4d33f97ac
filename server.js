const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// إعدادات الأمان
app.use(helmet());

// تحديد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100 // حد أقصى 100 طلب لكل IP
});
app.use(limiter);

// إعدادات CORS
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));

// إعدادات تحليل البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// مجلد الملفات الثابتة
app.use('/uploads', express.static('uploads'));

// صفحة ترحيب
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مرحباً بك في نظام المحاسبة والمراجعة الداخلية! 🎉',
    version: '1.0.0',
    features: [
      '📊 الحسابات المالية والتكاليف',
      '🏪 إدارة المخازن',
      '🛒 إدارة المشتريات',
      '💰 إدارة الخزينة',
      '📄 مخازن الورق',
      '📈 التقارير والمراجعة'
    ],
    next_steps: [
      '1. قم بإعداد قاعدة البيانات: npm run setup',
      '2. سجل دخول باستخدام: admin / admin123',
      '3. استخدم API endpoints للتفاعل مع النظام'
    ]
  });
});

// معلومات النظام
app.get('/api/info', (req, res) => {
  res.json({
    success: true,
    system: {
      name: 'نظام المحاسبة والمراجعة الداخلية',
      version: '1.0.0',
      author: 'Ashraf',
      description: 'نظام محاسبي شامل يغطي جميع جوانب المراجعة الداخلية'
    },
    modules: {
      financial: 'الحسابات المالية والتكاليف',
      inventory: 'إدارة المخازن',
      purchasing: 'إدارة المشتريات',
      treasury: 'إدارة الخزينة',
      paper_warehouse: 'مخازن الورق',
      reports: 'التقارير والمراجعة'
    }
  });
});

// اختبار الاتصال
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'النظام يعمل بشكل طبيعي'
  });
});

// مسارات API الرئيسية (سيتم تفعيلها لاحقاً)
try {
  app.use('/api/auth', require('./routes/auth'));
  console.log('✅ تم تحميل مسار المصادقة');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار المصادقة:', error.message);
}

try {
  app.use('/api/financial', require('./routes/financial'));
  console.log('✅ تم تحميل مسار الحسابات المالية');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار الحسابات المالية:', error.message);
}

try {
  app.use('/api/inventory', require('./routes/inventory'));
  console.log('✅ تم تحميل مسار المخازن');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار المخازن:', error.message);
}

try {
  app.use('/api/warehouses', require('./routes/warehouses'));
  console.log('✅ تم تحميل مسار إدارة المخازن');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار إدارة المخازن:', error.message);
}

try {
  app.use('/api/purchasing', require('./routes/purchasing'));
  console.log('✅ تم تحميل مسار المشتريات');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار المشتريات:', error.message);
}

try {
  app.use('/api/treasury', require('./routes/treasury'));
  console.log('✅ تم تحميل مسار الخزينة');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار الخزينة:', error.message);
}

try {
  app.use('/api/paper-warehouse', require('./routes/paperWarehouse'));
  console.log('✅ تم تحميل مسار مخازن الورق');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار مخازن الورق:', error.message);
}

try {
  app.use('/api/reports', require('./routes/reports'));
  console.log('✅ تم تحميل مسار التقارير');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار التقارير:', error.message);
}

try {
  app.use('/api/export', require('./routes/export'));
  console.log('✅ تم تحميل مسار التصدير');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار التصدير:', error.message);
}

try {
  app.use('/api/import', require('./routes/import'));
  console.log('✅ تم تحميل مسار الاستيراد');
} catch (error) {
  console.log('⚠️ لم يتم تحميل مسار الاستيراد:', error.message);
}

// خدمة الملفات الثابتة للإنتاج
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'client/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.stack);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// معالج الصفحات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'الصفحة المطلوبة غير موجودة'
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
});

module.exports = app;
