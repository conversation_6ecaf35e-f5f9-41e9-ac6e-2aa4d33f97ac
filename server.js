const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// إعدادات الأمان
app.use(helmet());

// تحديد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100 // حد أقصى 100 طلب لكل IP
});
app.use(limiter);

// إعدادات CORS
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));

// إعدادات تحليل البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// مجلد الملفات الثابتة
app.use('/uploads', express.static('uploads'));

// مسارات API الرئيسية
app.use('/api/auth', require('./routes/auth'));
app.use('/api/financial', require('./routes/financial'));
app.use('/api/inventory', require('./routes/inventory'));
app.use('/api/warehouses', require('./routes/warehouses'));
app.use('/api/purchasing', require('./routes/purchasing'));
app.use('/api/treasury', require('./routes/treasury'));
app.use('/api/paper-warehouse', require('./routes/paperWarehouse'));
app.use('/api/reports', require('./routes/reports'));

// خدمة الملفات الثابتة للإنتاج
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'client/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.stack);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// معالج الصفحات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'الصفحة المطلوبة غير موجودة'
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
});

module.exports = app;
