<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة والمراجعة الداخلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status {
            background: rgba(0,255,0,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid rgba(0,255,0,0.5);
            text-align: center;
        }
        
        .login-section {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
            backdrop-filter: blur(15px);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
        }
        
        .form-group input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        
        .btn {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,215,0,0.4);
        }
        
        .api-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .api-card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .api-card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .api-link {
            display: block;
            background: rgba(255,255,255,0.1);
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .api-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-card h4 {
            color: #ffd700;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .response-area {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .error {
            background: rgba(255,0,0,0.2);
            border: 2px solid rgba(255,0,0,0.5);
        }
        
        .success {
            background: rgba(0,255,0,0.2);
            border: 2px solid rgba(0,255,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 نظام المحاسبة والمراجعة الداخلية</h1>
            <p>واجهة تفاعلية للنظام المحاسبي الشامل</p>
            
            <div class="status">
                <h3>✅ النظام متصل ويعمل بنجاح!</h3>
                <p>الخادم: http://localhost:5000 | الحالة: متصل</p>
            </div>
        </div>

        <!-- قسم تسجيل الدخول -->
        <div class="login-section">
            <h2>🔐 تسجيل الدخول</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" placeholder="أدخل كلمة المرور">
                </div>
                <button type="submit" class="btn">تسجيل الدخول</button>
            </form>
            <div id="loginResponse" class="response-area" style="display: none;"></div>
        </div>

        <!-- قسم اختبار API -->
        <div class="api-section">
            <div class="api-card">
                <h3>📊 معلومات النظام</h3>
                <a href="#" class="api-link" onclick="testAPI('/api/info')">📋 معلومات النظام</a>
                <a href="#" class="api-link" onclick="testAPI('/api/health')">💚 حالة النظام</a>
                <a href="#" class="api-link" onclick="testAPI('/api/auth/users')">👥 قائمة المستخدمين</a>
            </div>
            
            <div class="api-card">
                <h3>💰 الحسابات المالية</h3>
                <a href="#" class="api-link" onclick="testAPI('/api/financial/accounts')">📈 الحسابات المالية</a>
                <a href="#" class="api-link" onclick="testAPI('/api/financial/vouchers')">📄 أذونات الصرف</a>
                <a href="#" class="api-link" onclick="testAPI('/api/reports/trial-balance')">⚖️ ميزان المراجعة</a>
            </div>
            
            <div class="api-card">
                <h3>🏪 المخازن والمشتريات</h3>
                <a href="#" class="api-link" onclick="testAPI('/api/inventory/items')">📦 الأصناف</a>
                <a href="#" class="api-link" onclick="testAPI('/api/inventory/balances')">📊 أرصدة المخازن</a>
                <a href="#" class="api-link" onclick="testAPI('/api/purchasing/suppliers')">🏢 الموردين</a>
            </div>
        </div>

        <!-- منطقة عرض النتائج -->
        <div id="apiResponse" class="response-area" style="display: none;"></div>

        <!-- مميزات النظام -->
        <div class="features-grid">
            <div class="feature-card">
                <h4>📊 الحسابات المالية</h4>
                <p>شجرة حسابات + أذونات + قيود محاسبية</p>
            </div>
            <div class="feature-card">
                <h4>🏪 إدارة المخازن</h4>
                <p>6 أنواع مخازن + بطاقات أصناف + جرد</p>
            </div>
            <div class="feature-card">
                <h4>🛒 إدارة المشتريات</h4>
                <p>طلبات شراء + موردين + أوامر توريد</p>
            </div>
            <div class="feature-card">
                <h4>💰 إدارة الخزينة</h4>
                <p>خزائن منفصلة + حركات + شيكات</p>
            </div>
            <div class="feature-card">
                <h4>📄 مخازن الورق</h4>
                <p>إنتاج يومي + شحنات + أوزان</p>
            </div>
            <div class="feature-card">
                <h4>📈 التقارير</h4>
                <p>تقارير شاملة + تصدير PDF + استيراد Excel</p>
            </div>
        </div>
    </div>

    <script>
        let authToken = '';

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const responseDiv = document.getElementById('loginResponse');
            
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                responseDiv.style.display = 'block';
                responseDiv.className = 'response-area ' + (data.success ? 'success' : 'error');
                responseDiv.innerHTML = `
                    <h4>${data.success ? '✅ نجح تسجيل الدخول!' : '❌ فشل تسجيل الدخول!'}</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.success && data.data && data.data.token) {
                    authToken = data.data.token;
                    alert('✅ تم تسجيل الدخول بنجاح! يمكنك الآن استخدام API');
                }
                
            } catch (error) {
                responseDiv.style.display = 'block';
                responseDiv.className = 'response-area error';
                responseDiv.innerHTML = `
                    <h4>❌ خطأ في الاتصال!</h4>
                    <p>تأكد من تشغيل الخادم على http://localhost:5000</p>
                    <pre>${error.message}</pre>
                `;
            }
        });

        // اختبار API
        async function testAPI(endpoint) {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'block';
            responseDiv.className = 'response-area';
            responseDiv.innerHTML = '<h4>⏳ جاري التحميل...</h4>';
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (authToken) {
                    headers['Authorization'] = `Bearer ${authToken}`;
                }
                
                const response = await fetch(`http://localhost:5000${endpoint}`, {
                    method: 'GET',
                    headers: headers
                });
                
                const data = await response.json();
                
                responseDiv.className = 'response-area ' + (response.ok ? 'success' : 'error');
                responseDiv.innerHTML = `
                    <h4>${response.ok ? '✅' : '❌'} ${endpoint}</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                responseDiv.className = 'response-area error';
                responseDiv.innerHTML = `
                    <h4>❌ خطأ في الاتصال!</h4>
                    <p>تأكد من تشغيل الخادم على http://localhost:5000</p>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // اختبار الاتصال عند تحميل الصفحة
        window.addEventListener('load', () => {
            testAPI('/api/health');
        });
    </script>
</body>
</html>
