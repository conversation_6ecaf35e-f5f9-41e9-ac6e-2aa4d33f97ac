{"name": "accounting-system", "version": "1.0.0", "description": "نظام المحاسبة والمراجعة الداخلية الشامل", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "client": "cd client && npm start", "build": "cd client && npm run build", "install-client": "cd client && npm install", "heroku-postbuild": "npm run install-client && npm run build"}, "keywords": ["accounting", "محاسبة", "مراجعة داخلية", "مخازن", "خزينة"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}