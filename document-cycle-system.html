<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>نظام الدورة المستندية</title>
<style>
body {
    font-family: Arial;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    direction: rtl;
    margin: 0;
}
.container {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(255,255,255,0.1);
    padding: 30px;
    border-radius: 15px;
}
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
}
.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #4CAF50, #81C784);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    margin: 0 auto 20px;
}
button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 5px;
    transition: all 0.3s;
}
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.btn-blue { background: linear-gradient(45deg, #2196F3, #1976D2); }
.btn-red { background: linear-gradient(45deg, #f44336, #d32f2f); }
.btn-orange { background: linear-gradient(45deg, #FF9800, #F57C00); }
input, select {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    box-sizing: border-box;
    background: rgba(255,255,255,0.9);
    color: #333;
}
.hidden { display: none; }
.quick {
    background: rgba(255,215,0,0.3);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    cursor: pointer;
    border: 2px solid rgba(255,215,0,0.7);
    text-align: center;
    font-weight: bold;
}
.quick:hover {
    background: rgba(255,215,0,0.5);
    transform: scale(1.02);
}
.cycle-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 20px 0;
}
.cycle-card {
    background: rgba(255,255,255,0.15);
    padding: 25px;
    border-radius: 15px;
    border: 2px solid rgba(255,255,255,0.3);
}
.cycle-card h3 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 20px;
    text-align: center;
}
.step {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}
.step.completed {
    background: rgba(76,175,80,0.2);
    border-left-color: #4CAF50;
}
.step.current {
    background: rgba(255,193,7,0.2);
    border-left-color: #FFC107;
}
.step-number {
    display: inline-block;
    background: #4CAF50;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    text-align: center;
    line-height: 25px;
    margin-left: 10px;
    font-weight: bold;
}
.form-section {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}
.footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    border-top: 2px solid rgba(255,255,255,0.3);
}
</style>
</head>
<body>

<div class="container">
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen">
        <div class="header">
            <div class="logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>📋 نظام الدورة المستندية الكاملة</h2>
        </div>
        
        <div id="loginMessage"></div>
        
        <input type="text" id="username" placeholder="👤 اسم المستخدم">
        <input type="password" id="password" placeholder="🔒 كلمة المرور">
        
        <button onclick="login()" style="width: 100%; font-size: 18px;">🚀 دخول النظام</button>
        
        <div class="quick" onclick="quickLogin()">
            🔥 دخول سريع للتجربة 🔥<br>
            اضغط هنا للدخول: admin / admin123
        </div>
        
        <div class="footer">
            <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
            <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
            <p>AG Technology Systems</p>
            <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- النظام الرئيسي -->
    <div id="mainSystem" class="hidden">
        <div class="header">
            <div class="logo">AG</div>
            <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
            <h2>📋 نظام الدورة المستندية الكاملة</h2>
            <p>مرحباً <span id="currentUser" style="color: #ffd700;">مدير النظام</span> | 
               <button class="btn-red" onclick="logout()">🚪 خروج</button></p>
        </div>

        <!-- اختيار نوع الدورة -->
        <div id="cycleSelection">
            <h3 style="text-align: center; color: #ffd700; margin: 30px 0;">اختر نوع الدورة المستندية:</h3>
            
            <div class="cycle-container">
                <div class="cycle-card">
                    <h3>📄 دورة مبيعات الورق</h3>
                    <p style="text-align: center; margin: 15px 0;">الدورة المستندية الكاملة لمبيعات الورق (19 خطوة)</p>
                    <button onclick="startSalesCycle()" style="width: 100%;">🚀 بدء دورة المبيعات</button>
                </div>
                
                <div class="cycle-card">
                    <h3>📦 دورة البضاعة الواردة</h3>
                    <p style="text-align: center; margin: 15px 0;">دورة استلام وفحص البضاعة الواردة (7 خطوات)</p>
                    <button onclick="startIncomingCycle()" style="width: 100%;">🚀 بدء دورة الوارد</button>
                </div>
            </div>
        </div>

        <!-- دورة المبيعات -->
        <div id="salesCycle" class="hidden">
            <h3 style="text-align: center; color: #ffd700;">📄 دورة مبيعات الورق (19 خطوة)</h3>
            <button class="btn-blue" onclick="showCycleSelection()">🔙 العودة للقائمة الرئيسية</button>
            
            <div id="salesSteps">
                <!-- سيتم إضافة الخطوات هنا بواسطة JavaScript -->
            </div>
            
            <div class="form-section">
                <h4 style="color: #ffd700;">📝 بيانات العملية:</h4>
                <input type="text" id="customerName" placeholder="اسم العميل">
                <select id="paperType">
                    <option value="">اختر نوع الورق</option>
                    <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                    <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                    <option value="ورق صحف 45 جرام">ورق صحف 45 جرام</option>
                </select>
                <input type="number" id="quantity" placeholder="الكمية (طن)">
                <input type="number" id="unitPrice" placeholder="سعر الطن (جنيه)">
                
                <button onclick="nextSalesStep()" style="width: 100%;">⏭️ الخطوة التالية</button>
                <button class="btn-orange" onclick="resetSalesCycle()">🔄 إعادة تشغيل الدورة</button>
            </div>
        </div>

        <!-- دورة البضاعة الواردة -->
        <div id="incomingCycle" class="hidden">
            <h3 style="text-align: center; color: #ffd700;">📦 دورة البضاعة الواردة (7 خطوات)</h3>
            <button class="btn-blue" onclick="showCycleSelection()">🔙 العودة للقائمة الرئيسية</button>
            
            <div id="incomingSteps">
                <!-- سيتم إضافة الخطوات هنا بواسطة JavaScript -->
            </div>
            
            <div class="form-section">
                <h4 style="color: #ffd700;">📝 بيانات الشحنة:</h4>
                <input type="text" id="supplierName" placeholder="اسم المورد">
                <select id="materialType">
                    <option value="">اختر نوع المادة</option>
                    <option value="لب الورق">لب الورق</option>
                    <option value="كيماويات">كيماويات</option>
                    <option value="وقود ديزل">وقود ديزل</option>
                    <option value="قطع غيار">قطع غيار</option>
                </select>
                <input type="number" id="receivedQuantity" placeholder="الكمية المستلمة">
                <input type="text" id="invoiceNumber" placeholder="رقم فاتورة المورد">
                
                <button onclick="nextIncomingStep()" style="width: 100%;">⏭️ الخطوة التالية</button>
                <button class="btn-orange" onclick="resetIncomingCycle()">🔄 إعادة تشغيل الدورة</button>
            </div>
        </div>

        <div class="footer">
            <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
            <p><strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong></p>
            <p>AG Technology Systems</p>
            <p style="font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
            <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>
</div>

<script>
// متغيرات النظام
let currentSalesStep = 0;
let currentIncomingStep = 0;

// خطوات دورة المبيعات (19 خطوة)
const salesSteps = [
    "استلام طلب العميل وفحص الجدية",
    "فحص الرصيد الائتماني للعميل",
    "التأكد من توفر المخزون",
    "إعداد عرض السعر وإرساله للعميل",
    "استلام موافقة العميل على العرض",
    "إعداد أمر البيع الداخلي",
    "اعتماد أمر البيع من الإدارة",
    "إعداد أذن صرف من المخازن",
    "فحص جودة الورق قبل التسليم",
    "إعداد فاتورة المبيعات",
    "مراجعة الفاتورة من المحاسبة",
    "اعتماد الفاتورة من الإدارة المالية",
    "إعداد أذن التسليم",
    "تحميل البضاعة وإخراجها",
    "استلام إيصال استلام من العميل",
    "تسجيل القيد المحاسبي",
    "إرسال الفاتورة للعميل",
    "متابعة التحصيل",
    "إقفال العملية وأرشفة المستندات"
];

// خطوات دورة البضاعة الواردة (7 خطوات)
const incomingSteps = [
    "استلام البضاعة من المورد مع المستندات",
    "فحص مطابقة البضاعة لأمر الشراء",
    "فحص جودة ومواصفات البضاعة",
    "إعداد محضر استلام البضاعة",
    "إدخال البضاعة للمخازن",
    "تسجيل القيد المحاسبي",
    "أرشفة المستندات وإقفال العملية"
];

// دوال تسجيل الدخول
function quickLogin() {
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin123';
    login();
}

function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const messageDiv = document.getElementById('loginMessage');
    
    if (!username || !password) {
        messageDiv.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
        return;
    }
    
    if (username === 'admin' && password === 'admin123') {
        messageDiv.innerHTML = '<div style="color: green; text-align: center; padding: 10px;">✅ تم تسجيل الدخول بنجاح...</div>';
        
        setTimeout(function() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainSystem').classList.remove('hidden');
            alert('🎉 مرحباً بك في نظام الدورة المستندية!\n\nيمكنك الآن تنفيذ:\n• دورة مبيعات الورق (19 خطوة)\n• دورة البضاعة الواردة (7 خطوات)\n\n🏢 شركة ايه جي تكنولوجي سيستميز');
        }, 1500);
        
    } else {
        messageDiv.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
    }
}

function logout() {
    if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('mainSystem').classList.add('hidden');
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('loginMessage').innerHTML = '';
        showCycleSelection();
    }
}

// دوال التنقل
function showCycleSelection() {
    document.getElementById('cycleSelection').classList.remove('hidden');
    document.getElementById('salesCycle').classList.add('hidden');
    document.getElementById('incomingCycle').classList.add('hidden');
}

function startSalesCycle() {
    document.getElementById('cycleSelection').classList.add('hidden');
    document.getElementById('salesCycle').classList.remove('hidden');
    resetSalesCycle();
}

function startIncomingCycle() {
    document.getElementById('cycleSelection').classList.add('hidden');
    document.getElementById('incomingCycle').classList.remove('hidden');
    resetIncomingCycle();
}

// دوال دورة المبيعات
function resetSalesCycle() {
    currentSalesStep = 0;
    displaySalesSteps();
}

function displaySalesSteps() {
    const container = document.getElementById('salesSteps');
    container.innerHTML = '';

    salesSteps.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = 'step';

        if (index < currentSalesStep) {
            stepDiv.classList.add('completed');
        } else if (index === currentSalesStep) {
            stepDiv.classList.add('current');
        }

        stepDiv.innerHTML = `
            <span class="step-number">${index + 1}</span>
            <strong>${step}</strong>
            ${index === currentSalesStep ? ' ← الخطوة الحالية' : ''}
            ${index < currentSalesStep ? ' ✅' : ''}
        `;

        container.appendChild(stepDiv);
    });
}

function nextSalesStep() {
    if (currentSalesStep === 0) {
        // التحقق من البيانات المطلوبة
        const customer = document.getElementById('customerName').value;
        const paperType = document.getElementById('paperType').value;
        const quantity = document.getElementById('quantity').value;
        const price = document.getElementById('unitPrice').value;

        if (!customer || !paperType || !quantity || !price) {
            alert('❌ يرجى إدخال جميع البيانات المطلوبة قبل بدء الدورة');
            return;
        }
    }

    if (currentSalesStep < salesSteps.length) {
        // تنفيذ الخطوة الحالية
        executeSalesStep(currentSalesStep);
        currentSalesStep++;
        displaySalesSteps();

        if (currentSalesStep >= salesSteps.length) {
            alert('🎉 تم إكمال دورة المبيعات بنجاح!\n\nتم تنفيذ جميع الخطوات الـ 19 للدورة المستندية\n\n✅ العملية مكتملة وجاهزة للأرشفة');
        }
    }
}

function executeSalesStep(stepIndex) {
    const customer = document.getElementById('customerName').value || 'شركة الأهرام للطباعة';
    const paperType = document.getElementById('paperType').value || 'ورق كتابة 80 جرام';
    const quantity = document.getElementById('quantity').value || '100';
    const price = document.getElementById('unitPrice').value || '25000';
    const total = quantity * price;

    const stepMessages = [
        `📞 تم استلام طلب من ${customer}\nنوع الورق: ${paperType}\nالكمية: ${quantity} طن`,
        `💳 تم فحص الرصيد الائتماني للعميل\nالحد الائتماني: ${(total * 1.2).toLocaleString()} جنيه\nالرصيد المتاح: ${(total * 0.8).toLocaleString()} جنيه\n✅ العميل مؤهل ائتمانياً`,
        `📦 تم فحص المخزون\nالمتوفر: ${quantity * 2} طن\nالمطلوب: ${quantity} طن\n✅ المخزون متوفر`,
        `💰 تم إعداد عرض السعر\nالسعر: ${price.toLocaleString()} جنيه/طن\nالإجمالي: ${total.toLocaleString()} جنيه\n📧 تم إرسال العرض للعميل`,
        `✅ تم استلام موافقة العميل على العرض\nرقم الموافقة: APP-${Date.now()}`,
        `📋 تم إعداد أمر البيع الداخلي\nرقم الأمر: SO-${Date.now()}\nالتاريخ: ${new Date().toLocaleDateString()}`,
        `👨‍💼 تم اعتماد أمر البيع من الإدارة\nالمعتمد: مدير المبيعات\nالتوقيت: ${new Date().toLocaleTimeString()}`,
        `📤 تم إعداد أذن صرف من المخازن\nرقم الأذن: ISS-${Date.now()}\nالمخزن: المخزن الرئيسي`,
        `🔍 تم فحص جودة الورق\nنتيجة الفحص: مطابق للمواصفات\nالفاحص: مراقب الجودة`,
        `🧾 تم إعداد فاتورة المبيعات\nرقم الفاتورة: INV-${Date.now()}\nالمبلغ: ${total.toLocaleString()} جنيه`,
        `📊 تم مراجعة الفاتورة من المحاسبة\nالمراجع: المحاسب الرئيسي\n✅ الفاتورة صحيحة`,
        `💼 تم اعتماد الفاتورة من الإدارة المالية\nالمعتمد: المدير المالي\nالتوقيت: ${new Date().toLocaleTimeString()}`,
        `📋 تم إعداد أذن التسليم\nرقم الأذن: DEL-${Date.now()}\nموعد التسليم: ${new Date().toLocaleDateString()}`,
        `🚛 تم تحميل البضاعة وإخراجها\nرقم الشاحنة: TR-${Math.floor(Math.random() * 1000)}\nالسائق: أحمد محمد`,
        `📝 تم استلام إيصال استلام من العميل\nرقم الإيصال: REC-${Date.now()}\nالمستلم: ${customer}`,
        `📚 تم تسجيل القيد المحاسبي\nمن ح/ العملاء ${total.toLocaleString()}\nإلى ح/ المبيعات ${total.toLocaleString()}`,
        `📧 تم إرسال الفاتورة للعميل\nطريقة الإرسال: بريد إلكتروني + بريد عادي`,
        `💰 بدء متابعة التحصيل\nتاريخ الاستحقاق: ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}\nالمبلغ: ${total.toLocaleString()} جنيه`,
        `📁 تم إقفال العملية وأرشفة المستندات\nرقم الملف: FILE-${Date.now()}\n✅ العملية مكتملة`
    ];

    alert(`الخطوة ${stepIndex + 1}: ${salesSteps[stepIndex]}\n\n${stepMessages[stepIndex]}`);
}

// دوال دورة البضاعة الواردة
function resetIncomingCycle() {
    currentIncomingStep = 0;
    displayIncomingSteps();
}

function displayIncomingSteps() {
    const container = document.getElementById('incomingSteps');
    container.innerHTML = '';

    incomingSteps.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = 'step';

        if (index < currentIncomingStep) {
            stepDiv.classList.add('completed');
        } else if (index === currentIncomingStep) {
            stepDiv.classList.add('current');
        }

        stepDiv.innerHTML = `
            <span class="step-number">${index + 1}</span>
            <strong>${step}</strong>
            ${index === currentIncomingStep ? ' ← الخطوة الحالية' : ''}
            ${index < currentIncomingStep ? ' ✅' : ''}
        `;

        container.appendChild(stepDiv);
    });
}

function nextIncomingStep() {
    if (currentIncomingStep === 0) {
        // التحقق من البيانات المطلوبة
        const supplier = document.getElementById('supplierName').value;
        const material = document.getElementById('materialType').value;
        const quantity = document.getElementById('receivedQuantity').value;
        const invoice = document.getElementById('invoiceNumber').value;

        if (!supplier || !material || !quantity || !invoice) {
            alert('❌ يرجى إدخال جميع البيانات المطلوبة قبل بدء الدورة');
            return;
        }
    }

    if (currentIncomingStep < incomingSteps.length) {
        // تنفيذ الخطوة الحالية
        executeIncomingStep(currentIncomingStep);
        currentIncomingStep++;
        displayIncomingSteps();

        if (currentIncomingStep >= incomingSteps.length) {
            alert('🎉 تم إكمال دورة البضاعة الواردة بنجاح!\n\nتم تنفيذ جميع الخطوات الـ 7 للدورة المستندية\n\n✅ العملية مكتملة وجاهزة للأرشفة');
        }
    }
}

function executeIncomingStep(stepIndex) {
    const supplier = document.getElementById('supplierName').value || 'شركة المواد الخام المصرية';
    const material = document.getElementById('materialType').value || 'لب الورق';
    const quantity = document.getElementById('receivedQuantity').value || '50';
    const invoice = document.getElementById('invoiceNumber').value || 'PUR-12345';

    const stepMessages = [
        `📦 تم استلام الشحنة من ${supplier}\nنوع المادة: ${material}\nالكمية: ${quantity} طن\nرقم الفاتورة: ${invoice}\nالسائق: محمد أحمد\nرقم الشاحنة: ${Math.floor(Math.random() * 1000)}`,
        `📋 تم فحص مطابقة البضاعة لأمر الشراء\nرقم أمر الشراء: PO-${Date.now()}\nالكمية المطلوبة: ${quantity} طن\nالكمية المستلمة: ${quantity} طن\n✅ البضاعة مطابقة`,
        `🔍 تم فحص جودة ومواصفات البضاعة\nالفاحص: مراقب الجودة\nنتيجة الفحص: مطابق للمواصفات\nدرجة الجودة: A+\n✅ البضاعة مقبولة`,
        `📝 تم إعداد محضر استلام البضاعة\nرقم المحضر: REC-${Date.now()}\nالتاريخ: ${new Date().toLocaleDateString()}\nالتوقيت: ${new Date().toLocaleTimeString()}\nالمستلم: أمين المخزن`,
        `🏪 تم إدخال البضاعة للمخازن\nالمخزن: مخزن المواد الخام\nالموقع: الرف رقم ${Math.floor(Math.random() * 50) + 1}\nحالة التخزين: مناسبة\n✅ تم التخزين بنجاح`,
        `📚 تم تسجيل القيد المحاسبي\nمن ح/ المخزون ${(quantity * 15000).toLocaleString()}\nإلى ح/ الموردين ${(quantity * 15000).toLocaleString()}\nالمحاسب: المحاسب الرئيسي`,
        `📁 تم أرشفة المستندات وإقفال العملية\nرقم الملف: FILE-IN-${Date.now()}\nالمستندات: فاتورة المورد، محضر الاستلام، أذن الإدخال\n✅ العملية مكتملة`
    ];

    alert(`الخطوة ${stepIndex + 1}: ${incomingSteps[stepIndex]}\n\n${stepMessages[stepIndex]}`);
}

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    displaySalesSteps();
    displayIncomingSteps();
});
</script>

</body>
</html>
