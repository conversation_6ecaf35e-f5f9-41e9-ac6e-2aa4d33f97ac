'use strict';Object.defineProperty(exports, "__esModule", { value: true });exports['default'] =

importDeclaration;var _contextCompat = require('eslint-module-utils/contextCompat');function importDeclaration(context, node) {
  var ancestors = (0, _contextCompat.getAncestors)(context, node);
  return ancestors[ancestors.length - 1];
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uL3NyYy9pbXBvcnREZWNsYXJhdGlvbi5qcyJdLCJuYW1lcyI6WyJpbXBvcnREZWNsYXJhdGlvbiIsImNvbnRleHQiLCJub2RlIiwiYW5jZXN0b3JzIiwibGVuZ3RoIl0sIm1hcHBpbmdzIjoiOztBQUV3QkEsaUIsQ0FGeEIsa0VBRWUsU0FBU0EsaUJBQVQsQ0FBMkJDLE9BQTNCLEVBQW9DQyxJQUFwQyxFQUEwQztBQUN2RCxNQUFNQyxZQUFZLGlDQUFhRixPQUFiLEVBQXNCQyxJQUF0QixDQUFsQjtBQUNBLFNBQU9DLFVBQVVBLFVBQVVDLE1BQVYsR0FBbUIsQ0FBN0IsQ0FBUDtBQUNEIiwiZmlsZSI6ImltcG9ydERlY2xhcmF0aW9uLmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0QW5jZXN0b3JzIH0gZnJvbSAnZXNsaW50LW1vZHVsZS11dGlscy9jb250ZXh0Q29tcGF0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaW1wb3J0RGVjbGFyYXRpb24oY29udGV4dCwgbm9kZSkge1xuICBjb25zdCBhbmNlc3RvcnMgPSBnZXRBbmNlc3RvcnMoY29udGV4dCwgbm9kZSk7XG4gIHJldHVybiBhbmNlc3RvcnNbYW5jZXN0b3JzLmxlbmd0aCAtIDFdO1xufVxuIl19