<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="نظام المحاسبة والمراجعة الداخلية الشامل" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>نظام المحاسبة والمراجعة الداخلية</title>
    
    <style>
      * {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
      }
      
      body {
        margin: 0;
        direction: rtl;
        text-align: right;
      }
      
      .ant-layout {
        direction: rtl;
      }
      
      .ant-menu {
        direction: rtl;
      }
      
      .ant-table {
        direction: rtl;
      }
      
      .ant-form {
        direction: rtl;
      }
      
      /* تخصيص الألوان */
      :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #faad14;
        --error-color: #ff4d4f;
        --text-color: #262626;
        --border-color: #d9d9d9;
        --background-color: #f0f2f5;
      }
    </style>
  </head>
  <body>
    <noscript>تحتاج إلى تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    <div id="root"></div>
  </body>
</html>
