"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _BugOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/BugOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var BugOutlined = function BugOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _BugOutlined.default
  }));
};

/**![bug](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwNCAyODBoNTZjNC40IDAgOC0zLjYgOC04IDAtMjguMyA1LjktNTMuMiAxNy4xLTczLjUgMTAuNi0xOS40IDI2LTM0LjggNDUuNC00NS40QzQ1MC45IDE0MiA0NzUuNyAxMzYgNTA0IDEzNmgxNmMyOC4zIDAgNTMuMiA1LjkgNzMuNSAxNy4xIDE5LjQgMTAuNiAzNC44IDI2IDQ1LjQgNDUuNEM2NTAgMjE4LjkgNjU2IDI0My43IDY1NiAyNzJjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOCAwLTQwLTguOC03Ni43LTI1LjktMTA4LjFhMTg0LjMxIDE4NC4zMSAwIDAwLTc0LTc0QzU5Ni43IDcyLjggNTYwIDY0IDUyMCA2NGgtMTZjLTQwIDAtNzYuNyA4LjgtMTA4LjEgMjUuOWExODQuMzEgMTg0LjMxIDAgMDAtNzQgNzRDMzA0LjggMTk1LjMgMjk2IDIzMiAyOTYgMjcyYzAgNC40IDMuNiA4IDggOHoiIC8+PHBhdGggZD0iTTk0MCA1MTJINzkyVjQxMmM3Ni44IDAgMTM5LTYyLjIgMTM5LTEzOSAwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDhhNjMgNjMgMCAwMS02MyA2M0gyMzJhNjMgNjMgMCAwMS02My02M2MwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDggMCA3Ni44IDYyLjIgMTM5IDEzOSAxMzl2MTAwSDg0Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDE0OHY5NmMwIDYuNS4yIDEzIC43IDE5LjNDMTY0LjEgNzI4LjYgMTE2IDc5Ni43IDExNiA4NzZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOCAwLTQ0LjIgMjMuOS04Mi45IDU5LjYtMTAzLjdhMjczIDI3MyAwIDAwMjIuNyA0OWMyNC4zIDQxLjUgNTkgNzYuMiAxMDAuNSAxMDAuNVM0NjAuNSA5NjAgNTEyIDk2MHM5OS44LTEzLjkgMTQxLjMtMzguMmEyODEuMzggMjgxLjM4IDAgMDAxMjMuMi0xNDkuNUExMjAgMTIwIDAgMDE4MzYgODc2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTggMC03OS4zLTQ4LjEtMTQ3LjQtMTE2LjctMTc2LjcuNC02LjQuNy0xMi44LjctMTkuM3YtOTZoMTQ4YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04ek03MTYgNjgwYzAgMzYuOC05LjcgNzItMjcuOCAxMDIuOS0xNy43IDMwLjMtNDMgNTUuNi03My4zIDczLjNDNTg0IDg3NC4zIDU0OC44IDg4NCA1MTIgODg0cy03Mi05LjctMTAyLjktMjcuOGMtMzAuMy0xNy43LTU1LjYtNDMtNzMuMy03My4zQTIwMi43NSAyMDIuNzUgMCAwMTMwOCA2ODBWNDEyaDQwOHYyNjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BugOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BugOutlined';
}
var _default = exports.default = RefIcon;