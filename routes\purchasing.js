const express = require('express');
const { executeQuery } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('purchasing', 'audit'));

// الحصول على جميع الموردين
router.get('/suppliers', async (req, res) => {
  try {
    const { category, active_only = 'true' } = req.query;
    
    let whereClause = '';
    let params = [];

    if (category) {
      whereClause += 'WHERE supplier_category = ?';
      params.push(category);
    }

    if (active_only === 'true') {
      whereClause += whereClause ? ' AND is_active = TRUE' : 'WHERE is_active = TRUE';
    }

    const suppliers = await executeQuery(`
      SELECT * FROM suppliers 
      ${whereClause}
      ORDER BY supplier_name
    `, params);

    res.json({
      success: true,
      data: { suppliers }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الموردين:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء مورد جديد
router.post('/suppliers', auditLog('INSERT', 'suppliers'), async (req, res) => {
  try {
    const { 
      supplier_code, 
      supplier_name, 
      contact_person, 
      phone, 
      email, 
      address, 
      supplier_category, 
      payment_terms 
    } = req.body;

    if (!supplier_code || !supplier_name) {
      return res.status(400).json({
        success: false,
        message: 'رقم المورد واسم المورد مطلوبان'
      });
    }

    // التحقق من عدم تكرار رقم المورد
    const existingSupplier = await executeQuery(
      'SELECT id FROM suppliers WHERE supplier_code = ?',
      [supplier_code]
    );

    if (existingSupplier.length) {
      return res.status(400).json({
        success: false,
        message: 'رقم المورد موجود بالفعل'
      });
    }

    const result = await executeQuery(
      `INSERT INTO suppliers 
       (supplier_code, supplier_name, contact_person, phone, email, address, supplier_category, payment_terms) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [supplier_code, supplier_name, contact_person, phone, email, address, supplier_category, payment_terms]
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المورد بنجاح',
      data: { supplierId: result.insertId }
    });

  } catch (error) {
    console.error('خطأ في إنشاء المورد:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على طلبات الشراء
router.get('/requests', async (req, res) => {
  try {
    const { status, department, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause += 'WHERE pr.status = ?';
      params.push(status);
    }

    if (department) {
      whereClause += whereClause ? ' AND pr.department = ?' : 'WHERE pr.department = ?';
      params.push(department);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND pr.request_date >= ?' : 'WHERE pr.request_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND pr.request_date <= ?' : 'WHERE pr.request_date <= ?';
      params.push(end_date);
    }

    const requests = await executeQuery(`
      SELECT 
        pr.*,
        requester.full_name as requested_by_name,
        approver.full_name as approved_by_name
      FROM purchase_requests pr
      LEFT JOIN users requester ON pr.requested_by = requester.id
      LEFT JOIN users approver ON pr.approved_by = approver.id
      ${whereClause}
      ORDER BY pr.request_date DESC, pr.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // الحصول على تفاصيل كل طلب شراء
    for (let request of requests) {
      const details = await executeQuery(`
        SELECT prd.*, i.item_name, i.item_code, i.unit_of_measure
        FROM purchase_request_details prd
        JOIN items i ON prd.item_id = i.id
        WHERE prd.purchase_request_id = ?
      `, [request.id]);
      request.details = details;
    }

    // عدد طلبات الشراء الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM purchase_requests pr ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        requests,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على طلبات الشراء:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء طلب شراء جديد
router.post('/requests', auditLog('INSERT', 'purchase_requests'), async (req, res) => {
  try {
    const { 
      department, 
      priority, 
      items_details, 
      notes 
    } = req.body;

    if (!department || !items_details || !items_details.length) {
      return res.status(400).json({
        success: false,
        message: 'القسم وتفاصيل الأصناف مطلوبة'
      });
    }

    // إنشاء رقم طلب الشراء
    const currentYear = new Date().getFullYear();
    const requestCount = await executeQuery(
      'SELECT COUNT(*) as count FROM purchase_requests WHERE YEAR(request_date) = ?',
      [currentYear]
    );
    
    const requestNumber = `PR-${currentYear}-${String(requestCount[0].count + 1).padStart(4, '0')}`;

    // حساب إجمالي المبلغ المقدر
    const totalAmount = items_details.reduce((sum, item) => 
      sum + ((item.estimated_unit_cost || 0) * item.quantity_requested), 0
    );

    // إنشاء طلب الشراء
    const requestResult = await executeQuery(
      `INSERT INTO purchase_requests 
       (request_number, request_date, department, total_amount, priority, notes, requested_by) 
       VALUES (?, CURDATE(), ?, ?, ?, ?, ?)`,
      [requestNumber, department, totalAmount, priority || 'medium', notes, req.user.id]
    );

    const requestId = requestResult.insertId;

    // إضافة تفاصيل الأصناف
    for (const item of items_details) {
      await executeQuery(
        `INSERT INTO purchase_request_details 
         (purchase_request_id, item_id, quantity_requested, estimated_unit_cost, estimated_total_cost, specifications) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          requestId,
          item.item_id,
          item.quantity_requested,
          item.estimated_unit_cost || 0,
          (item.estimated_unit_cost || 0) * item.quantity_requested,
          item.specifications
        ]
      );
    }

    req.recordId = requestId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء طلب الشراء بنجاح',
      data: { 
        requestId,
        requestNumber 
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء طلب الشراء:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث حالة طلب الشراء
router.put('/requests/:id/status', auditLog('UPDATE', 'purchase_requests'), async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'حالة الطلب مطلوبة'
      });
    }

    // الحصول على الطلب الحالي
    const requests = await executeQuery(
      'SELECT * FROM purchase_requests WHERE id = ?',
      [id]
    );

    if (!requests.length) {
      return res.status(404).json({
        success: false,
        message: 'طلب الشراء غير موجود'
      });
    }

    let updateFields = ['status = ?', 'notes = ?'];
    let updateValues = [status, notes];

    // إضافة معتمد الطلب عند الموافقة
    if (status === 'approved') {
      updateFields.push('approved_by = ?');
      updateValues.push(req.user.id);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await executeQuery(
      `UPDATE purchase_requests SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    req.recordId = id;

    res.json({
      success: true,
      message: 'تم تحديث حالة طلب الشراء بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث حالة طلب الشراء:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على أوامر التوريد
router.get('/orders', async (req, res) => {
  try {
    const { status, supplier_id, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause += 'WHERE po.status = ?';
      params.push(status);
    }

    if (supplier_id) {
      whereClause += whereClause ? ' AND po.supplier_id = ?' : 'WHERE po.supplier_id = ?';
      params.push(supplier_id);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND po.order_date >= ?' : 'WHERE po.order_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND po.order_date <= ?' : 'WHERE po.order_date <= ?';
      params.push(end_date);
    }

    const orders = await executeQuery(`
      SELECT 
        po.*,
        s.supplier_name,
        creator.full_name as created_by_name,
        approver.full_name as approved_by_name
      FROM purchase_orders po
      JOIN suppliers s ON po.supplier_id = s.id
      LEFT JOIN users creator ON po.created_by = creator.id
      LEFT JOIN users approver ON po.approved_by = approver.id
      ${whereClause}
      ORDER BY po.order_date DESC, po.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد أوامر التوريد الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM purchase_orders po ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على أوامر التوريد:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
