const express = require('express');
const { executeQuery, executeTransaction } = require('../config/database');
const { authenticateToken, authorizeDepartment, auditLog } = require('../middleware/auth');

const router = express.Router();

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);
router.use(authorizeDepartment('paper_warehouse', 'audit'));

// الحصول على جميع مخازن الورق
router.get('/warehouses', async (req, res) => {
  try {
    const { active_only = 'true' } = req.query;
    
    let whereClause = '';
    if (active_only === 'true') {
      whereClause = 'WHERE pw.is_active = TRUE';
    }

    const warehouses = await executeQuery(`
      SELECT pw.*, u.full_name as keeper_name
      FROM paper_warehouses pw
      LEFT JOIN users u ON pw.keeper_id = u.id
      ${whereClause}
      ORDER BY pw.warehouse_code
    `);

    res.json({
      success: true,
      data: { warehouses }
    });

  } catch (error) {
    console.error('خطأ في الحصول على مخازن الورق:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على أنواع الورق
router.get('/paper-types', async (req, res) => {
  try {
    const { active_only = 'true' } = req.query;
    
    let whereClause = '';
    if (active_only === 'true') {
      whereClause = 'WHERE is_active = TRUE';
    }

    const paperTypes = await executeQuery(`
      SELECT * FROM paper_types 
      ${whereClause}
      ORDER BY paper_code
    `);

    res.json({
      success: true,
      data: { paperTypes }
    });

  } catch (error) {
    console.error('خطأ في الحصول على أنواع الورق:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء نوع ورق جديد
router.post('/paper-types', auditLog('INSERT', 'paper_types'), async (req, res) => {
  try {
    const { 
      paper_code, 
      paper_name, 
      weight_gsm, 
      width_cm, 
      length_cm, 
      unit_of_measure 
    } = req.body;

    if (!paper_code || !paper_name || !weight_gsm || !width_cm || !length_cm) {
      return res.status(400).json({
        success: false,
        message: 'جميع بيانات نوع الورق مطلوبة'
      });
    }

    // التحقق من عدم تكرار رقم الورق
    const existingPaper = await executeQuery(
      'SELECT id FROM paper_types WHERE paper_code = ?',
      [paper_code]
    );

    if (existingPaper.length) {
      return res.status(400).json({
        success: false,
        message: 'رقم نوع الورق موجود بالفعل'
      });
    }

    const result = await executeQuery(
      `INSERT INTO paper_types 
       (paper_code, paper_name, weight_gsm, width_cm, length_cm, unit_of_measure) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [paper_code, paper_name, weight_gsm, width_cm, length_cm, unit_of_measure || 'طن']
    );

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء نوع الورق بنجاح',
      data: { paperTypeId: result.insertId }
    });

  } catch (error) {
    console.error('خطأ في إنشاء نوع الورق:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على الإنتاج اليومي
router.get('/daily-production', async (req, res) => {
  try {
    const { start_date, end_date, paper_type_id, warehouse_id, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (start_date) {
      whereClause += 'WHERE dpp.production_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND dpp.production_date <= ?' : 'WHERE dpp.production_date <= ?';
      params.push(end_date);
    }

    if (paper_type_id) {
      whereClause += whereClause ? ' AND dpp.paper_type_id = ?' : 'WHERE dpp.paper_type_id = ?';
      params.push(paper_type_id);
    }

    if (warehouse_id) {
      whereClause += whereClause ? ' AND dpp.warehouse_id = ?' : 'WHERE dpp.warehouse_id = ?';
      params.push(warehouse_id);
    }

    const production = await executeQuery(`
      SELECT 
        dpp.*,
        pt.paper_name,
        pt.paper_code,
        pt.weight_gsm,
        pw.warehouse_name,
        creator.full_name as created_by_name,
        approver.full_name as approved_by_name
      FROM daily_paper_production dpp
      JOIN paper_types pt ON dpp.paper_type_id = pt.id
      JOIN paper_warehouses pw ON dpp.warehouse_id = pw.id
      LEFT JOIN users creator ON dpp.created_by = creator.id
      LEFT JOIN users approver ON dpp.approved_by = approver.id
      ${whereClause}
      ORDER BY dpp.production_date DESC, dpp.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد سجلات الإنتاج الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM daily_paper_production dpp ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        production,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الإنتاج اليومي:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إضافة إنتاج يومي جديد
router.post('/daily-production', auditLog('INSERT', 'daily_paper_production'), async (req, res) => {
  try {
    const { 
      production_date, 
      paper_type_id, 
      warehouse_id, 
      quantity_produced, 
      weight_per_unit, 
      production_committee, 
      notes 
    } = req.body;

    if (!production_date || !paper_type_id || !warehouse_id || !quantity_produced || !weight_per_unit) {
      return res.status(400).json({
        success: false,
        message: 'تاريخ الإنتاج ونوع الورق والمخزن والكمية والوزن مطلوبة'
      });
    }

    // حساب الوزن الإجمالي
    const totalWeight = quantity_produced * weight_per_unit;

    const result = await executeQuery(
      `INSERT INTO daily_paper_production 
       (production_date, paper_type_id, warehouse_id, quantity_produced, weight_per_unit, 
        total_weight, production_committee, notes, created_by) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        production_date, paper_type_id, warehouse_id, quantity_produced, 
        weight_per_unit, totalWeight, production_committee, notes, req.user.id
      ]
    );

    // تحديث أرصدة الورق
    await updatePaperBalance(paper_type_id, warehouse_id, production_date, quantity_produced, 0);

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إضافة الإنتاج اليومي بنجاح',
      data: { 
        productionId: result.insertId,
        totalWeight 
      }
    });

  } catch (error) {
    console.error('خطأ في إضافة الإنتاج اليومي:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على شحنات الورق
router.get('/shipments', async (req, res) => {
  try {
    const { status, customer_name, start_date, end_date, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause += 'WHERE ps.status = ?';
      params.push(status);
    }

    if (customer_name) {
      whereClause += whereClause ? ' AND ps.customer_name LIKE ?' : 'WHERE ps.customer_name LIKE ?';
      params.push(`%${customer_name}%`);
    }

    if (start_date) {
      whereClause += whereClause ? ' AND ps.shipment_date >= ?' : 'WHERE ps.shipment_date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      whereClause += whereClause ? ' AND ps.shipment_date <= ?' : 'WHERE ps.shipment_date <= ?';
      params.push(end_date);
    }

    const shipments = await executeQuery(`
      SELECT 
        ps.*,
        pt.paper_name,
        pt.paper_code,
        pt.weight_gsm,
        pw.warehouse_name,
        creator.full_name as created_by_name,
        approver.full_name as approved_by_name
      FROM paper_shipments ps
      JOIN paper_types pt ON ps.paper_type_id = pt.id
      JOIN paper_warehouses pw ON ps.warehouse_id = pw.id
      LEFT JOIN users creator ON ps.created_by = creator.id
      LEFT JOIN users approver ON ps.approved_by = approver.id
      ${whereClause}
      ORDER BY ps.shipment_date DESC, ps.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), parseInt(offset)]);

    // عدد الشحنات الإجمالي
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total FROM paper_shipments ps ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        shipments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult[0].total,
          pages: Math.ceil(totalResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الشحنات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء شحنة جديدة
router.post('/shipments', auditLog('INSERT', 'paper_shipments'), async (req, res) => {
  try {
    const { 
      customer_name, 
      paper_type_id, 
      warehouse_id, 
      truck_empty_weight, 
      truck_loaded_weight, 
      net_weight, 
      unit_price, 
      exit_permit_number, 
      weighing_slip_number 
    } = req.body;

    if (!customer_name || !paper_type_id || !warehouse_id || !net_weight) {
      return res.status(400).json({
        success: false,
        message: 'اسم العميل ونوع الورق والمخزن والوزن الصافي مطلوبة'
      });
    }

    // التحقق من توفر الكمية في المخزن
    const balance = await getPaperBalance(paper_type_id, warehouse_id);
    if (balance < net_weight) {
      return res.status(400).json({
        success: false,
        message: 'الكمية المطلوبة غير متوفرة في المخزن'
      });
    }

    // إنشاء رقم الشحنة
    const currentYear = new Date().getFullYear();
    const shipmentCount = await executeQuery(
      'SELECT COUNT(*) as count FROM paper_shipments WHERE YEAR(shipment_date) = ?',
      [currentYear]
    );
    
    const shipmentNumber = `SH-${currentYear}-${String(shipmentCount[0].count + 1).padStart(4, '0')}`;

    // حساب إجمالي المبلغ
    const totalAmount = unit_price ? net_weight * unit_price : null;

    const result = await executeQuery(
      `INSERT INTO paper_shipments 
       (shipment_number, shipment_date, customer_name, paper_type_id, warehouse_id, 
        truck_empty_weight, truck_loaded_weight, net_weight, unit_price, total_amount, 
        exit_permit_number, weighing_slip_number, created_by) 
       VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        shipmentNumber, customer_name, paper_type_id, warehouse_id,
        truck_empty_weight, truck_loaded_weight, net_weight, unit_price, totalAmount,
        exit_permit_number, weighing_slip_number, req.user.id
      ]
    );

    // تحديث أرصدة الورق
    await updatePaperBalance(paper_type_id, warehouse_id, new Date().toISOString().split('T')[0], 0, net_weight);

    req.recordId = result.insertId;

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الشحنة بنجاح',
      data: { 
        shipmentId: result.insertId,
        shipmentNumber,
        totalAmount 
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء الشحنة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// دالة مساعدة لتحديث أرصدة الورق
async function updatePaperBalance(paperTypeId, warehouseId, date, productionIn, shipmentsOut) {
  // الحصول على آخر رصيد
  const lastBalance = await executeQuery(`
    SELECT * FROM paper_balances 
    WHERE paper_type_id = ? AND warehouse_id = ? 
    ORDER BY balance_date DESC 
    LIMIT 1
  `, [paperTypeId, warehouseId]);

  const openingBalance = lastBalance.length ? lastBalance[0].closing_balance : 0;
  const closingBalance = openingBalance + productionIn - shipmentsOut;

  // إدراج أو تحديث الرصيد
  await executeQuery(`
    INSERT INTO paper_balances 
    (paper_type_id, warehouse_id, balance_date, opening_balance, production_in, shipments_out, closing_balance) 
    VALUES (?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
    production_in = production_in + VALUES(production_in),
    shipments_out = shipments_out + VALUES(shipments_out),
    closing_balance = opening_balance + production_in - shipments_out
  `, [paperTypeId, warehouseId, date, openingBalance, productionIn, shipmentsOut, closingBalance]);
}

// دالة مساعدة للحصول على رصيد الورق
async function getPaperBalance(paperTypeId, warehouseId) {
  const balance = await executeQuery(`
    SELECT closing_balance FROM paper_balances 
    WHERE paper_type_id = ? AND warehouse_id = ? 
    ORDER BY balance_date DESC 
    LIMIT 1
  `, [paperTypeId, warehouseId]);

  return balance.length ? balance[0].closing_balance : 0;
}

module.exports = router;
