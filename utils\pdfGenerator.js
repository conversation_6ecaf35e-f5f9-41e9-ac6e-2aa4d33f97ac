const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

class PDFGenerator {
  constructor() {
    this.doc = null;
    this.currentY = 0;
    this.pageMargin = 50;
    this.lineHeight = 20;
  }

  // إنشاء مستند PDF جديد
  createDocument() {
    this.doc = new PDFDocument({
      size: 'A4',
      margin: this.pageMargin,
      info: {
        Title: 'تقرير نظام المحاسبة',
        Author: 'نظام المحاسبة والمراجعة الداخلية',
        Subject: 'تقرير محاسبي',
        Creator: 'Accounting System'
      }
    });

    this.currentY = this.pageMargin;
    return this.doc;
  }

  // إضافة خط عربي (يحتاج ملف خط)
  addArabicFont() {
    try {
      // يمكن إضافة خط عربي هنا
      // this.doc.font('path/to/arabic-font.ttf');
    } catch (error) {
      console.log('تحذير: لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي');
    }
  }

  // إضافة عنوان رئيسي
  addTitle(title, subtitle = '') {
    this.doc.fontSize(20)
           .font('Helvetica-Bold')
           .text(title, this.pageMargin, this.currentY, { align: 'center' });
    
    this.currentY += 30;

    if (subtitle) {
      this.doc.fontSize(14)
             .font('Helvetica')
             .text(subtitle, this.pageMargin, this.currentY, { align: 'center' });
      this.currentY += 25;
    }

    this.addLine();
    return this;
  }

  // إضافة خط فاصل
  addLine() {
    this.doc.moveTo(this.pageMargin, this.currentY)
           .lineTo(this.doc.page.width - this.pageMargin, this.currentY)
           .stroke();
    this.currentY += 15;
    return this;
  }

  // إضافة نص عادي
  addText(text, options = {}) {
    const defaultOptions = {
      fontSize: 12,
      font: 'Helvetica',
      align: 'right'
    };

    const finalOptions = { ...defaultOptions, ...options };

    this.doc.fontSize(finalOptions.fontSize)
           .font(finalOptions.font)
           .text(text, this.pageMargin, this.currentY, {
             align: finalOptions.align,
             width: this.doc.page.width - (this.pageMargin * 2)
           });

    this.currentY += this.lineHeight;
    return this;
  }

  // إضافة جدول
  addTable(headers, data, options = {}) {
    const defaultOptions = {
      headerBg: '#f0f0f0',
      cellPadding: 8,
      fontSize: 10,
      headerFontSize: 11
    };

    const finalOptions = { ...defaultOptions, ...options };
    const tableWidth = this.doc.page.width - (this.pageMargin * 2);
    const colWidth = tableWidth / headers.length;

    // رسم رأس الجدول
    this.doc.rect(this.pageMargin, this.currentY, tableWidth, 25)
           .fillAndStroke(finalOptions.headerBg, '#000000');

    // كتابة عناوين الأعمدة
    headers.forEach((header, index) => {
      this.doc.fillColor('#000000')
             .fontSize(finalOptions.headerFontSize)
             .font('Helvetica-Bold')
             .text(
               header,
               this.pageMargin + (index * colWidth) + finalOptions.cellPadding,
               this.currentY + finalOptions.cellPadding,
               { width: colWidth - (finalOptions.cellPadding * 2), align: 'center' }
             );
    });

    this.currentY += 25;

    // رسم صفوف البيانات
    data.forEach((row, rowIndex) => {
      const rowY = this.currentY;

      // رسم خلفية الصف (متناوبة)
      if (rowIndex % 2 === 0) {
        this.doc.rect(this.pageMargin, rowY, tableWidth, 20)
               .fillAndStroke('#f9f9f9', '#cccccc');
      } else {
        this.doc.rect(this.pageMargin, rowY, tableWidth, 20)
               .stroke('#cccccc');
      }

      // كتابة بيانات الصف
      row.forEach((cell, cellIndex) => {
        this.doc.fillColor('#000000')
               .fontSize(finalOptions.fontSize)
               .font('Helvetica')
               .text(
                 String(cell || ''),
                 this.pageMargin + (cellIndex * colWidth) + finalOptions.cellPadding,
                 rowY + finalOptions.cellPadding,
                 { width: colWidth - (finalOptions.cellPadding * 2), align: 'center' }
               );
      });

      this.currentY += 20;

      // التحقق من الحاجة لصفحة جديدة
      if (this.currentY > this.doc.page.height - 100) {
        this.addPage();
      }
    });

    this.currentY += 10;
    return this;
  }

  // إضافة صفحة جديدة
  addPage() {
    this.doc.addPage();
    this.currentY = this.pageMargin;
    return this;
  }

  // إضافة تاريخ ووقت التقرير
  addDateTime() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('ar-EG');
    const timeStr = now.toLocaleTimeString('ar-EG');
    
    this.addText(`تاريخ التقرير: ${dateStr}`, { align: 'left' });
    this.addText(`وقت التقرير: ${timeStr}`, { align: 'left' });
    this.currentY += 10;
    return this;
  }

  // إضافة رقم الصفحة
  addPageNumbers() {
    const pages = this.doc.bufferedPageRange();
    for (let i = 0; i < pages.count; i++) {
      this.doc.switchToPage(i);
      this.doc.fontSize(10)
             .text(
               `صفحة ${i + 1} من ${pages.count}`,
               this.pageMargin,
               this.doc.page.height - 30,
               { align: 'center' }
             );
    }
    return this;
  }

  // حفظ PDF
  save(filename) {
    return new Promise((resolve, reject) => {
      try {
        const stream = fs.createWriteStream(filename);
        this.doc.pipe(stream);
        this.doc.end();

        stream.on('finish', () => {
          resolve(filename);
        });

        stream.on('error', (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // إنشاء buffer للإرسال عبر HTTP
  getBuffer() {
    return new Promise((resolve, reject) => {
      try {
        const buffers = [];
        this.doc.on('data', buffers.push.bind(buffers));
        this.doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });
        this.doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }
}

// دوال مساعدة لإنشاء تقارير محددة

// تقرير ميزان المراجعة
async function generateTrialBalanceReport(data, options = {}) {
  const generator = new PDFGenerator();
  generator.createDocument();
  generator.addArabicFont();

  // العنوان
  generator.addTitle('ميزان المراجعة', `من ${options.startDate || 'البداية'} إلى ${options.endDate || 'اليوم'}`);
  
  // التاريخ والوقت
  generator.addDateTime();

  // الجدول
  const headers = ['رقم الحساب', 'اسم الحساب', 'نوع الحساب', 'مدين', 'دائن', 'الرصيد'];
  const tableData = data.map(account => [
    account.account_code,
    account.account_name,
    account.account_type,
    account.total_debit.toFixed(2),
    account.total_credit.toFixed(2),
    account.balance.toFixed(2)
  ]);

  generator.addTable(headers, tableData);

  // الإجماليات
  const totalDebits = data.reduce((sum, acc) => sum + acc.total_debit, 0);
  const totalCredits = data.reduce((sum, acc) => sum + acc.total_credit, 0);

  generator.addLine();
  generator.addText(`إجمالي المدين: ${totalDebits.toFixed(2)}`, { font: 'Helvetica-Bold' });
  generator.addText(`إجمالي الدائن: ${totalCredits.toFixed(2)}`, { font: 'Helvetica-Bold' });

  // أرقام الصفحات
  generator.addPageNumbers();

  return generator.getBuffer();
}

// تقرير أرصدة المخازن
async function generateInventoryReport(data, options = {}) {
  const generator = new PDFGenerator();
  generator.createDocument();
  generator.addArabicFont();

  generator.addTitle('تقرير أرصدة المخازن', options.warehouseName || 'جميع المخازن');
  generator.addDateTime();

  const headers = ['رقم الصنف', 'اسم الصنف', 'المخزن', 'الكمية', 'القيمة', 'حالة المخزون'];
  const tableData = data.map(item => [
    item.item_code,
    item.item_name,
    item.warehouse_name,
    item.current_quantity.toFixed(3),
    item.current_value.toFixed(2),
    item.stock_status
  ]);

  generator.addTable(headers, tableData);

  // إحصائيات
  const totalValue = data.reduce((sum, item) => sum + item.current_value, 0);
  const lowStockItems = data.filter(item => item.stock_status === 'نقص في المخزون').length;

  generator.addLine();
  generator.addText(`إجمالي قيمة المخزون: ${totalValue.toFixed(2)}`, { font: 'Helvetica-Bold' });
  generator.addText(`عدد الأصناف ناقصة المخزون: ${lowStockItems}`, { font: 'Helvetica-Bold' });

  generator.addPageNumbers();
  return generator.getBuffer();
}

module.exports = {
  PDFGenerator,
  generateTrialBalanceReport,
  generateInventoryReport
};
