<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>نظام المحاسبة - اختبار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #81C784);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            margin: 10px;
            display: inline-block;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        .form-group {
            margin: 20px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #ffd700;
            font-weight: bold;
            font-size: 16px;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        .demo-btn {
            background: rgba(255,215,0,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            cursor: pointer;
            border: 2px solid rgba(255,215,0,0.3);
            text-align: center;
            font-size: 16px;
        }
        .demo-btn:hover {
            background: rgba(255,215,0,0.3);
        }
        .section {
            display: none;
            margin-top: 30px;
        }
        .section.active {
            display: block;
        }
        .tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        .tab:hover, .tab.active {
            background: rgba(255,215,0,0.3);
        }
        .card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 2px solid #f44336;
            color: #f44336;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            border-top: 2px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شاشة تسجيل الدخول -->
        <div id="loginScreen">
            <div class="header">
                <div class="logo">AG</div>
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
            </div>
            
            <div class="card">
                <h3>🔐 تسجيل الدخول</h3>
                <div id="message"></div>
                
                <div class="form-group">
                    <label>👤 اسم المستخدم:</label>
                    <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>🔒 كلمة المرور:</label>
                    <input type="password" id="password" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()" style="width: 100%;">🚀 دخول النظام</button>
                
                <div style="margin-top: 30px;">
                    <h4>📋 بيانات تجريبية للاختبار:</h4>
                    <div class="demo-btn" onclick="fillLogin('admin', 'admin123')">
                        👨‍💼 <strong>مدير النظام:</strong> admin / admin123
                    </div>
                    <div class="demo-btn" onclick="fillLogin('accountant', 'acc123')">
                        👨‍💰 <strong>محاسب:</strong> accountant / acc123
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <div class="logo" style="width: 50px; height: 50px; font-size: 16px;">AG</div>
                <div>
                    <strong style="color: #ffd700;">شركة ايه جي تكنولوجي سيستميز</strong><br>
                    <span style="font-size: 14px;">AG Technology Systems</span><br>
                    <span style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</span>
                </div>
            </div>
        </div>

        <!-- النظام الرئيسي -->
        <div id="mainSystem" class="section">
            <div class="header">
                <h1>🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</h1>
                <h2>🎉 نظام المحاسبة والمراجعة الداخلية</h2>
                <p>مرحباً <span id="currentUser" style="color: #ffd700;">المستخدم</span> | 
                   <button class="btn btn-secondary" onclick="logout()">🚪 خروج</button></p>
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('dashboard')">🏠 الرئيسية</button>
                <button class="tab" onclick="showTab('operations')">⚡ العمليات</button>
                <button class="tab" onclick="showTab('reports')">📈 التقارير</button>
                <button class="tab" onclick="showTab('inventory')">🏪 المخازن</button>
            </div>

            <!-- لوحة التحكم -->
            <div id="dashboard" class="section active">
                <h2>📊 لوحة التحكم الرئيسية</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;">
                    <div style="background: rgba(76,175,80,0.2); padding: 25px; border-radius: 15px; text-align: center; border: 2px solid #4CAF50;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #4CAF50;">4</div>
                        <div style="font-size: 16px;">المستخدمين</div>
                    </div>
                    <div style="background: rgba(33,150,243,0.2); padding: 25px; border-radius: 15px; text-align: center; border: 2px solid #2196F3;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #2196F3;">2.5M</div>
                        <div style="font-size: 16px;">المبيعات (جنيه)</div>
                    </div>
                    <div style="background: rgba(255,152,0,0.2); padding: 25px; border-radius: 15px; text-align: center; border: 2px solid #FF9800;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #FF9800;">150</div>
                        <div style="font-size: 16px;">الأصناف</div>
                    </div>
                    <div style="background: rgba(156,39,176,0.2); padding: 25px; border-radius: 15px; text-align: center; border: 2px solid #9C27B0;">
                        <div style="font-size: 2.5em; font-weight: bold; color: #9C27B0;">85</div>
                        <div style="font-size: 16px;">العملاء</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="card">
                        <h3>📄 فاتورة مبيعات</h3>
                        <p>إنشاء فاتورة مبيعات جديدة مع جميع التفاصيل</p>
                        <button class="btn" onclick="testInvoice()">📄 اختبار الفاتورة</button>
                        <button class="btn btn-secondary" onclick="showTab('operations')">⚡ العمليات</button>
                    </div>
                    <div class="card">
                        <h3>📦 فاتورة مشتريات</h3>
                        <p>تسجيل فاتورة مشتريات جديدة</p>
                        <button class="btn" onclick="testPurchase()">📦 اختبار المشتريات</button>
                        <button class="btn btn-secondary" onclick="showTab('operations')">⚡ العمليات</button>
                    </div>
                    <div class="card">
                        <h3>📈 التقارير المالية</h3>
                        <p>عرض وطباعة جميع التقارير والقوائم المالية</p>
                        <button class="btn" onclick="testReport()">📈 اختبار التقارير</button>
                        <button class="btn btn-secondary" onclick="showTab('reports')">📈 التقارير</button>
                    </div>
                    <div class="card">
                        <h3>🏪 إدارة المخازن</h3>
                        <p>متابعة المخزون وحركة الأصناف</p>
                        <button class="btn" onclick="testInventory()">🏪 اختبار المخازن</button>
                        <button class="btn btn-secondary" onclick="showTab('inventory')">🏪 المخازن</button>
                    </div>
                </div>
            </div>

            <!-- العمليات اليومية -->
            <div id="operations" class="section">
                <h2>⚡ العمليات اليومية</h2>
                
                <div class="card">
                    <h3>📄 إنشاء فاتورة مبيعات</h3>
                    <div class="form-group">
                        <label>العميل:</label>
                        <select id="customer">
                            <option value="">اختر العميل</option>
                            <option value="شركة الأهرام للطباعة">شركة الأهرام للطباعة</option>
                            <option value="مؤسسة النيل للنشر">مؤسسة النيل للنشر</option>
                            <option value="شركة المستقبل للورق">شركة المستقبل للورق</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>المنتج:</label>
                        <select id="product">
                            <option value="">اختر المنتج</option>
                            <option value="ورق كتابة 80 جرام">ورق كتابة 80 جرام</option>
                            <option value="ورق طباعة 70 جرام">ورق طباعة 70 جرام</option>
                            <option value="ورق صحف 45 جرام">ورق صحف 45 جرام</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الكمية (طن):</label>
                        <input type="number" id="quantity" placeholder="أدخل الكمية" value="100">
                    </div>
                    <div class="form-group">
                        <label>السعر (جنيه/طن):</label>
                        <input type="number" id="price" placeholder="أدخل السعر" value="25000">
                    </div>
                    <button class="btn" onclick="createInvoice()">📄 إنشاء الفاتورة</button>
                    <button class="btn btn-secondary" onclick="printInvoice()">🖨️ طباعة الفاتورة</button>
                </div>
            </div>

            <!-- التقارير -->
            <div id="reports" class="section">
                <h2>📈 التقارير والقوائم المالية</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="card">
                        <h3>⚖️ ميزان المراجعة</h3>
                        <p><strong>إجمالي المدين:</strong> 12,750,000 جنيه</p>
                        <p><strong>إجمالي الدائن:</strong> 12,750,000 جنيه</p>
                        <button class="btn" onclick="showTrialBalance()">⚖️ عرض الميزان</button>
                        <button class="btn btn-secondary" onclick="printTrialBalance()">🖨️ طباعة</button>
                    </div>
                    <div class="card">
                        <h3>📋 قائمة الدخل</h3>
                        <p><strong>إيرادات المبيعات:</strong> 5,500,000 جنيه</p>
                        <p><strong>صافي الربح:</strong> 658,750 جنيه</p>
                        <button class="btn" onclick="showIncomeStatement()">📋 عرض القائمة</button>
                        <button class="btn btn-secondary" onclick="printIncomeStatement()">🖨️ طباعة</button>
                    </div>
                </div>
            </div>

            <!-- المخازن -->
            <div id="inventory" class="section">
                <h2>🏪 إدارة المخازن</h2>
                
                <div class="card">
                    <h3>📊 أرصدة المخزون الحالية</h3>
                    <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <p><strong>• ورق كتابة 80 جرام:</strong> 500 طن - قيمة: 12,500,000 جنيه</p>
                        <p><strong>• ورق طباعة 70 جرام:</strong> 300 طن - قيمة: 6,900,000 جنيه</p>
                        <p><strong>• ورق صحف 45 جرام:</strong> 200 طن - قيمة: 3,600,000 جنيه</p>
                        <p><strong>• لب الورق:</strong> 150 طن - قيمة: 2,250,000 جنيه</p>
                        <hr style="border: 1px solid rgba(255,255,255,0.3); margin: 15px 0;">
                        <p><strong>إجمالي قيمة المخزون:</strong> 25,250,000 جنيه</p>
                    </div>
                    <button class="btn" onclick="showInventoryDetails()">📊 تفاصيل المخزون</button>
                </div>
            </div>

            <!-- فوتر النظام -->
            <div class="footer">
                <div class="logo">AG</div>
                <div>
                    <h3 style="color: #ffd700;">🏢 شركة ايه جي تكنولوجي سيستميز</h3>
                    <p>AG Technology Systems</p>
                    <p style="font-size: 14px;">تصميم وتطوير الأنظمة المحاسبية والإدارية</p>
                    <p style="font-size: 12px;">© 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🔧 بدء تحميل النظام...');

        // بيانات المستخدمين
        const users = {
            'admin': { password: 'admin123', name: 'مدير النظام' },
            'accountant': { password: 'acc123', name: 'محاسب رئيسي' }
        };

        let currentUser = null;

        // دالة ملء بيانات الدخول
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            console.log('✅ تم ملء البيانات:', username);
        }

        // دالة تسجيل الدخول
        function login() {
            console.log('🔐 محاولة تسجيل الدخول...');

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');

            if (!username || !password) {
                messageDiv.innerHTML = '<div class="message error">❌ يرجى إدخال اسم المستخدم وكلمة المرور</div>';
                return;
            }

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                messageDiv.innerHTML = '<div class="message success">✅ تم تسجيل الدخول بنجاح...</div>';

                console.log('✅ تم تسجيل الدخول بنجاح');

                setTimeout(() => {
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('mainSystem').style.display = 'block';
                    document.getElementById('currentUser').textContent = currentUser.name;

                    alert(`🎉 مرحباً ${currentUser.name}!\n\nتم تسجيل الدخول بنجاح إلى نظام المحاسبة\n\n✅ يمكنك الآن:\n• إصدار فواتير المبيعات\n• إدخال المشتريات\n• إدارة المخازن\n• عرض وطباعة التقارير المالية\n\n🏢 شركة ايه جي تكنولوجي سيستميز`);
                }, 1500);

            } else {
                messageDiv.innerHTML = '<div class="message error">❌ اسم المستخدم أو كلمة المرور غير صحيحة</div>';
            }
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                currentUser = null;
                document.getElementById('loginScreen').style.display = 'block';
                document.getElementById('mainSystem').style.display = 'none';
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('message').innerHTML = '';
                console.log('🚪 تم تسجيل الخروج');
            }
        }

        // دالة عرض التبويبات
        function showTab(tabId) {
            console.log('📂 عرض التبويب:', tabId);

            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('#mainSystem .section');
            sections.forEach(section => section.classList.remove('active'));

            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(tabId);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('✅ تم عرض القسم:', tabId);
            }

            // تفعيل التبويب المطلوب
            if (event && event.target) {
                event.target.classList.add('active');
            }
        }

        // دوال الاختبار السريع
        function testInvoice() {
            console.log('📄 اختبار فاتورة المبيعات');
            alert('✅ اختبار فاتورة المبيعات!\n\nالعميل: شركة الأهرام للطباعة\nالمنتج: ورق كتابة 80 جرام\nالكمية: 100 طن\nالسعر: 25,000 جنيه/طن\nالإجمالي قبل الضريبة: 2,500,000 جنيه\nضريبة القيمة المضافة (14%): 350,000 جنيه\nالإجمالي شامل الضريبة: 2,850,000 جنيه\n\n📄 رقم الفاتورة: INV-' + Date.now());
        }

        function testPurchase() {
            console.log('📦 اختبار فاتورة المشتريات');
            alert('✅ اختبار فاتورة المشتريات!\n\nالمورد: شركة المواد الخام المصرية\nالصنف: لب الورق\nالكمية: 50 طن\nالسعر: 15,000 جنيه/طن\nالإجمالي: 750,000 جنيه\n\n📦 تم تسجيل الفاتورة في النظام');
        }

        function testReport() {
            console.log('📈 اختبار التقارير المالية');
            alert('✅ اختبار التقارير المالية!\n\n⚖️ ميزان المراجعة:\nإجمالي المدين: 12,750,000 جنيه\nإجمالي الدائن: 12,750,000 جنيه\n\n📋 قائمة الدخل:\nإيرادات المبيعات: 5,500,000 جنيه\nصافي الربح: 658,750 جنيه\n\n📊 جميع التقارير متاحة للعرض والطباعة');
        }

        function testInventory() {
            console.log('🏪 اختبار إدارة المخازن');
            alert('✅ اختبار إدارة المخازن!\n\n📊 أرصدة المخزون:\n• ورق كتابة 80 جرام: 500 طن\n• ورق طباعة 70 جرام: 300 طن\n• ورق صحف 45 جرام: 200 طن\n• لب الورق: 150 طن\n\nإجمالي قيمة المخزون: 25,250,000 جنيه');
        }

        // دوال العمليات الفعلية
        function createInvoice() {
            console.log('📄 إنشاء فاتورة مبيعات');

            const customer = document.getElementById('customer').value || 'شركة الأهرام للطباعة';
            const product = document.getElementById('product').value || 'ورق كتابة 80 جرام';
            const quantity = document.getElementById('quantity').value || '100';
            const price = document.getElementById('price').value || '25000';

            const total = quantity * price;
            const tax = total * 0.14;
            const grandTotal = total + tax;

            alert(`✅ تم إنشاء فاتورة مبيعات بنجاح!\n\nالعميل: ${customer}\nالمنتج: ${product}\nالكمية: ${quantity} طن\nالسعر: ${price.toLocaleString()} جنيه/طن\nالإجمالي قبل الضريبة: ${total.toLocaleString()} جنيه\nضريبة القيمة المضافة (14%): ${tax.toLocaleString()} جنيه\nالإجمالي شامل الضريبة: ${grandTotal.toLocaleString()} جنيه\n\n📄 رقم الفاتورة: INV-${Date.now()}`);

            // مسح النموذج
            document.getElementById('customer').value = '';
            document.getElementById('product').value = '';
            document.getElementById('quantity').value = '';
            document.getElementById('price').value = '';
        }

        // دوال التقارير
        function showTrialBalance() {
            console.log('⚖️ عرض ميزان المراجعة');
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n⚖️ ميزان المراجعة\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nرقم الحساب | اسم الحساب | مدين | دائن\n─────────────────────────────────────────\n1000 | الأصول الثابتة | 6,000,000 |\n1100 | الأصول المتداولة | 5,000,000 |\n2000 | الخصوم المتداولة | | 1,800,000\n3000 | رأس المال | | 4,000,000\n4000 | إيرادات المبيعات | | 5,500,000\n5000 | تكلفة البضاعة المباعة | 3,850,000 |\n6000 | مصروفات التشغيل | 900,000 |\n─────────────────────────────────────────\nالإجمالي | 15,750,000 | 15,750,000\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        function showIncomeStatement() {
            console.log('📋 عرض قائمة الدخل');
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n📋 قائمة الدخل\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nالإيرادات:\n• إيرادات المبيعات: 5,500,000 جنيه\n• إيرادات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي الإيرادات: 5,600,000 جنيه\n\nالتكاليف والمصروفات:\n• تكلفة البضاعة المباعة: 3,850,000 جنيه\n─────────────────────────────────────\nمجمل الربح: 1,750,000 جنيه\n\nالمصروفات التشغيلية:\n• مصروفات البيع والتوزيع: 450,000 جنيه\n• مصروفات إدارية وعمومية: 350,000 جنيه\n• مصروفات أخرى: 100,000 جنيه\n─────────────────────────────────────\nإجمالي المصروفات التشغيلية: 900,000 جنيه\n\nصافي الربح قبل الضرائب: 850,000 جنيه\n• ضرائب الدخل (22.5%): 191,250 جنيه\n─────────────────────────────────────\nصافي الربح بعد الضرائب: 658,750 جنيه\n\n═══════════════════════════════════════\nهامش الربح الإجمالي: 31.25%\nهامش الربح الصافي: 11.76%\n\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        function showInventoryDetails() {
            console.log('🏪 عرض تفاصيل المخزون');
            alert(`🏭 شركة مصر ادفو للب وورق الكتابة والطباعة\n🏪 تقرير المخزون التفصيلي\n\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\n═══════════════════════════════════════\n\nكود | اسم الصنف | الكمية | الوحدة | السعر | القيمة\n─────────────────────────────────────────\nP001 | ورق كتابة 80 جرام | 500 | طن | 25,000 | 12,500,000\nP002 | ورق طباعة 70 جرام | 300 | طن | 23,000 | 6,900,000\nP003 | ورق صحف 45 جرام | 200 | طن | 18,000 | 3,600,000\nR001 | لب الورق | 150 | طن | 15,000 | 2,250,000\n─────────────────────────────────────────\nالإجمالي | | 1,150 | طن | | 25,250,000\n\n═══════════════════════════════════════\nتصميم وتطوير: شركة ايه جي تكنولوجي سيستميز`);
        }

        // دوال الطباعة
        function printInvoice() {
            console.log('🖨️ طباعة فاتورة المبيعات');
            printDocument('📄 فاتورة مبيعات', `فاتورة مبيعات\n\nرقم الفاتورة: INV-${Date.now()}\nالتاريخ: ${new Date().toLocaleDateString('ar-EG')}\n\nبيانات العميل:\nالعميل: شركة الأهرام للطباعة والنشر\nالعنوان: القاهرة - مصر الجديدة\nالرقم الضريبي: 123456789\n\nتفاصيل الفاتورة:\nالمنتج: ورق كتابة 80 جرام - مقاس A4\nالكمية: 100 طن\nالسعر: 25,000 جنيه/طن\nالإجمالي قبل الضريبة: 2,500,000 جنيه\nضريبة القيمة المضافة (14%): 350,000 جنيه\nالإجمالي شامل الضريبة: 2,850,000 جنيه\n\nشروط السداد: 30 يوم من تاريخ الفاتورة\nطريقة السداد: شيكات مؤجلة`);
        }

        function printTrialBalance() {
            console.log('🖨️ طباعة ميزان المراجعة');
            printDocument('⚖️ ميزان المراجعة', `ميزان المراجعة\n\nرقم الحساب | اسم الحساب | مدين | دائن\n─────────────────────────────────────────\n1000 | الأصول الثابتة | 6,000,000 |\n1100 | الأصول المتداولة | 5,000,000 |\n2000 | الخصوم المتداولة | | 1,800,000\n3000 | رأس المال | | 4,000,000\n4000 | إيرادات المبيعات | | 5,500,000\n5000 | تكلفة البضاعة المباعة | 3,850,000 |\n6000 | مصروفات التشغيل | 900,000 |\n─────────────────────────────────────────\nالإجمالي | 15,750,000 | 15,750,000`);
        }

        function printIncomeStatement() {
            console.log('🖨️ طباعة قائمة الدخل');
            printDocument('📋 قائمة الدخل', `قائمة الدخل\n\nالإيرادات:\n• إيرادات المبيعات: 5,500,000 جنيه\n• إيرادات أخرى: 100,000 جنيه\nإجمالي الإيرادات: 5,600,000 جنيه\n\nالتكاليف والمصروفات:\n• تكلفة البضاعة المباعة: 3,850,000 جنيه\nمجمل الربح: 1,750,000 جنيه\n\nالمصروفات التشغيلية:\n• مصروفات البيع والتوزيع: 450,000 جنيه\n• مصروفات إدارية وعمومية: 350,000 جنيه\n• مصروفات أخرى: 100,000 جنيه\nإجمالي المصروفات التشغيلية: 900,000 جنيه\n\nصافي الربح قبل الضرائب: 850,000 جنيه\n• ضرائب الدخل (22.5%): 191,250 جنيه\nصافي الربح بعد الضرائب: 658,750 جنيه\n\nهامش الربح الإجمالي: 31.25%\nهامش الربح الصافي: 11.76%`);
        }

        function printDocument(title, content) {
            console.log('🖨️ فتح نافذة الطباعة:', title);

            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG');

            const printContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #4CAF50; padding-bottom: 20px; margin-bottom: 30px; }
        .company-logo { width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #4CAF50, #81C784); display: inline-flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; color: white; margin-bottom: 15px; }
        .company-name { font-size: 24px; font-weight: bold; color: #4CAF50; margin: 10px 0; }
        .document-title { font-size: 20px; font-weight: bold; color: #333; margin: 15px 0; }
        .content { margin: 20px 0; white-space: pre-line; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ddd; padding: 10px; background: white; }
        @media print { .no-print { display: none !important; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-logo">AG</div>
        <div class="company-name">🏭 شركة مصر ادفو للب وورق الكتابة والطباعة</div>
        <div class="document-title">${title}</div>
        <div>التاريخ: ${currentDate} | الوقت: ${currentTime}</div>
    </div>
    <div class="content">${content}</div>
    <div class="footer">
        <strong>شركة ايه جي تكنولوجي سيستميز</strong> | AG Technology Systems<br>
        تصميم وتطوير الأنظمة المحاسبية والإدارية | © 2024 جميع الحقوق محفوظة
    </div>
    <script>
        window.onload = function() {
            console.log('🖨️ بدء الطباعة...');
            window.print();
            setTimeout(function() {
                console.log('🖨️ إغلاق نافذة الطباعة...');
                window.close();
            }, 1000);
        }
    </script>
</body>
</html>`;

            printWindow.document.write(printContent);
            printWindow.document.close();
        }

        // تفعيل Enter للدخول
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && document.getElementById('loginScreen').style.display !== 'none') {
                console.log('⌨️ تم الضغط على Enter - تسجيل الدخول');
                login();
            }
        });

        // رسائل في الكونسول
        console.log('🏭 شركة مصر ادفو للب وورق الكتابة والطباعة - نظام المحاسبة والمراجعة الداخلية');
        console.log('🏢 تصميم وتطوير: شركة ايه جي تكنولوجي سيستميز');
        console.log('✅ النظام جاهز للاستخدام!');
        console.log('📋 للاختبار: اضغط على البطاقات التجريبية أو أدخل admin/admin123');

        // اختبار تلقائي
        setTimeout(() => {
            console.log('🔧 اختبار تلقائي: جميع الدوال محملة بنجاح');
            console.log('🎯 النظام يعمل بشكل صحيح ومستعد للاستخدام');
        }, 1000);
    </script>
</body>
</html>
